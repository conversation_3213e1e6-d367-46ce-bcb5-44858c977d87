<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宜承SPU数据分析 - 数据可视化</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- ECharts -->
    <script src="https://cdn.bootcdn.net/ajax/libs/echarts/5.4.3/echarts.min.js"></script>
    <!-- Date range picker -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #06b6d4;
            --accent-color: #8b5cf6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #0f172a;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            --light-color: #ffffff;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --radius-sm: 0.375rem;
            --radius: 0.5rem;
            --radius-md: 0.75rem;
            --radius-lg: 1rem;
            --radius-xl: 1.5rem;
        }

        * {
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, var(--gray-50) 0%, #f0f4f8 100%);
            font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--gray-900);
            line-height: 1.6;
            font-size: 14px;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 50%, var(--secondary-color) 100%);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-md);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.375rem;
            color: white !important;
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            transform: translateY(-1px);
            color: rgba(255, 255, 255, 0.9) !important;
        }

        .navbar-brand i {
            font-size: 1.5rem;
            margin-right: 0.75rem;
        }

        .card {
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: var(--light-color);
            overflow: hidden;
            position: relative;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.3), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-color);
        }

        .card:hover::before {
            opacity: 1;
        }

        .card-header {
            background: linear-gradient(135deg, var(--gray-50), var(--gray-100));
            border-bottom: 1px solid var(--gray-200);
            font-weight: 600;
            padding: 1.25rem 1.5rem;
            font-size: 1rem;
            color: var(--gray-800);
        }

        .card-body {
            padding: 1.5rem;
        }

        .stat-card {
            background: linear-gradient(135deg, 
                rgba(99, 102, 241, 0.08) 0%, 
                rgba(139, 92, 246, 0.08) 50%, 
                rgba(6, 182, 212, 0.08) 100%);
            border: 1px solid rgba(99, 102, 241, 0.2);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, var(--primary-color), var(--accent-color));
        }

        .stat-card:hover {
            background: linear-gradient(135deg, 
                rgba(99, 102, 241, 0.12) 0%, 
                rgba(139, 92, 246, 0.12) 50%, 
                rgba(6, 182, 212, 0.12) 100%);
        }

        .stat-value {
            font-size: 2.25rem;
            font-weight: 800;
            color: var(--primary-color);
            line-height: 1.2;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 0.8125rem;
            color: var(--gray-600);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin: 0;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            border: 2px solid var(--gray-200);
            border-radius: var(--radius-md);
            padding: 0.875rem 1rem 0.875rem 3rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: var(--light-color);
            font-size: 0.9375rem;
            color: var(--gray-900);
        }

        .search-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            outline: none;
            background: var(--light-color);
        }

        .search-input::placeholder {
            color: var(--gray-400);
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
            font-size: 1rem;
            z-index: 2;
        }

        .btn {
            border-radius: var(--radius-md);
            padding: 0.75rem 1.25rem;
            font-weight: 600;
            font-size: 0.9375rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-decoration: none;
            cursor: pointer;
        }

        .btn:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, #7c3aed 100%);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            color: white;
        }

        .btn-primary:active {
            transform: translateY(0);
            box-shadow: var(--shadow);
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn-outline-secondary {
            border: 2px solid var(--gray-300);
            color: var(--gray-700);
            background: var(--light-color);
        }

        .btn-outline-secondary:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
            color: var(--gray-800);
        }

        .btn-outline-light {
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .btn-outline-light:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        .btn-group .btn {
            border-radius: 0;
        }

        .btn-group .btn:first-child {
            border-radius: var(--radius-md) 0 0 var(--radius-md);
        }

        .btn-group .btn:last-child {
            border-radius: 0 var(--radius-md) var(--radius-md) 0;
        }

        .btn-group .btn.active {
            background: var(--primary-color);
            color: white;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .filter-panel {
            background: var(--light-color);
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-xl);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
            position: relative;
        }

        .filter-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--secondary-color));
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
        }

        .form-label {
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
            font-size: 0.9375rem;
        }

        .form-select, .form-control {
            border: 2px solid var(--gray-200);
            border-radius: var(--radius-md);
            padding: 0.875rem 1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: var(--light-color);
            font-size: 0.9375rem;
            color: var(--gray-900);
        }

        .form-select:focus, .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            outline: none;
            background: var(--light-color);
        }

        .form-select:hover, .form-control:hover {
            border-color: var(--gray-300);
        }

        .input-group {
            position: relative;
        }

        .input-group .form-control {
            border-radius: var(--radius-md) 0 0 var(--radius-md);
        }

        .input-group .btn {
            border-radius: 0 var(--radius-md) var(--radius-md) 0;
        }

        .input-group .dropdown .btn {
            border-radius: 0 var(--radius-md) var(--radius-md) 0;
        }

        .table-responsive {
            border-radius: var(--radius-xl);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .table {
            margin-bottom: 0;
            font-size: 0.9375rem;
        }

        .table thead th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 50%, var(--secondary-color) 100%);
            color: white;
            border: none;
            font-weight: 600;
            padding: 1.25rem 1rem;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            font-size: 0.875rem;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table thead th:first-child {
            padding-left: 1.5rem;
        }

        .table thead th:last-child {
            padding-right: 1.5rem;
        }

        .table tbody tr {
            border-bottom: 1px solid var(--gray-200);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: var(--light-color);
        }

        .table tbody tr:hover {
            background: linear-gradient(135deg, 
                rgba(99, 102, 241, 0.03) 0%, 
                rgba(139, 92, 246, 0.03) 50%, 
                rgba(6, 182, 212, 0.03) 100%);
            transform: translateX(4px);
            box-shadow: 4px 0 0 var(--primary-color);
        }

        .table tbody tr:last-child {
            border-bottom: none;
        }

        .table tbody td {
            padding: 1.25rem 1rem;
            vertical-align: middle;
            color: var(--gray-800);
        }

        .table tbody td:first-child {
            padding-left: 1.5rem;
        }

        .table tbody td:last-child {
            padding-right: 1.5rem;
        }

        .badge {
            padding: 0.375rem 0.875rem;
            border-radius: var(--radius);
            font-weight: 600;
            font-size: 0.8125rem;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .badge-success {
            background: linear-gradient(135deg, var(--success-color), #34d399);
            color: white;
        }

        .badge-warning {
            background: linear-gradient(135deg, var(--warning-color), #fbbf24);
            color: white;
        }

        .badge-danger {
            background: linear-gradient(135deg, var(--danger-color), #f87171);
            color: white;
        }

        .pagination {
            justify-content: center;
            margin-top: 2rem;
            gap: 0.25rem;
        }

        .page-item {
            margin: 0;
        }

        .page-link {
            border: 2px solid var(--gray-200);
            border-radius: var(--radius-md);
            padding: 0.75rem 1rem;
            color: var(--gray-700);
            background: var(--light-color);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            text-decoration: none;
        }

        .page-link:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .page-item.active .page-link {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            box-shadow: var(--shadow);
        }

        .page-item.disabled .page-link {
            background: var(--gray-100);
            border-color: var(--gray-200);
            color: var(--gray-400);
            cursor: not-allowed;
        }

        .page-item.disabled .page-link:hover {
            transform: none;
            box-shadow: none;
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 1rem 0;
            border-radius: var(--radius-lg);
            overflow: hidden;
        }

        /* 优化图表样式 */
        .chart-card {
            background: var(--light-color);
            border: 1px solid var(--gray-200);
            backdrop-filter: blur(20px);
            position: relative;
        }

        .chart-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--secondary-color));
            opacity: 0.8;
        }

        .chart-card .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 50%, var(--secondary-color) 100%);
            color: white;
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
            border: none;
            padding: 1.5rem;
        }

        .chart-card .card-header h5 {
            margin: 0;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .top-spu-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            margin: 0.75rem 0;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
            border-radius: var(--radius-md);
            border: 1px solid rgba(99, 102, 241, 0.1);
            border-left: 4px solid var(--primary-color);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .top-spu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.3), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .top-spu-item:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
            transform: translateX(8px);
            box-shadow: var(--shadow-md);
            border-color: var(--primary-color);
        }

        .top-spu-item:hover::before {
            opacity: 1;
        }

        .spu-rank {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            font-weight: 700;
            box-shadow: var(--shadow-sm);
            flex-shrink: 0;
        }

        .spu-info {
            flex: 1;
            margin: 0 1rem;
        }

        .spu-info h6 {
            margin: 0;
            font-size: 0.9375rem;
            color: var(--gray-900);
            font-weight: 600;
            line-height: 1.4;
        }

        .spu-info small {
            color: var(--gray-500);
            font-size: 0.8125rem;
        }

        .spu-value {
            font-weight: 700;
            color: var(--primary-color);
            font-size: 1rem;
            flex-shrink: 0;
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 4rem;
        }

        .spinner-border {
            color: var(--primary-color);
            width: 3rem;
            height: 3rem;
        }

        .alert {
            border: 1px solid transparent;
            border-radius: var(--radius-md);
            padding: 1rem 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .alert::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: currentColor;
            opacity: 0.3;
        }

        .alert-info {
            background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(14, 165, 233, 0.1));
            color: var(--secondary-color);
            border-color: rgba(6, 182, 212, 0.2);
        }

        .dropdown-menu {
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-lg);
            background: var(--light-color);
            backdrop-filter: blur(20px);
            padding: 0.5rem;
        }

        .dropdown-item {
            border-radius: var(--radius-sm);
            padding: 0.75rem 1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            color: var(--gray-700);
            font-weight: 500;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
            color: var(--primary-color);
            transform: translateX(4px);
        }

        .dropdown-divider {
            margin: 0.5rem 0;
            border-color: var(--gray-200);
        }

        /* 导航栏链接样式 */
        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: var(--radius-sm);
            padding: 0.5rem 1rem !important;
        }

        .nav-link:hover {
            color: white !important;
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-1px);
        }

        .nav-link.active {
            color: white !important;
            background: rgba(255, 255, 255, 0.2);
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 表单复选框样式 */
        .form-check-input {
            border: 2px solid var(--gray-300);
            border-radius: var(--radius-sm);
            transition: all 0.3s ease;
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .form-check-input:focus {
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        /* 响应式设计优化 */
        @media (max-width: 768px) {
            :root {
                --radius-xl: 1rem;
            }

            .container-fluid {
                padding: 1rem;
            }
            
            .card-body {
                padding: 1rem;
            }
            
            .stat-value {
                font-size: 1.75rem;
            }

            .filter-panel {
                padding: 1.5rem;
            }

            .chart-container {
                height: 450px;
            }

            .btn {
                padding: 0.625rem 1rem;
                font-size: 0.875rem;
            }

            .table thead th {
                padding: 1rem 0.75rem;
                font-size: 0.8125rem;
            }

            .table tbody td {
                padding: 1rem 0.75rem;
                font-size: 0.875rem;
            }
        }

        @media (max-width: 576px) {
            .stat-value {
                font-size: 1.5rem;
            }

            .filter-panel {
                padding: 1rem;
            }

            .chart-container {
                height: 350px;
            }

            .top-spu-item {
                padding: 0.75rem;
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }

            .spu-info {
                margin: 0;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .slide-up {
            animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes fadeIn {
            from { 
                opacity: 0; 
                transform: translateY(20px);
            }
            to { 
                opacity: 1; 
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--gray-100);
            border-radius: var(--radius);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--gray-300);
            border-radius: var(--radius);
            transition: background 0.3s ease;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--gray-400);
        }

        /* 焦点可见性优化 */
        .btn:focus-visible,
        .form-control:focus-visible,
        .form-select:focus-visible {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* 波纹效果 */
        .btn {
            position: relative;
            overflow: hidden;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s ease-out;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* 聚焦增强效果 */
        .focused {
            transform: translateY(-2px);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .focused .form-control,
        .focused .form-select {
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.15);
        }

        /* 加载状态优化 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .loading-content {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            border: 1px solid var(--gray-200);
        }

        /* 成功/错误提示优化 */
        .toast-container {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 9999;
        }

        .toast {
            border: none;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-lg);
            backdrop-filter: blur(10px);
        }

        .toast-success {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.95), rgba(52, 211, 153, 0.95));
            color: white;
        }

        .toast-error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.95), rgba(248, 113, 113, 0.95));
            color: white;
        }

        /* 年度对比开关样式 */
        .form-switch .form-check-input {
            background-color: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }

        .form-switch .form-check-input:checked {
            background-color: rgba(16, 185, 129, 0.8);
            border-color: rgba(16, 185, 129, 0.8);
            box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.3);
        }

        .form-switch .form-check-input:focus {
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }

        /* 年度对比控制面板样式 */
        #yearCompareControls {
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-md);
            padding: 0.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        #yearCompareControls .form-select {
            background-color: rgba(255, 255, 255, 0.9);
            border-color: rgba(255, 255, 255, 0.3);
            color: var(--gray-800);
        }

        #yearCompareControls .form-select:focus {
            background-color: white;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
        }

        /* 年度对比图表增强样式 */
        .year-compare-active .chart-card {
            border: 2px solid var(--success-color);
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(52, 211, 153, 0.05));
        }

        .year-compare-active .chart-card::before {
            background: linear-gradient(90deg, var(--success-color), #34d399);
        }

        /* 年度对比提示样式 */
        .year-compare-hint {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(52, 211, 153, 0.1));
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: var(--radius-md);
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
            color: var(--success-color);
        }

        .year-compare-hint i {
            color: var(--success-color);
            margin-right: 0.5rem;
        }

        /* 高级表格样式 */
        .table-striped > tbody > tr:nth-of-type(odd) > td {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.02), rgba(139, 92, 246, 0.02));
        }

        /* 数据为空状态 */
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--gray-500);
        }

        .empty-state i {
            font-size: 4rem;
            color: var(--gray-300);
            margin-bottom: 1rem;
        }

        .empty-state h4 {
            color: var(--gray-600);
            margin-bottom: 0.5rem;
        }

        .empty-state p {
            color: var(--gray-500);
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-graph-up me-2"></i>
                SPU数据分析平台
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="shop.html">
                            <i class="bi bi-house me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="#">
                            <i class="bi bi-bar-chart me-1"></i>SPU分析
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <!-- 统计概览 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="text-center mb-5">
                    <h1 class="display-6 fw-bold mb-3" style="background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                        <i class="bi bi-speedometer2 me-3" style="color: var(--primary-color);"></i>
                        数据概览仪表盘
                    </h1>
                    <p class="text-muted fs-5 mb-0">实时监控SPU数据表现，洞察业务趋势</p>
                    <div class="d-inline-block mt-3 px-4 py-2 rounded-pill" style="background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1)); border: 1px solid rgba(99, 102, 241, 0.2);">
                        <small class="text-primary fw-semibold">
                            <i class="bi bi-clock me-1"></i>
                            数据实时更新中
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <div class="stat-value" id="totalRecords">-</div>
                        <div class="stat-label">总记录数</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <div class="stat-value" id="totalStyles">-</div>
                        <div class="stat-label">款式编码数</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <div class="stat-value" id="totalProducts">-</div>
                        <div class="stat-label">商品编码数</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <div class="stat-value" id="totalSales">-</div>
                        <div class="stat-label">总销售额</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="row mb-4">
            <div class="col-12 mb-4">
                <div class="card chart-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up me-2"></i>销售趋势分析
                        </h5>
                        <div class="d-flex align-items-center flex-wrap gap-2">
                            <div class="me-2">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="yearCompareSwitch">
                                    <label class="form-check-label text-white fw-semibold" for="yearCompareSwitch">
                                        年度对比
                                    </label>
                                </div>
                            </div>
                            <div class="me-2" id="yearCompareControls" style="display: none;">
                                <div class="d-flex align-items-center gap-2">
                                    <select class="form-select form-select-sm" id="compareYear1" style="width: 90px;">
                                        <option value="2023">2023年</option>
                                        <option value="2024" selected>2024年</option>
                                        <option value="2025">2025年</option>
                                    </select>
                                    <span class="text-white">vs</span>
                                    <select class="form-select form-select-sm" id="compareYear2" style="width: 90px;">
                                        <option value="2023">2023年</option>
                                        <option value="2024">2024年</option>
                                        <option value="2025" selected>2025年</option>
                                    </select>
                                </div>
                            </div>
                            <div class="me-2">
                                <button class="btn btn-outline-light btn-sm" id="refreshTrendBtn" title="刷新趋势图">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-light active" data-chart-type="line">
                                    <i class="bi bi-graph-up"></i> 折线图
                                </button>
                                <button class="btn btn-outline-light" data-chart-type="bar">
                                    <i class="bi bi-bar-chart"></i> 柱状图
                                </button>
                                <button class="btn btn-outline-light" data-chart-type="area">
                                    <i class="bi bi-area-chart"></i> 面积图
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" style="height: 600px;">
                            <div id="trendChart" style="width: 100%; height: 600px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选面板 -->
        <div class="filter-panel">
            <div class="row">
                <div class="col-12 mb-3">
                    <h5><i class="bi bi-funnel me-2"></i>数据筛选</h5>
                </div>
                <div class="col-lg-4 col-md-6 mb-3">
                    <label class="form-label">日期范围</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="dateRange" placeholder="选择日期范围">
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                快捷选择
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="setQuickDate('2025')">2025年数据</a></li>
                                <li><a class="dropdown-item" href="#" onclick="setQuickDate('2024')">2024年数据</a></li>
                                <li><a class="dropdown-item" href="#" onclick="setQuickDate('2023')">2023年数据</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="setQuickDate('last30')">最近30天</a></li>
                                <li><a class="dropdown-item" href="#" onclick="setQuickDate('last90')">最近90天</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label class="form-label">数据粒度</label>
                    <select class="form-select" id="granularity">
                        <option value="daily" selected>每日数据</option>
                        <option value="weekly">周度汇总</option>
                        <option value="monthly">月度汇总</option>
                    </select>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label class="form-label">排序方式</label>
                    <select class="form-select" id="sortBy">
                        <option value="total_amount" selected>按销售额</option>
                        <option value="total_quantity">按销量</option>
                        <option value="total_profit">按利润</option>
                        <option value="profit_margin">按利润率</option>
                    </select>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label class="form-label">搜索</label>
                    <div class="search-container">
                        <i class="bi bi-search search-icon"></i>
                        <input type="text" class="form-control search-input" id="searchInput" 
                               placeholder="搜索款式编码、商品编码或名称">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <button class="btn btn-primary me-2" onclick="applyFilters()">
                        <i class="bi bi-search me-1"></i>应用筛选
                    </button>
                    <button class="btn btn-outline-primary me-2" onclick="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-1"></i>重置
                    </button>
                    <button class="btn btn-outline-primary" onclick="exportData()">
                        <i class="bi bi-download me-1"></i>导出数据
                    </button>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-table me-2"></i>SPU数据详情
                </h5>
                <div class="d-flex align-items-center gap-3">
                    <div class="d-flex align-items-center">
                        <span class="me-2">数据维度：</span>
                        <div class="btn-group btn-group-sm" role="group">
                            <input type="radio" class="btn-check" name="dataDimension" id="styleCodeDimension" value="style_code" checked>
                            <label class="btn btn-outline-primary" for="styleCodeDimension">
                                <i class="bi bi-grid-3x3-gap me-1"></i>款式编码
                            </label>
                            <input type="radio" class="btn-check" name="dataDimension" id="productCodeDimension" value="product_code">
                            <label class="btn btn-outline-primary" for="productCodeDimension">
                                <i class="bi bi-upc-scan me-1"></i>商品编码
                            </label>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="me-2">每页显示：</span>
                        <select class="form-select form-select-sm" id="pageSize" style="width: auto;">
                            <option value="25" selected>25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="loading-spinner" id="tableLoading">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2 text-muted">数据加载中...</p>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="spuTable">
                        <thead>
                            <tr>
                                <th width="50">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>款式编码</th>
                                <th id="productCodeHeader">商品编码</th>
                                <th>商品名称</th>
                                <th>供应商</th>
                                <th>总销量</th>
                                <th>总销售额</th>
                                <th>同比去年</th>
                                <th>销售天数</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="spuTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <nav aria-label="分页导航">
                    <ul class="pagination mb-0" id="pagination">
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- SPU数据详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 50%, var(--secondary-color) 100%); color: white;">
                    <h5 class="modal-title">
                        <i class="bi bi-graph-up-arrow me-2"></i>SPU数据详情
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" style="padding: 2rem;">
                    <!-- 加载状态 -->
                    <div id="detailLoading" class="text-center py-5" style="display: none;">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="text-muted">正在加载SPU数据详情...</p>
                    </div>
                    
                    <!-- 详情内容 -->
                    <div id="detailContent">
                        <!-- SPU基本信息 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>基本信息</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row" id="basicInfo">
                                            <!-- 基本信息将在这里动态生成 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 销售数据统计 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="bi bi-bar-chart me-2"></i>销售数据统计</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row" id="salesStats">
                                            <!-- 销售统计将在这里动态生成 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 趋势图表 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="bi bi-graph-up me-2"></i>销售趋势</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="detailChart" style="width: 100%; height: 400px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 商品编码列表 -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="bi bi-list-ul me-2"></i>关联商品编码</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-hover" id="productCodesTable">
                                                <thead>
                                                    <tr>
                                                        <th>商品编码</th>
                                                        <th>商品名称</th>
                                                        <th>销量</th>
                                                        <th>销售额</th>
                                                        <th>利润</th>
                                                        <th>利润率</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="productCodesBody">
                                                    <!-- 商品编码数据将在这里动态生成 -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>关闭
                    </button>
                    <button type="button" class="btn btn-primary" onclick="exportSpuDetail()">
                        <i class="bi bi-download me-1"></i>导出详情
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <!-- Date range picker -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    
    <script>
        // 登录检测函数
        function checkLoginStatus() {
            const cookies = document.cookie.split(';');
            const loginAuth = cookies.find(cookie => cookie.trim().startsWith('loginAuth='));
            
            if (!loginAuth || loginAuth.split('=')[1].trim() !== 'true') {
                // 未登录，显示提示并跳转到登录页面
                alert('请先登录才能访问此页面');
                window.location.href = 'login.html';
                return false;
            }
            return true;
        }
        
        // 获取用户信息
        function getUserInfo() {
            const cookies = document.cookie.split(';');
            const usernameCookie = cookies.find(cookie => cookie.trim().startsWith('username='));
            const userRoleCookie = cookies.find(cookie => cookie.trim().startsWith('userRole='));
            
            return {
                username: usernameCookie ? decodeURIComponent(usernameCookie.split('=')[1].trim()) : '用户',
                role: userRoleCookie ? decodeURIComponent(userRoleCookie.split('=')[1].trim()) : 'user'
            };
        }
        
        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            // 检查登录状态
            if (!checkLoginStatus()) {
                return; // 如果未登录，函数会跳转，这里直接返回
            }
            
            // 获取用户信息并更新导航栏
            const userInfo = getUserInfo();
            updateNavbar(userInfo);
            
            // 页面加载完成后的其他初始化工作
            console.log('用户已登录:', userInfo.username, '角色:', userInfo.role);
            
            // 添加页面载入动画
            initPageAnimations();
            
            // 初始化交互增强
            initInteractionEnhancements();
        });
        
        // 初始化页面动画
        function initPageAnimations() {
            // 为统计卡片添加动画
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 150);
            });
            
            // 为其他卡片添加动画
            const cards = document.querySelectorAll('.card:not(.stat-card)');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 600 + index * 100);
            });
        }
        
        // 初始化交互增强
        function initInteractionEnhancements() {
            // 为按钮添加点击波纹效果
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');
                    
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
            
            // 表格行悬停增强
            const tableRows = document.querySelectorAll('.table tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.zIndex = '1';
                });
                
                row.addEventListener('mouseleave', function() {
                    this.style.zIndex = '';
                });
            });
            
            // 输入框聚焦增强
            const inputs = document.querySelectorAll('.form-control, .form-select');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.closest('.form-group, .input-group, .col-lg-3, .col-lg-4, .col-md-6')?.classList.add('focused');
                });
                
                input.addEventListener('blur', function() {
                    this.closest('.form-group, .input-group, .col-lg-3, .col-lg-4, .col-md-6')?.classList.remove('focused');
                });
            });
        }
        
        // 更新导航栏显示用户信息
        function updateNavbar(userInfo) {
            const navbar = document.querySelector('.navbar-nav');
            if (navbar) {
                // 添加用户信息和退出登录链接
                const userNavItem = document.createElement('li');
                userNavItem.className = 'nav-item dropdown';
                userNavItem.innerHTML = `
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>${userInfo.username}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="userprofile.html">
                            <i class="bi bi-person me-2"></i>个人中心
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>退出登录
                        </a></li>
                    </ul>
                `;
                navbar.appendChild(userNavItem);
            }
        }
        
        // 退出登录函数
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                // 删除所有相关的cookie
                document.cookie = 'loginAuth=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                document.cookie = 'userRole=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                document.cookie = 'username=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                document.cookie = 'user=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                document.cookie = 'name=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                document.cookie = 'roleName=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                document.cookie = 'userPermissions=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                
                // 重定向到登录页面
                window.location.href = 'login.html';
            }
        }
        
        // 定期检查登录状态（每5分钟检查一次）
        setInterval(function() {
            if (!checkLoginStatus()) {
                return;
            }
        }, 5 * 60 * 1000); // 5分钟
    </script>
    
    <script src="generateSpuAnalytics.js"></script>
</body>
</html> 