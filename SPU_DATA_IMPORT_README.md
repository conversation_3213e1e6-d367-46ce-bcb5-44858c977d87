# SPU数据导入工具

这是一个用于将销售主题分析Excel文件导入到SQLite数据库的Python工具。

## 📋 功能特性

- ✅ **批量导入**: 支持一次性导入多个Excel文件
- 🔄 **增量更新**: 自动检测并更新已存在的记录
- 🎯 **数据清理**: 自动清理和标准化数据格式
- 📊 **统计信息**: 导入完成后显示详细的数据库统计
- 🚀 **高性能**: 分批处理，支持大文件导入
- 🛡️ **数据完整性**: 重复数据检测和处理

## 📁 文件结构

```
项目根目录/
├── spu_data_importer.py      # 主导入工具
├── spu_import_example.py     # 使用示例
├── check_db_structure.py     # 数据库结构查看工具
├── check_excel_structure.py  # Excel文件结构分析工具
├── static/
│   ├── db/
│   │   └── spu_data.db      # SQLite数据库文件
│   └── spu_day_date/        # Excel文件目录
│       ├── 销售主题分析_多维分析230101-231231.xlsx
│       ├── 销售主题分析_多维分析240101-240131.xlsx
│       └── ...
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install pandas openpyxl sqlite3
```

### 2. 基本使用

#### 导入所有Excel文件
```bash
python spu_data_importer.py
```

#### 导入特定文件
```bash
# 导入2025年1月的文件
python spu_data_importer.py --file "*250101*.xlsx"

# 导入2024年的所有文件
python spu_data_importer.py --file "*24*.xlsx"
```

#### 使用自定义路径
```bash
python spu_data_importer.py --db "custom/path/database.db" --dir "custom/excel/dir"
```

### 3. 程序化使用

```python
from spu_data_importer import SPUDataImporter

# 创建导入器实例
importer = SPUDataImporter()

# 导入所有文件
importer.import_all_files()

# 导入特定文件
importer.import_all_files("*250101*.xlsx")

# 查看统计信息
importer.show_database_stats()
```

## 📊 数据映射

Excel文件的列名会自动映射到数据库字段：

| Excel列名 | 数据库字段 | 类型 |
|-----------|------------|------|
| 日期 | date | TEXT |
| 商品编码 | product_code | TEXT |
| 款式编码 | style_code | TEXT |
| 供应商 | supplier | TEXT |
| 商品名称 | product_name | TEXT |
| 净销售额 | net_sales_amount | REAL |
| 净销量 | net_sales_quantity | REAL |
| ... | ... | ... |

## 🎯 命令行参数

```
usage: spu_data_importer.py [-h] [--file FILE] [--db DB] [--dir DIR]

SPU数据导入工具

options:
  -h, --help   显示帮助信息
  --file FILE  指定要导入的文件模式，例如: *250101*.xlsx
  --db DB      数据库文件路径 (默认: static/db/spu_data.db)
  --dir DIR    Excel文件目录 (默认: static/spu_day_date)
```

## 📈 导入过程

1. **初始化数据库**: 创建表和索引（如果不存在）
2. **文件扫描**: 扫描指定目录下的Excel文件
3. **数据读取**: 逐个读取Excel文件
4. **数据清理**: 标准化日期格式、处理空值、转换数据类型
5. **批量导入**: 分批插入数据，避免内存溢出
6. **重复检测**: 根据(日期,商品编码,款式编码)检测重复记录
7. **统计报告**: 显示导入结果和数据库统计信息

## 🔧 高级功能

### 数据完整性

- **唯一性约束**: 组合键(date, product_code, style_code)确保数据唯一性
- **更新策略**: 遇到重复记录时自动更新现有数据
- **数据验证**: 自动验证必要字段的完整性

### 性能优化

- **分批处理**: 每批1000条记录，避免内存问题
- **索引优化**: 自动创建查询索引提升性能
- **事务管理**: 批量提交减少磁盘I/O

### 错误处理

- **容错机制**: 单条记录错误不影响整体导入
- **详细日志**: 显示处理进度和错误信息
- **统计报告**: 显示成功、更新、跳过的记录数

## 📋 使用示例

### 示例1: 导入新的月度数据
```bash
# 导入2025年6月的新数据
python spu_data_importer.py --file "*250601*.xlsx"
```

### 示例2: 重新导入历史数据
```bash
# 重新导入2024年所有数据
python spu_data_importer.py --file "*24*.xlsx"
```

### 示例3: 查看当前数据状态
```python
from spu_data_importer import SPUDataImporter

importer = SPUDataImporter()
importer.show_database_stats()
```

## 🛠️ 故障排除

### 常见问题

1. **Excel文件读取失败**
   - 确保Excel文件未被其他程序占用
   - 检查文件是否损坏

2. **数据库连接错误**
   - 确保数据库目录存在
   - 检查文件权限

3. **内存不足**
   - 减少batch_size参数
   - 单独处理大文件

### 性能建议

- 定期清理临时文件
- 使用SSD存储提升I/O性能
- 在低峰期进行大批量导入

## 📞 技术支持

如有问题或建议，请联系开发团队。

---
*最后更新: 2025年6月6日* 