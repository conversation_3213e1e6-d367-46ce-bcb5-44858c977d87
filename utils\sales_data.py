import pandas as pd
import os
import csv
from datetime import datetime, timedelta

def read_sales_data(file_path):
    """读取销售数据CSV文件"""
    try:
        # 尝试多种编码
        encodings = ['gbk', 'utf-8', 'gb18030', 'gb2312', 'cp936']
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                print(f"成功使用 {encoding} 编码读取CSV文件: {file_path}")
                return df
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"使用 {encoding} 编码读取CSV文件时发生错误: {str(e)}")
                continue

        print(f"尝试了所有编码但无法读取文件: {file_path}")
        return None
    except Exception as e:
        print(f"读取销售数据失败: {e}")
        return None

def get_promotion_expense_from_file():
    """从账户余额CSV文件中获取推广支出数据

    根据特定要求：
    - 从B列找"推广费日账单"的行
    - 从C列找"正常支出"的行
    - 提取对应的D列金额并求和
    """
    try:
        # 获取当前日期并格式化为YYYYMMDD格式
        today = datetime.now()
        yesterday = (today - timedelta(days=1)).strftime("%Y%m%d")

        # 构建CSV文件路径
        csv_filename = f"{yesterday}账户余额.csv"
        temp_path = os.path.join('static', 'temp', csv_filename)

        # 检查是否有从环境变量指定的文件路径
        # 使用 .get() 安全访问，如果不存在则返回 None
        env_path = os.environ.get('BALANCE_FILE_PATH')
        if env_path:
            temp_path = env_path
            # 不要在这里删除环境变量，调用者应该管理它
            # del os.environ['BALANCE_FILE_PATH']
            print(f"使用了环境变量 BALANCE_FILE_PATH 指定的路径: {temp_path}")

        # 如果指定文件不存在，尝试查找最近的文件
        if not os.path.exists(temp_path):
            # 获取temp目录下所有文件
            temp_dir = os.path.join('static', 'temp')
            if not os.path.exists(temp_dir):
                print(f"目录不存在: {temp_dir}")
                return 0

            files = [f for f in os.listdir(temp_dir) if f.endswith('账户余额.csv')]

            if not files:
                print("未找到账户余额CSV文件")
                return 0

            # 按文件名排序（日期格式YYYYMMDD会自然排序）
            files.sort(reverse=True)
            temp_path = os.path.join(temp_dir, files[0])
            print(f"使用最近的账户余额文件: {files[0]}")

        # 解析CSV文件
        total_expense = 0
        encodings = ['gbk', 'utf-8', 'gb18030', 'gb2312', 'cp936']

        for encoding in encodings:
            try:
                with open(temp_path, 'r', encoding=encoding) as f:
                    reader = csv.reader(f)
                    for row in reader:
                        if row and len(row) >= 4:  # 确保行不为空且至少有4列
                            # B列(索引1)="推广费日账单" 或 C列(索引2)="正常支出"
                            if (len(row) > 1 and row[1] == "推广费日账单") or (len(row) > 2 and row[2] == "正常支出"):
                                try:
                                    # 提取D列(索引3)金额数据
                                    value = float(row[3].replace(",", ""))
                                    total_expense += value
                                except (ValueError, TypeError):
                                    # 如果转换失败，跳过这一项
                                    continue

                print(f"成功使用 {encoding} 编码读取推广支出数据: {temp_path}")
                print(f"总推广支出: {total_expense}")
                return total_expense
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"使用 {encoding} 编码读取推广支出CSV文件时发生错误: {str(e)}")
                continue

        print("所有编码尝试失败，无法读取推广支出数据")
        return 0

    except Exception as e:
        print(f"获取推广支出数据失败: {e}")
        return 0

def calculate_dashboard_data(df):
    """计算仪表盘所需的数据"""
    if df is None or df.empty:
        return None

    # 计算总销售额和订单数
    total_sales = df['成交金额'].sum()
    total_orders = df['成交订单数'].sum()
    total_refund = df['退款金额'].sum()
    total_refund_orders = df['退款单数'].sum()

    # 计算昨日净订单数（成交订单数减去退款订单数）
    net_orders = total_orders - total_refund_orders

    # 计算平均客单价
    total_buyers = df['成交买家数'].sum()
    avg_order_price = total_sales / total_buyers if total_buyers > 0 else 0

    # 计算平均转化率
    avg_conversion = df['成交转化率'].mean() * 100 if '成交转化率' in df.columns else 0

    # 计算活跃店铺数（有成交的店铺）
    active_shops = len(df[df['成交金额'] > 0])

    # 计算退款率
    refund_rate = (total_refund / total_sales * 100) if total_sales > 0 else 0

    # 计算老买家占比
    if '成交老买家占比' in df.columns and '成交买家数' in df.columns:
        total_old_buyers = (df['成交老买家占比'] * df['成交买家数']).sum()
        old_buyer_rate = (total_old_buyers / total_buyers * 100) if total_buyers > 0 else 0
    else:
        old_buyer_rate = 0

    # 从账户余额文件读取推广支出数据
    promotion_expense = get_promotion_expense_from_file()

    # 如果获取失败，使用销售额的一部分作为模拟数据
    if promotion_expense == 0:
        promotion_expense = total_sales * 0.15

    # 计算昨日全店ROI（净销售额/推广费用）
    net_sales = total_sales - total_refund
    roi = net_sales / promotion_expense if promotion_expense > 0 else 0

    # 按店铺销售额排序
    top_shops = df.nlargest(5, '成交金额')[['店铺名', '成交金额', '成交订单数', '成交转化率']].to_dict('records')
    for shop in top_shops:
        shop['name'] = shop['店铺名']
        shop['sales'] = float(shop['成交金额'])
        shop['orders'] = int(shop['成交订单数'])
        shop['conversion'] = float(shop['成交转化率']) * 100
        del shop['店铺名']
        del shop['成交金额']
        del shop['成交订单数']
        del shop['成交转化率']

    # 计算销售趋势（按小时）
    hourly_sales = {}
    if '成交时间' in df.columns:
        df['hour'] = pd.to_datetime(df['成交时间']).dt.hour
        hourly_sales = df.groupby('hour')['成交金额'].sum().to_dict()

    # 格式化小时销售数据
    hourly_data = {
        'hours': sorted(hourly_sales.keys()),
        'values': [hourly_sales[hour] for hour in sorted(hourly_sales.keys())]
    }

    return {
        'overview': {
            'totalSales': {
                'value': total_sales,
                'trend': 0  # 需要与历史数据比较
            },
            'promotionExpense': {
                'value': promotion_expense,
                'trend': 0  # 需要与历史数据比较
            },
            'totalOrders': {
                'value': total_orders,
                'trend': 0
            },
            'netOrders': {
                'value': net_orders,
                'trend': 0
            },
            'roi': {
                'value': roi,
                'trend': 0
            },
            'avgOrderPrice': {
                'value': avg_order_price,
                'trend': 0
            },
            'conversion': {
                'value': avg_conversion,
                'trend': 0
            },
            'refundRate': {
                'value': refund_rate,
                'trend': 0
            },
            'activeShops': {
                'value': active_shops,
                'trend': 0
            }
        },
        'topShops': top_shops,
        'hourlySales': hourly_data,
        'salesTrend': {
            'dates': [],  # 需要历史数据
            'values': []
        }
    }

def calculate_trends(current_data, previous_data):
    """计算当前数据相对于昨日数据的涨跌幅"""
    if not current_data or not previous_data:
        return current_data

    # 获取当前和前一天的概览数据
    current_overview = current_data['overview']
    previous_overview = previous_data['overview']

    # 计算各指标的涨跌幅
    for key in current_overview:
        if key in previous_overview:
            current_value = current_overview[key]['value']
            previous_value = previous_overview[key]['value']

            # 计算百分比变化
            if previous_value != 0:
                trend = round((current_value - previous_value) / previous_value * 100, 1)
            else:
                trend = 0 if current_value == 0 else 100  # 如果前一天为0，当前不为0，则认为增长100%

            current_overview[key]['trend'] = trend

    return current_data

def get_multi_day_sales_data(days=7):
    """
    读取多天的销售数据和推广支出数据，用于构建趋势图

    参数:
        days: 获取最近几天的数据

    返回:
        包含多天销售数据和推广支出数据的字典
    """
    today = datetime.now()
    sales_trend_data = {
        'dates': [],
        'values': []
    }

    # 创建新的推广支出趋势数据对象
    promotion_trend_data = {
        'dates': [],
        'values': []
    }

    temp_dir = os.path.join('static', 'temp')
    if not os.path.exists(temp_dir):
        print(f"目录不存在: {temp_dir}")
        return sales_trend_data

    # 获取所有销售数据文件
    sales_files = []
    for file in os.listdir(temp_dir):
        if file.endswith('销售数据.csv'):
            try:
                # 提取日期部分
                date_str = file.split('销售数据.csv')[0]
                date = datetime.strptime(date_str, '%Y%m%d')
                sales_files.append((date, os.path.join(temp_dir, file)))
            except ValueError:
                continue

    # 获取所有账户余额文件
    balance_files = {}
    for file in os.listdir(temp_dir):
        if file.endswith('账户余额.csv'):
            try:
                # 提取日期部分
                date_str = file.split('账户余额.csv')[0]
                date = datetime.strptime(date_str, '%Y%m%d')
                balance_files[date_str] = os.path.join(temp_dir, file)
            except ValueError:
                continue

    # 按日期排序文件
    sales_files.sort(reverse=True)

    # 获取最近n天的数据
    recent_files = sales_files[:days]

    for date, file_path in recent_files:
        df = read_sales_data(file_path)
        if df is not None:
            # 格式化日期为字符串
            date_str = date.strftime('%m-%d')
            formatted_date_str = date.strftime('%Y%m%d')

            # 计算总销售额
            total_sales = df['成交金额'].sum()

            # 查找对应日期的账户余额文件
            promotion_expense = 0
            if formatted_date_str in balance_files:
                # 从对应日期的账户余额文件中读取推广支出
                balance_file = balance_files[formatted_date_str]
                try:
                    encodings = ['gbk', 'utf-8', 'gb18030', 'gb2312', 'cp936']
                    for encoding in encodings:
                        try:
                            with open(balance_file, 'r', encoding=encoding) as f:
                                reader = csv.reader(f)
                                for row in reader:
                                    if row and len(row) >= 4:
                                        # 提取B列="推广费日账单" 或 C列="正常支出"的数据
                                        if (len(row) > 1 and row[1] == "推广费日账单") or (len(row) > 2 and row[2] == "正常支出"):
                                            try:
                                                value = float(row[3].replace(",", ""))
                                                promotion_expense += value
                                            except (ValueError, TypeError):
                                                continue
                            break
                        except UnicodeDecodeError:
                            continue
                        except Exception as e:
                            print(f"读取{balance_file}推广支出数据失败: {e}")
                            continue
                except Exception as e:
                    print(f"处理账户余额文件失败: {e}")
                    # 如果获取失败，使用销售额的一部分作为模拟数据
                    promotion_expense = total_sales * 0.15
            else:
                # 使用销售额的一部分作为模拟数据
                promotion_expense = total_sales * 0.15

            sales_trend_data['dates'].append(date_str)
            sales_trend_data['values'].append(total_sales)

            promotion_trend_data['dates'].append(date_str)
            promotion_trend_data['values'].append(promotion_expense)

    # 反转列表使其按照日期升序排列
    sales_trend_data['dates'].reverse()
    sales_trend_data['values'].reverse()
    promotion_trend_data['dates'].reverse()
    promotion_trend_data['values'].reverse()

    return {
        'sales': sales_trend_data,
        'promotion': promotion_trend_data
    }