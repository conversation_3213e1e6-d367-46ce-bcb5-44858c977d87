/**
 * ui.js - Common UI components and functions
 */

import { createElementFromHTML } from './utils.js';

// Create a loading indicator
export function createLoadingIndicator(message = '加载中...') {
    return `
        <div class="loading-indicator">
            <div class="spinner"></div>
            <p>${message}</p>
        </div>
    `;
}

// Create an error message
export function createErrorMessage(message = '加载失败，请稍后重试') {
    return `
        <div class="error-message">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
            <p>${message}</p>
        </div>
    `;
}

// Create a no data message
export function createNoDataMessage(message = '暂无数据') {
    return `
        <div class="no-data">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                <path d="M13 2v7h7"></path>
            </svg>
            <p>${message}</p>
        </div>
    `;
}

// Create a pagination component
export function createPagination(currentPage, totalPages, onPageChange) {
    const paginationContainer = document.createElement('div');
    paginationContainer.className = 'pagination';
    
    // Previous button
    const prevButton = document.createElement('button');
    prevButton.className = 'pagination-btn prev';
    prevButton.disabled = currentPage <= 1;
    prevButton.innerHTML = '&laquo; 上一页';
    prevButton.addEventListener('click', () => {
        if (currentPage > 1) {
            onPageChange(currentPage - 1);
        }
    });
    
    // Next button
    const nextButton = document.createElement('button');
    nextButton.className = 'pagination-btn next';
    nextButton.disabled = currentPage >= totalPages;
    nextButton.innerHTML = '下一页 &raquo;';
    nextButton.addEventListener('click', () => {
        if (currentPage < totalPages) {
            onPageChange(currentPage + 1);
        }
    });
    
    // Page info
    const pageInfo = document.createElement('div');
    pageInfo.className = 'pagination-info';
    pageInfo.textContent = `第 ${currentPage} 页 / 共 ${totalPages} 页`;
    
    // Add elements to container
    paginationContainer.appendChild(prevButton);
    paginationContainer.appendChild(pageInfo);
    paginationContainer.appendChild(nextButton);
    
    return paginationContainer;
}

// Create a modal dialog
export function createModal(title, content, options = {}) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    
    const modalContent = document.createElement('div');
    modalContent.className = 'modal-content';
    
    // Modal header
    const modalHeader = document.createElement('div');
    modalHeader.className = 'modal-header';
    
    const modalTitle = document.createElement('h2');
    modalTitle.textContent = title;
    
    const closeButton = document.createElement('button');
    closeButton.className = 'close-btn';
    closeButton.innerHTML = '&times;';
    closeButton.addEventListener('click', () => {
        document.body.removeChild(modal);
        if (options.onClose) {
            options.onClose();
        }
    });
    
    modalHeader.appendChild(modalTitle);
    modalHeader.appendChild(closeButton);
    
    // Modal body
    const modalBody = document.createElement('div');
    modalBody.className = 'modal-body';
    
    if (typeof content === 'string') {
        modalBody.innerHTML = content;
    } else {
        modalBody.appendChild(content);
    }
    
    // Modal footer
    const modalFooter = document.createElement('div');
    modalFooter.className = 'modal-footer';
    
    if (options.buttons) {
        options.buttons.forEach(button => {
            const btn = document.createElement('button');
            btn.className = `modal-btn ${button.class || ''}`;
            btn.textContent = button.text;
            btn.addEventListener('click', () => {
                if (button.action) {
                    button.action();
                }
                if (button.closeModal !== false) {
                    document.body.removeChild(modal);
                }
            });
            modalFooter.appendChild(btn);
        });
    } else {
        // Default OK button
        const okButton = document.createElement('button');
        okButton.className = 'modal-btn primary';
        okButton.textContent = '确定';
        okButton.addEventListener('click', () => {
            document.body.removeChild(modal);
            if (options.onClose) {
                options.onClose();
            }
        });
        modalFooter.appendChild(okButton);
    }
    
    // Assemble modal
    modalContent.appendChild(modalHeader);
    modalContent.appendChild(modalBody);
    modalContent.appendChild(modalFooter);
    modal.appendChild(modalContent);
    
    // Click outside to close
    if (options.closeOnOutsideClick !== false) {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
                if (options.onClose) {
                    options.onClose();
                }
            }
        });
    }
    
    // Add to body
    document.body.appendChild(modal);
    
    // Return the modal element
    return modal;
}

// Create a notification
export function createNotification(message, type = 'info', duration = 3000) {
    // Remove any existing notification
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Add to body
    document.body.appendChild(notification);
    
    // Show with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Hide after duration
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, duration);
    
    return notification;
}

// Create a tab component
export function createTabs(tabsConfig, containerId) {
    const container = document.getElementById(containerId) || document.createElement('div');
    container.className = 'tabs-container';
    
    // Create tabs header
    const tabsHeader = document.createElement('div');
    tabsHeader.className = 'tabs-header';
    
    // Create tabs content
    const tabsContent = document.createElement('div');
    tabsContent.className = 'tabs-content';
    
    // Process each tab
    tabsConfig.forEach((tab, index) => {
        // Create tab button
        const tabButton = document.createElement('button');
        tabButton.className = `tab ${index === 0 ? 'active' : ''}`;
        tabButton.setAttribute('data-tab', tab.id);
        tabButton.textContent = tab.title;
        
        // Create tab content
        const tabContent = document.createElement('div');
        tabContent.className = 'tab-content';
        tabContent.id = `${tab.id}-tab`;
        tabContent.style.display = index === 0 ? 'block' : 'none';
        
        if (typeof tab.content === 'string') {
            tabContent.innerHTML = tab.content;
        } else if (tab.content instanceof HTMLElement) {
            tabContent.appendChild(tab.content);
        }
        
        // Add click event to tab button
        tabButton.addEventListener('click', () => {
            // Remove active class from all tabs
            tabsHeader.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            // Add active class to current tab
            tabButton.classList.add('active');
            
            // Hide all content
            tabsContent.querySelectorAll('.tab-content').forEach(c => {
                c.style.display = 'none';
            });
            // Show current content
            tabContent.style.display = 'block';
            
            // Call onTabChange if provided
            if (tab.onTabChange) {
                tab.onTabChange();
            }
        });
        
        // Add to containers
        tabsHeader.appendChild(tabButton);
        tabsContent.appendChild(tabContent);
    });
    
    // Assemble tabs
    container.appendChild(tabsHeader);
    container.appendChild(tabsContent);
    
    return container;
}

// Create a search input
export function createSearchInput(placeholder = '搜索...', onSearch, debounceTime = 300) {
    const searchContainer = document.createElement('div');
    searchContainer.className = 'search-container';
    
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.className = 'search-input';
    searchInput.placeholder = placeholder;
    
    let timeout;
    searchInput.addEventListener('input', () => {
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            onSearch(searchInput.value.trim());
        }, debounceTime);
    });
    
    const searchIcon = document.createElement('span');
    searchIcon.className = 'search-icon';
    searchIcon.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
        </svg>
    `;
    
    searchContainer.appendChild(searchIcon);
    searchContainer.appendChild(searchInput);
    
    return searchContainer;
}

// Create a date range picker
export function createDateRangePicker(onApply) {
    const container = document.createElement('div');
    container.className = 'date-range-picker';
    
    const startDateInput = document.createElement('input');
    startDateInput.type = 'date';
    startDateInput.className = 'date-input';
    startDateInput.id = 'startDate';
    
    const endDateInput = document.createElement('input');
    endDateInput.type = 'date';
    endDateInput.className = 'date-input';
    endDateInput.id = 'endDate';
    
    const applyButton = document.createElement('button');
    applyButton.className = 'apply-date-btn';
    applyButton.textContent = '应用';
    applyButton.addEventListener('click', () => {
        const startDate = startDateInput.value;
        const endDate = endDateInput.value;
        
        if (startDate && endDate) {
            onApply(startDate, endDate);
        } else {
            createNotification('请选择开始日期和结束日期', 'error');
        }
    });
    
    container.appendChild(document.createTextNode('从: '));
    container.appendChild(startDateInput);
    container.appendChild(document.createTextNode(' 到: '));
    container.appendChild(endDateInput);
    container.appendChild(applyButton);
    
    return container;
}
