#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPU数据导入使用示例
演示如何使用导入工具
"""

from spu_data_importer import SPUDataImporter

def example_import_all():
    """示例：导入所有Excel文件"""
    print("=== 导入所有Excel文件 ===")
    importer = SPUDataImporter()
    importer.import_all_files()

def example_import_specific_file():
    """示例：导入特定文件"""
    print("=== 导入特定文件 ===")
    importer = SPUDataImporter()
    # 导入2025年1月的文件
    importer.import_all_files("*250101*.xlsx")

def example_import_with_custom_paths():
    """示例：使用自定义路径"""
    print("=== 使用自定义路径 ===")
    importer = SPUDataImporter(
        db_path='custom_db/spu_data.db',
        excel_dir='custom_excel_dir'
    )
    importer.import_all_files()

def example_check_stats():
    """示例：查看数据库统计信息"""
    print("=== 查看数据库统计信息 ===")
    importer = SPUDataImporter()
    importer.show_database_stats()

if __name__ == '__main__':
    print("SPU数据导入工具使用示例")
    print("请选择要执行的示例:")
    print("1. 导入所有Excel文件")
    print("2. 导入特定文件 (2025年1月)")
    print("3. 使用自定义路径")
    print("4. 查看数据库统计信息")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    if choice == '1':
        example_import_all()
    elif choice == '2':
        example_import_specific_file()
    elif choice == '3':
        example_import_with_custom_paths()
    elif choice == '4':
        example_check_stats()
    else:
        print("无效选择") 