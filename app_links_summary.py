from flask import Flask, request, jsonify
import pandas as pd
import os
from datetime import datetime, timedelta

app = Flask(__name__)

@app.route('/api/links/summary', methods=['GET'])
def get_links_summary():
    try:
        # 获取查询参数
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        
        # 如果没有提供日期，默认使用昨天的数据
        if not start_date or not end_date:
            yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y%m%d")
            file_path = f'static/goods_data/{yesterday[4:]}goods_data.xlsx'
            
            # 如果昨天的文件不存在，尝试找到最近的文件
            if not os.path.exists(file_path):
                files = [f for f in os.listdir('static/goods_data') if f.endswith('goods_data.xlsx')]
                if not files:
                    return jsonify({"success": False, "message": "未找到商品数据文件"}), 404
                
                # 按文件名排序（日期格式MMDD会自然排序）
                files.sort(reverse=True)
                file_path = os.path.join('static/goods_data', files[0])
        else:
            # 将YYYY-MM-DD格式转换为MMDD格式
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
            file_path = f'static/goods_data/{start_date_obj.strftime("%m%d")}goods_data.xlsx'
            
            if not os.path.exists(file_path):
                return jsonify({"success": False, "message": f"未找到{start_date}的商品数据文件"}), 404
        
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        # 计算摘要数据
        summary = {
            "hotSelling": {
                "count": len(df[df['payOrdrCnt'] > 0]),
                "growth": df['payOrdrCntPpr'].mean() * 100 if 'payOrdrCntPpr' in df.columns else 0,
                "topItem": df.sort_values(by='payOrdrCnt', ascending=False)['goodsName'].iloc[0] if len(df) > 0 else ""
            },
            "fastGrowing": {
                "count": len(df[df['payOrdrCntPpr'] > 0.2]) if 'payOrdrCntPpr' in df.columns else 0,
                "growth": df[df['payOrdrCntPpr'] > 0.2]['payOrdrCntPpr'].mean() * 100 if 'payOrdrCntPpr' in df.columns else 0,
                "topItem": df.sort_values(by='payOrdrCntPpr', ascending=False)['goodsName'].iloc[0] if 'payOrdrCntPpr' in df.columns and len(df) > 0 else ""
            },
            "profitKing": {
                "amount": df['payOrdrAmt'].sum() if 'payOrdrAmt' in df.columns else 0,
                "growth": df['payOrdrAmtPpr'].mean() * 100 if 'payOrdrAmtPpr' in df.columns else 0,
                "topItem": df.sort_values(by='payOrdrAmt', ascending=False)['goodsName'].iloc[0] if 'payOrdrAmt' in df.columns and len(df) > 0 else ""
            },
            "lossBlackHole": {
                "amount": df[df['payOrdrCntPpr'] < -0.2]['payOrdrAmt'].sum() if 'payOrdrCntPpr' in df.columns else 0,
                "growth": df[df['payOrdrCntPpr'] < -0.2]['payOrdrCntPpr'].mean() * 100 if 'payOrdrCntPpr' in df.columns else 0,
                "topItem": df.sort_values(by='payOrdrCntPpr')['goodsName'].iloc[0] if 'payOrdrCntPpr' in df.columns and len(df) > 0 else ""
            }
        }
        
        return jsonify({"success": True, "summary": summary})
    except Exception as e:
        print(f"获取链接摘要数据失败: {e}")
        return jsonify({"success": False, "message": f"系统错误，请稍后重试: {str(e)}"}), 500

if __name__ == '__main__':
    app.run(debug=True)
