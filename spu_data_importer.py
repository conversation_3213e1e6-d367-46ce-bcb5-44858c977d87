#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPU数据导入工具
用于将销售主题分析Excel文件导入到SQLite数据库
"""

import pandas as pd
import sqlite3
import os
import glob
import sys
from datetime import datetime
from pathlib import Path
import re

class SPUDataImporter:
    def __init__(self, db_path='static/db/spu_data.db', excel_dir='static/spu_day_date'):
        self.db_path = db_path
        self.excel_dir = excel_dir
        self.processed_files = set()
        
        # Excel列名到数据库字段的映射
        self.column_mapping = {
            '日期': 'date',
            '商品编码': 'product_code',
            '款式编码': 'style_code',
            '国标码': 'national_code',
            '供应商': 'supplier',
            '供应商款号': 'supplier_style_code',
            '颜色规格': 'color_spec',
            '商品名称': 'product_name',
            '商品简称': 'product_short_name',
            '产品分类': 'product_category',
            '虚拟分类': 'virtual_category',
            '成本价': 'cost_price',
            '品牌': 'brand',
            '其它价格1': 'other_price1',
            '其它价格2': 'other_price2',
            '其它价格3': 'other_price3',
            '其它价格4': 'other_price4',
            '其它价格5': 'other_price5',
            '开发员': 'developer',
            '其它属性2': 'other_attr2',
            '其它属性3': 'other_attr3',
            '其它属性4': 'other_attr4',
            '其它属性5': 'other_attr5',
            '基本售价': 'basic_price',
            '市场吊牌价': 'market_price',
            '销售数量': 'sales_quantity',
            '实发数量': 'actual_quantity',
            '实发金额': 'actual_amount',
            '销售金额': 'sales_amount',
            '销售成本': 'sales_cost',
            '实发成本': 'actual_cost',
            '销售毛利': 'sales_profit',
            '销售毛利率': 'sales_margin_rate',
            '退货数量': 'return_quantity',
            '实退数量': 'actual_return_quantity',
            '退货金额': 'return_amount',
            '实退金额': 'actual_return_amount',
            '退货成本': 'return_cost',
            '实退成本': 'actual_return_cost',
            '退货毛利': 'return_profit',
            '净销量': 'net_sales_quantity',
            '净销售额': 'net_sales_amount',
            '净销售成本': 'net_sales_cost',
            '净销售毛利': 'net_sales_profit',
            '基本金额': 'basic_amount',
            '已付金额': 'paid_amount',
            '优惠金额': 'discount_amount',
            '运费收入': 'shipping_income',
            '运费支出': 'shipping_expense',
            '净毛利率': 'net_margin_rate',
            '境外运费支出': 'overseas_shipping_expense',
            '境外收入总计': 'overseas_total_income',
            '境外支出总计': 'overseas_total_expense'
        }
    
    def get_excel_files(self, file_pattern=None):
        """获取需要处理的Excel文件列表"""
        if file_pattern:
            pattern = os.path.join(self.excel_dir, file_pattern)
        else:
            pattern = os.path.join(self.excel_dir, '销售主题分析_多维分析*.xlsx')
        
        files = glob.glob(pattern)
        files.sort()  # 按文件名排序
        return files
    
    def init_database(self):
        """初始化数据库表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 检查表是否已存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='spu_sales_data';")
        table_exists = cursor.fetchone() is not None
        
        if not table_exists:
            # 创建表
            create_table_sql = """
            CREATE TABLE spu_sales_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date TEXT NOT NULL,
                product_code TEXT NOT NULL,
                style_code TEXT NOT NULL,
                national_code TEXT,
                supplier TEXT,
                supplier_style_code TEXT,
                color_spec TEXT,
                product_name TEXT,
                product_short_name TEXT,
                product_category TEXT,
                virtual_category TEXT,
                cost_price REAL,
                brand TEXT,
                other_price1 REAL,
                other_price2 REAL,
                other_price3 REAL,
                other_price4 REAL,
                other_price5 REAL,
                developer TEXT,
                other_attr2 TEXT,
                other_attr3 TEXT,
                other_attr4 TEXT,
                other_attr5 TEXT,
                basic_price REAL,
                market_price REAL,
                sales_quantity REAL,
                actual_quantity REAL,
                actual_amount REAL,
                sales_amount REAL,
                sales_cost REAL,
                actual_cost REAL,
                sales_profit REAL,
                sales_margin_rate TEXT,
                return_quantity REAL,
                actual_return_quantity REAL,
                return_amount REAL,
                actual_return_amount REAL,
                return_cost REAL,
                actual_return_cost REAL,
                return_profit REAL,
                net_sales_quantity REAL,
                net_sales_amount REAL,
                net_sales_cost REAL,
                net_sales_profit REAL,
                basic_amount REAL,
                paid_amount REAL,
                discount_amount REAL,
                shipping_income REAL,
                shipping_expense REAL,
                net_margin_rate TEXT,
                overseas_shipping_expense REAL,
                overseas_total_income REAL,
                overseas_total_expense REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(date, product_code, style_code)
            );
            """
            
            cursor.execute(create_table_sql)
            print("创建表 spu_sales_data")
        
        # 创建索引以提高查询性能
        index_sqls = [
            "CREATE INDEX IF NOT EXISTS idx_date ON spu_sales_data(date);",
            "CREATE INDEX IF NOT EXISTS idx_product_code ON spu_sales_data(product_code);",
            "CREATE INDEX IF NOT EXISTS idx_style_code ON spu_sales_data(style_code);",
            "CREATE INDEX IF NOT EXISTS idx_supplier ON spu_sales_data(supplier);",
            "CREATE INDEX IF NOT EXISTS idx_brand ON spu_sales_data(brand);",
            "CREATE INDEX IF NOT EXISTS idx_date_product ON spu_sales_data(date, product_code);",
            "CREATE INDEX IF NOT EXISTS idx_date_style ON spu_sales_data(date, style_code);"
        ]
        
        for sql in index_sqls:
            cursor.execute(sql)
        
        conn.commit()
        conn.close()
        print("数据库初始化完成")
    
    def standardize_date(self, date_value):
        """标准化日期格式"""
        if pd.isna(date_value):
            return None
        
        if isinstance(date_value, str):
            # 尝试解析字符串日期
            try:
                return pd.to_datetime(date_value).strftime('%Y-%m-%d')
            except:
                return None
        elif isinstance(date_value, (pd.Timestamp, datetime)):
            return date_value.strftime('%Y-%m-%d')
        else:
            return None
    
    def clean_data(self, df):
        """清理和标准化数据"""
        print(f"清理数据中... ({len(df)} 行)")
        
        # 重命名列
        df_renamed = df.rename(columns=self.column_mapping)
        
        # 标准化日期
        if 'date' in df_renamed.columns:
            df_renamed['date'] = df_renamed['date'].apply(self.standardize_date)
            # 移除无效日期的行
            df_renamed = df_renamed.dropna(subset=['date'])
        
        # 确保必要字段不为空
        required_fields = ['date', 'product_code', 'style_code']
        for field in required_fields:
            if field in df_renamed.columns:
                df_renamed = df_renamed.dropna(subset=[field])
        
        # 转换数值字段
        numeric_fields = [
            'cost_price', 'other_price1', 'other_price2', 'other_price3', 'other_price4', 'other_price5',
            'basic_price', 'market_price', 'sales_quantity', 'actual_quantity', 'actual_amount',
            'sales_amount', 'sales_cost', 'actual_cost', 'sales_profit', 'return_quantity',
            'actual_return_quantity', 'return_amount', 'actual_return_amount', 'return_cost',
            'actual_return_cost', 'return_profit', 'net_sales_quantity', 'net_sales_amount',
            'net_sales_cost', 'net_sales_profit', 'basic_amount', 'paid_amount', 'discount_amount',
            'shipping_income', 'shipping_expense', 'overseas_shipping_expense', 'overseas_total_income',
            'overseas_total_expense'
        ]
        
        for field in numeric_fields:
            if field in df_renamed.columns:
                df_renamed[field] = pd.to_numeric(df_renamed[field], errors='coerce')
        
        # 处理文本字段
        text_fields = [
            'product_code', 'style_code', 'national_code', 'supplier', 'supplier_style_code',
            'color_spec', 'product_name', 'product_short_name', 'product_category', 'virtual_category',
            'brand', 'developer', 'other_attr2', 'other_attr3', 'other_attr4', 'other_attr5',
            'sales_margin_rate', 'net_margin_rate'
        ]
        
        for field in text_fields:
            if field in df_renamed.columns:
                df_renamed[field] = df_renamed[field].astype(str).replace('nan', '').replace('None', '')
        
        print(f"数据清理完成，剩余 {len(df_renamed)} 行有效数据")
        return df_renamed
    
    def import_excel_file(self, file_path, batch_size=1000):
        """导入单个Excel文件"""
        print(f"\n开始导入文件: {os.path.basename(file_path)}")
        
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path, sheet_name=0)
            print(f"读取到 {len(df)} 行数据")
            
            if df.empty:
                print("文件为空，跳过")
                return 0
            
            # 清理数据
            df_clean = self.clean_data(df)
            
            if df_clean.empty:
                print("清理后无有效数据，跳过")
                return 0
            
            # 连接数据库
            conn = sqlite3.connect(self.db_path)
            
            # 分批插入数据
            total_inserted = 0
            total_updated = 0
            total_skipped = 0
            
            for i in range(0, len(df_clean), batch_size):
                batch_df = df_clean.iloc[i:i+batch_size]
                batch_inserted, batch_updated, batch_skipped = self.insert_batch(conn, batch_df)
                total_inserted += batch_inserted
                total_updated += batch_updated
                total_skipped += batch_skipped
                
                if (i + batch_size) % (batch_size * 10) == 0:
                    print(f"已处理 {min(i + batch_size, len(df_clean))}/{len(df_clean)} 行")
            
            conn.close()
            
            print(f"文件导入完成:")
            print(f"新增: {total_inserted} 行")
            print(f"更新: {total_updated} 行")
            print(f"跳过: {total_skipped} 行")
            
            return total_inserted + total_updated
            
        except Exception as e:
            print(f"导入文件失败: {e}")
            return 0
    
    def insert_batch(self, conn, df_batch):
        """批量插入数据"""
        cursor = conn.cursor()
        inserted = 0
        updated = 0
        skipped = 0
        
        for _, row in df_batch.iterrows():
            try:
                # 检查记录是否已存在
                check_sql = """
                SELECT id FROM spu_sales_data 
                WHERE date = ? AND product_code = ? AND style_code = ?
                """
                cursor.execute(check_sql, (row['date'], row['product_code'], row['style_code']))
                existing = cursor.fetchone()
                
                if existing:
                    # 更新现有记录
                    update_sql = """
                    UPDATE spu_sales_data SET
                        national_code = ?, supplier = ?, supplier_style_code = ?, color_spec = ?,
                        product_name = ?, product_short_name = ?, product_category = ?, virtual_category = ?,
                        cost_price = ?, brand = ?, other_price1 = ?, other_price2 = ?, other_price3 = ?,
                        other_price4 = ?, other_price5 = ?, developer = ?, other_attr2 = ?, other_attr3 = ?,
                        other_attr4 = ?, other_attr5 = ?, basic_price = ?, market_price = ?, sales_quantity = ?,
                        actual_quantity = ?, actual_amount = ?, sales_amount = ?, sales_cost = ?, actual_cost = ?,
                        sales_profit = ?, sales_margin_rate = ?, return_quantity = ?, actual_return_quantity = ?,
                        return_amount = ?, actual_return_amount = ?, return_cost = ?, actual_return_cost = ?,
                        return_profit = ?, net_sales_quantity = ?, net_sales_amount = ?, net_sales_cost = ?,
                        net_sales_profit = ?, basic_amount = ?, paid_amount = ?, discount_amount = ?,
                        shipping_income = ?, shipping_expense = ?, net_margin_rate = ?, overseas_shipping_expense = ?,
                        overseas_total_income = ?, overseas_total_expense = ?, created_at = CURRENT_TIMESTAMP
                    WHERE date = ? AND product_code = ? AND style_code = ?
                    """
                    
                    values = self.prepare_row_values(row) + [row['date'], row['product_code'], row['style_code']]
                    cursor.execute(update_sql, values)
                    updated += 1
                else:
                    # 插入新记录
                    insert_sql = """
                    INSERT INTO spu_sales_data (
                        date, product_code, style_code, national_code, supplier, supplier_style_code, color_spec,
                        product_name, product_short_name, product_category, virtual_category, cost_price, brand,
                        other_price1, other_price2, other_price3, other_price4, other_price5, developer,
                        other_attr2, other_attr3, other_attr4, other_attr5, basic_price, market_price,
                        sales_quantity, actual_quantity, actual_amount, sales_amount, sales_cost, actual_cost,
                        sales_profit, sales_margin_rate, return_quantity, actual_return_quantity, return_amount,
                        actual_return_amount, return_cost, actual_return_cost, return_profit, net_sales_quantity,
                        net_sales_amount, net_sales_cost, net_sales_profit, basic_amount, paid_amount,
                        discount_amount, shipping_income, shipping_expense, net_margin_rate, overseas_shipping_expense,
                        overseas_total_income, overseas_total_expense
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    
                    values = [row['date'], row['product_code'], row['style_code']] + self.prepare_row_values(row)
                    cursor.execute(insert_sql, values)
                    inserted += 1
                    
            except Exception as e:
                print(f"处理行数据时出错: {e}")
                skipped += 1
                continue
        
        conn.commit()
        return inserted, updated, skipped
    
    def prepare_row_values(self, row):
        """准备行数据值"""
        fields = [
            'national_code', 'supplier', 'supplier_style_code', 'color_spec', 'product_name', 
            'product_short_name', 'product_category', 'virtual_category', 'cost_price', 'brand',
            'other_price1', 'other_price2', 'other_price3', 'other_price4', 'other_price5', 
            'developer', 'other_attr2', 'other_attr3', 'other_attr4', 'other_attr5', 
            'basic_price', 'market_price', 'sales_quantity', 'actual_quantity', 'actual_amount',
            'sales_amount', 'sales_cost', 'actual_cost', 'sales_profit', 'sales_margin_rate',
            'return_quantity', 'actual_return_quantity', 'return_amount', 'actual_return_amount',
            'return_cost', 'actual_return_cost', 'return_profit', 'net_sales_quantity',
            'net_sales_amount', 'net_sales_cost', 'net_sales_profit', 'basic_amount', 
            'paid_amount', 'discount_amount', 'shipping_income', 'shipping_expense',
            'net_margin_rate', 'overseas_shipping_expense', 'overseas_total_income', 'overseas_total_expense'
        ]
        
        return [row.get(field) for field in fields]
    
    def import_all_files(self, file_pattern=None):
        """导入所有符合条件的Excel文件"""
        print("开始批量导入SPU数据...")
        
        # 确保目录存在
        Path(os.path.dirname(self.db_path)).mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        self.init_database()
        
        # 获取文件列表
        files = self.get_excel_files(file_pattern)
        
        if not files:
            print("未找到符合条件的Excel文件")
            return
        
        print(f"找到 {len(files)} 个文件待处理")
        
        total_processed = 0
        successful_files = 0
        
        for i, file_path in enumerate(files, 1):
            print(f"处理文件 {i}/{len(files)}")
            processed = self.import_excel_file(file_path)
            
            if processed > 0:
                total_processed += processed
                successful_files += 1
        
        print(f"批量导入完成!")
        print(f"成功处理: {successful_files}/{len(files)} 个文件")
        print(f"总共处理: {total_processed} 条记录")
        
        # 显示数据库统计信息
        self.show_database_stats()
    
    def show_database_stats(self):
        """显示数据库统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 总记录数
            cursor.execute("SELECT COUNT(*) FROM spu_sales_data;")
            total_records = cursor.fetchone()[0]
            
            # 日期范围
            cursor.execute("SELECT MIN(date), MAX(date) FROM spu_sales_data;")
            date_range = cursor.fetchone()
            
            # 不同的款式编码数量
            cursor.execute("SELECT COUNT(DISTINCT style_code) FROM spu_sales_data;")
            style_count = cursor.fetchone()[0]
            
            # 不同的商品编码数量
            cursor.execute("SELECT COUNT(DISTINCT product_code) FROM spu_sales_data;")
            product_count = cursor.fetchone()[0]
            
            # 总销售额
            cursor.execute("SELECT SUM(net_sales_amount) FROM spu_sales_data WHERE net_sales_amount IS NOT NULL;")
            total_sales = cursor.fetchone()[0] or 0
            
            # print(f"数据库统计信息:")
            # print(f"总记录数: {total_records:,}")
            # print(f"日期范围: {date_range[0]}-{date_range[1]}")
            # print(f"款式编码数: {style_count:,}")
            # print(f"商品编码数: {product_count:,}")
            # print(f"总销售额: ¥{total_sales:,.2f}")
            
            conn.close()
            
        except Exception as e:
            print(f"获取统计信息失败: {e}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='SPU数据导入工具')
    parser.add_argument('--file', help='指定要导入的文件模式，例如: *250101*.xlsx')
    parser.add_argument('--db', default='static/db/spu_data.db', help='数据库文件路径')
    parser.add_argument('--dir', default='static/spu_day_date', help='Excel文件目录')
    
    args = parser.parse_args()
    
    importer = SPUDataImporter(db_path=args.db, excel_dir=args.dir)
    
    if args.file:
        # 导入指定文件
        importer.import_all_files(args.file)
    else:
        # 导入所有文件
        importer.import_all_files()

if __name__ == '__main__':
    main() 