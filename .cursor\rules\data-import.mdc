---
description: 
globs: 
alwaysApply: true
---
# 数据导入指南

## 📥 数据导入工具总览

系统提供两个主要的数据导入工具，用于将Excel文件数据导入到SQLite数据库中：

## 💰 销售数据导入

### 工具文件
- **[sale_data_importer.py](mdc:sale_data_importer.py)** - 销售数据导入工具
- **[sale_import_example.py](mdc:sale_import_example.py)** - 使用示例
- **[SALE_DATA_IMPORT_README.md](mdc:SALE_DATA_IMPORT_README.md)** - 详细说明文档

### 主要功能
- 批量导入销售主题分析Excel文件
- 自动检测和处理重复数据（基于店铺编码+日期）
- 数据清理和标准化
- 分批处理支持大文件导入
- 详细的导入统计报告

### 使用方法
```bash
# 导入所有Excel文件
python sale_data_importer.py

# 导入特定日期文件
python sale_data_importer.py --file "*20250610*.xlsx"

# 自定义数据库和目录路径
python sale_data_importer.py --db "custom/path/database.db" --dir "custom/excel/dir"
```

### 数据库结构
- 数据库：`static/db/sale_data.db`
- 主表：`sale_data`
- 唯一约束：`(shop_code, date)`
- 包含字段：店铺信息、销售数据、退货数据、成本利润等

### 支持的Excel文件格式
- 文件位置：`static/sale_data_xlsx/`
- 文件命名：`销售主题分析_多维分析_YYYYMMDDHHMMSS_*.xlsx`
- 列名自动映射到数据库字段

## 🎯 SPU数据导入

### 工具文件
- **[spu_data_importer.py](mdc:spu_data_importer.py)** - SPU数据导入工具
- **[spu_import_example.py](mdc:spu_import_example.py)** - 使用示例
- **[SPU_DATA_IMPORT_README.md](mdc:SPU_DATA_IMPORT_README.md)** - 详细说明文档

### 主要功能
- 导入SPU（Standard Product Unit）数据
- 支持多时间维度数据分析
- 自动数据清理和验证
- 增量更新支持

### 使用方法
```bash
# 导入SPU数据
python spu_data_importer.py

# 导入示例数据
python spu_import_example.py
```

## 🔄 数据迁移工具

### 迁移工具
- **[spu_data_migration.py](mdc:spu_data_migration.py)** - SPU数据迁移工具
- 用于数据库结构更新和数据迁移

## 🔍 数据验证工具

### 验证脚本
- **[check_sales_data.py](mdc:check_sales_data.py)** - 销售数据验证
- **[check_tables.py](mdc:check_tables.py)** - 数据表结构检查

### 功能
- 验证数据完整性
- 检查数据格式
- 统计数据质量指标

## 📊 数据查询工具

### 查询工具
- **[database_query.py](mdc:database_query.py)** - 数据库查询工具
- 提供复杂查询和数据分析功能

## 🛠️ 工具函数库

### 核心工具
- **[utils/sales_data.py](mdc:utils/sales_data.py)** - 销售数据处理工具
- **[utils/data_utils.py](mdc:utils/data_utils.py)** - 通用数据处理工具
- **[utils/file_utils.py](mdc:utils/file_utils.py)** - 文件处理工具
- **[utils/date_utils.py](mdc:utils/date_utils.py)** - 日期处理工具

### 业务相关工具
- **[utils/shop_utils.py](mdc:utils/shop_utils.py)** - 店铺数据处理
- **[utils/promotion_utils.py](mdc:utils/promotion_utils.py)** - 推广数据处理

## 📁 数据存储结构

### 数据库文件
```
static/
├── db/
│   ├── sale_data.db      # 销售数据库
│   ├── spu_data.db       # SPU数据库
│   └── violation_data.db # 违规数据库
```

### Excel文件目录
```
static/
├── sale_data_xlsx/       # 销售数据Excel文件
├── spu_data_xlsx/        # SPU数据Excel文件
└── temp/                 # 临时文件目录
```

## 🔧 导入流程

### 标准导入流程
1. **文件扫描** - 扫描指定目录下的Excel文件
2. **数据读取** - 使用pandas读取Excel文件
3. **数据清理** - 标准化格式、处理空值
4. **数据验证** - 检查数据完整性和格式
5. **批量导入** - 分批插入数据库
6. **重复处理** - 检测并处理重复记录
7. **统计报告** - 生成导入结果报告

### 错误处理
- 容错机制：单条记录错误不影响整体导入
- 详细日志：记录处理进度和错误信息
- 回滚机制：确保数据一致性

## 📝 配置文件

### 店铺配置
- **[店铺-账号信息.csv](mdc:店铺-账号信息.csv)** - 店铺基础信息
- **[店铺-cookie.json](mdc:店铺-cookie.json)** - 店铺认证信息

### 用户配置
- **[宜承账号.json](mdc:宜承账号.json)** - 用户账号信息

## 🚀 性能优化

### 导入优化
- 分批处理：每批1000条记录
- 索引优化：自动创建查询索引
- 内存管理：避免大文件内存溢出
- 并发处理：支持多文件并发导入

### 数据库优化
- 事务管理：批量提交减少I/O
- 索引策略：基于查询模式优化索引
- 数据压缩：减少存储空间占用

## 📊 监控和日志

### 导入监控
- 实时进度提示
- 错误统计和报告
- 性能指标监控
- 数据质量评估

### 日志记录
- 详细的操作日志
- 错误堆栈跟踪
- 性能统计信息
- 数据变更记录

## 🔄 定期维护

### 数据维护
- 定期清理临时文件
- 数据库优化和压缩
- 索引重建和优化
- 过期数据清理

### 系统维护
- 定期备份数据库
- 检查数据完整性
- 性能监控和调优
- 日志轮转和清理

