// 检查登录状态
function checkLoginStatus() {
    const cookies = document.cookie.split(';');
    const loginAuth = cookies.find(cookie => cookie.trim().startsWith('loginAuth='));
    if (!loginAuth || loginAuth.split('=')[1].trim() !== 'true') {
        window.location.href = 'login.html';
        return false;
    }
    return true;
}

// 生成财务管理界面
async function generateFinanceManagement() {
    if (!checkLoginStatus()) return;

    const container = document.querySelector('.container');
    
    // 创建页面标题和更新时间
    const header = document.createElement('div');
    header.className = 'header';
    header.innerHTML = `
        <h1>财务管理</h1>
        <div class="update-time" style="color: #666; font-size: 14px; margin-top: 5px;">数据更新时间：<span id="lastUpdateTime">--</span></div>
    `;
    
    // 创建标签页
    const tabsContainer = document.createElement('div');
    tabsContainer.className = 'tabs-container';
    tabsContainer.innerHTML = `
        <div class="tabs">
            <div class="tab active" data-tab="overview">财务概览</div>
            <div class="tab" data-tab="details">财务明细</div>
            <div class="tab" data-tab="account-balance">账户余额</div>
        </div>
        <div class="tab-content" id="overview-tab">
            <div class="shop-list" id="overviewCards"></div>
        </div>
        <div class="tab-content" id="details-tab" style="display: none;">
            <div class="control-panel">
                <div class="search-box">
                    <input type="date" id="startDate">
                    <input type="date" id="endDate">
                    <button onclick="filterFinanceData()">筛选</button>
                </div>
                <div class="view-controls">
                    <select id="reportType">
                        <option value="daily">日报表</option>
                        <option value="weekly">周报表</option>
                        <option value="monthly">月报表</option>
                    </select>
                </div>
            </div>
            <table class="shop-table">
                <thead>
                    <tr>
                        <th>日期</th>
                        <th>类型</th>
                        <th>金额</th>
                        <th>支付方式</th>
                        <th>备注</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="financeTableBody"></tbody>
            </table>
            <div class="pagination" id="financePagination"></div>
        </div>
        <div class="tab-content" id="account-balance-tab" style="display: none;">
            <div class="control-panel">
                <div class="search-box">
                    <input type="text" id="balanceSearch" placeholder="搜索店铺名称...">
                    <button onclick="searchBalanceData()">搜索</button>
                </div>
                <div class="view-controls">
                    <select id="itemsPerPageSelect" onchange="changeItemsPerPage(this.value)">
                        <option value="9">9条/页</option>
                        <option value="18">18条/页</option>
                        <option value="36">36条/页</option>
                    </select>
                    <button id="gridViewBtn" class="view-btn active" onclick="switchBalanceView('grid')">
                        网格视图
                    </button>
                    <button id="listViewBtn" class="view-btn" onclick="switchBalanceView('list')">
                        列表视图
                    </button>
                </div>
            </div>
            <div class="balance-data-container" id="balanceDataContainer">
                <div class="balance-loading" id="balanceLoading">
                    <p>正在加载账户余额数据...</p>
                </div>
            </div>
            <div class="pagination" id="balancePagination"></div>
        </div>
    `;
    
    // 添加所有元素到容器
    container.appendChild(header);
    container.appendChild(tabsContainer);

    // 添加CSS样式
    addFinanceStyles();

    // 初始加载数据
    loadFinanceOverview();
    
    // 设置标签页切换事件
    const tabs = document.querySelectorAll('.tab');
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // 移除所有标签页的active类
            tabs.forEach(t => t.classList.remove('active'));
            // 添加当前标签页的active类
            tab.classList.add('active');
            
            // 隐藏所有内容区域
            document.querySelectorAll('.tab-content').forEach(content => {
                content.style.display = 'none';
            });
            
            // 显示当前标签页对应的内容区域
            const tabId = tab.getAttribute('data-tab');
            document.getElementById(`${tabId}-tab`).style.display = 'block';
            
            // 根据标签页加载相应数据
            if (tabId === 'details') {
                loadFinanceData();
            } else if (tabId === 'account-balance') {
                checkAccountBalanceData();
            }
        });
    });
}

// 添加财务管理样式
function addFinanceStyles() {
    const styleElement = document.createElement('style');
    styleElement.textContent = `
        .tabs-container {
            margin-top: 20px;
            width: 100%;
        }
        
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            transition: all 0.3s;
            border-bottom: 2px solid transparent;
            font-weight: 500;
        }
        
        .tab:hover {
            background-color: #f5f5f5;
        }
        
        .tab.active {
            border-bottom: 2px solid #4CAF50;
            color: #4CAF50;
        }
        
        .tab-content {
            padding: 20px 0;
        }
        
        .upload-section {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .upload-description {
            color: #666;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .file-upload-container {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .upload-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
            transition: background-color 0.3s;
        }
        
        .upload-btn:hover {
            background-color: #45a049;
        }
        
        .upload-status {
            margin-top: 10px;
            font-size: 14px;
        }
        
        .upload-status.success {
            color: #4CAF50;
        }
        
        .upload-status.error {
            color: #F44336;
        }
        
        .balance-data-container {
            margin-top: 20px;
        }
        
        .balance-loading {
            text-align: center;
            color: #666;
            padding: 30px;
        }
        
        .shop-balance-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 15px;
            overflow: hidden;
        }
        
        .shop-balance-header {
            background-color: #f5f5f5;
            padding: 10px 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .shop-balance-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .shop-balance-content {
            padding: 0;
        }
        
        .balance-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }
        
        .balance-table th, .balance-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .balance-table th {
            background-color: #f9f9f9;
            font-weight: 500;
            color: #555;
        }
        
        .balance-table tr:last-child td {
            border-bottom: none;
        }
        
        .balance-table tr:hover {
            background-color: #f5f5f5;
        }
        
        .data-type-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .data-type-badge.income {
            background-color: #E8F5E9;
            color: #2E7D32;
        }
        
        .data-type-badge.expense {
            background-color: #FFEBEE;
            color: #C62828;
        }
        
        .data-type-badge.balance {
            background-color: #E3F2FD;
            color: #1565C0;
        }
        
        .data-value {
            font-weight: 500;
        }
        
        .data-value.positive {
            color: #2E7D32;
        }
        
        .data-value.negative {
            color: #C62828;
        }
        
        .summary-row {
            background-color: #f5f5f5;
            font-weight: 600;
        }
        
        .no-data-message {
            text-align: center;
            padding: 30px;
            color: #666;
        }
        
        .balance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
            gap: 15px;
        }
        
        .csv-summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .csv-summary-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 15px;
            display: flex;
            flex-direction: column;
        }
        
        .csv-summary-title {
            font-size: 14px;
            color: #555;
            margin-bottom: 8px;
        }
        
        .csv-summary-value {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .csv-summary-value.income {
            color: #2E7D32;
        }
        
        .csv-summary-value.expense {
            color: #C62828;
        }
        
        .csv-summary-value.balance {
            color: #1565C0;
        }
        
        .control-panel {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 8px;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
        }
        
        .search-box input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            min-width: 200px;
        }
        
        .search-box button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .search-box button:hover {
            background-color: #45a049;
        }
        
        .view-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .view-controls select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
            cursor: pointer;
        }
        
        .sort-header {
            cursor: pointer;
            user-select: none;
            position: relative;
            padding-right: 20px !important;
        }
        
        .sort-header:hover {
            background-color: #f0f0f0;
        }
        
        .sort-header::after {
            content: '⇅';
            position: absolute;
            right: 5px;
            opacity: 0.3;
        }
        
        .sort-header.sort-asc::after {
            content: '↑';
            opacity: 1;
        }
        
        .sort-header.sort-desc::after {
            content: '↓';
            opacity: 1;
        }
        
        .view-btn {
            padding: 8px 16px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .view-btn.active {
            background-color: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }
        
        .balance-list-view {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .balance-list-view th,
        .balance-list-view td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .balance-list-view th {
            background-color: #f5f5f5;
            font-weight: 500;
            color: #333;
        }
        
        .balance-list-view tr:hover {
            background-color: #f9f9f9;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 5px;
            margin-top: 20px;
            padding: 10px;
        }
        
        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background-color: #fff;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s;
        }
        
        .page-btn.active {
            background-color: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }
        
        .page-btn:disabled {
            background-color: #f5f5f5;
            cursor: not-allowed;
            color: #999;
        }
    `;
    document.head.appendChild(styleElement);
}

// 计算CSV数据汇总
function calculateCSVSummary(data) {
    let summary = {
        pendingIncome: 0,
        yesterdayPromoExpense: 0,
        monthlyPromoExpense: 0,
        totalAssets: 0,
        Totalamountofmargin: 0,
        Totalpaymentamount: 0,
        Totalactivitypaymentamount: 0,
        shopCount: Object.keys(data).length
    };
    
    // 遍历每个店铺的数据
    for (const [shop, items] of Object.entries(data)) {
        items.forEach(item => {
            // 如果数据值是数字，则进行汇总
            if (!isNaN(parseFloat(item.dataValue)) || (typeof item.dataValue === 'string' && item.dataValue.includes('-'))) {
                let value = parseFloat(item.dataValue);
                if (isNaN(value)) value = 0;
                
                // 待到账金额
                if (item.dataType === '资金中心' && item.dataName === '待到账金额') {
                    summary.pendingIncome += value;
                }
                
                // 昨日推广支出
                if (item.dataType.includes('推广费日账单') && item.dataName.includes('正常支出数据')) {
                    // 对于负值或带有"-"的值，取绝对值
                    summary.yesterdayPromoExpense += (value < 0 || item.dataValue.includes('-')) ? Math.abs(value) : value;
                }
                
                // 月账单推广支出
                if (item.dataType.includes('推广费月账单') && (item.dataName.includes('现金总花费') || item.dataName.includes('支出'))) {
                    // 对于负值或带有"-"的值，取绝对值
                    summary.monthlyPromoExpense += (value < 0 || item.dataValue.includes('-')) ? Math.abs(value) : value;
                }
                
                // 资金中心的总资产
                if (item.dataType === '资金中心') {
                    summary.totalAssets += value;
                }
                
                // 货款总额
                if (item.dataType === '资金中心' && item.dataName === '货款账户') {
                    summary.Totalamountofmargin += value;
                }

                // 保证金总额
                if (item.dataType === '资金中心' && item.dataName === '店铺保证金账户') {
                    summary.Totalpaymentamount += value;
                }

                // 活动保证金总额
                if (item.dataType === '资金中心' && item.dataName === '活动保证金账户') {
                    summary.Totalactivitypaymentamount += value;
                }
            }
        });
    }
    
    return summary;
}

// 更新最后更新时间
function updateLastUpdateTime(data) {
    const lastUpdateTimeElement = document.getElementById('lastUpdateTime');
    if (!lastUpdateTimeElement) return;

    // 从数据中获取最新的更新时间
    const updateTime = data.updateTime || '--';
    lastUpdateTimeElement.textContent = updateTime;
}

// 更新最后更新时间
function updateLastUpdateTime(data) {
    const lastUpdateTimeElement = document.getElementById('lastUpdateTime');
    if (!lastUpdateTimeElement) return;

    // 从数据中获取最新的更新时间
    const updateTime = data.updateTime || new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
    lastUpdateTimeElement.textContent = updateTime;
}

// 加载财务概览数据
async function loadFinanceOverview() {
    const overviewCards = document.getElementById('overviewCards');
    
    try {
        // 获取财务概览数据
        const response = await fetch('/api/finance/overview');
        const data = await response.json();
        
        if (data.success) {
            const { income, expense, profit, updateTime } = data.data;
            
            // 更新最后更新时间
            updateLastUpdateTime({ updateTime });
            
            // 基本财务卡片
            let overviewHTML = `
                <div class="shop-card">
                    <h3>本月收入</h3>
                    <div class="stat-value">¥${income.value.toLocaleString()}</div>
                    <div class="shop-info">
                        <p>较上月: <span style="color: ${income.trend >= 0 ? '#4CAF50' : '#F44336'}">${income.trend >= 0 ? '↑' : '↓'} ${Math.abs(income.trend)}%</span></p>
                    </div>
                </div>
                <div class="shop-card">
                    <h3>本月支出</h3>
                    <div class="stat-value">¥${expense.value.toLocaleString()}</div>
                    <div class="shop-info">
                        <p>较上月: <span style="color: ${expense.trend >= 0 ? '#F44336' : '#4CAF50'}">${expense.trend >= 0 ? '↑' : '↓'} ${Math.abs(expense.trend)}%</span></p>
                    </div>
                </div>
                <div class="shop-card">
                    <h3>净利润</h3>
                    <div class="stat-value">¥${profit.value.toLocaleString()}</div>
                    <div class="shop-info">
                        <p>较上月: <span style="color: ${profit.trend >= 0 ? '#4CAF50' : '#F44336'}">${profit.trend >= 0 ? '↑' : '↓'} ${Math.abs(profit.trend)}%</span></p>
                    </div>
                </div>
            `;
            
            // 尝试获取CSV数据的汇总
            try {
                const csvResponse = await fetch('/api/finance/account-balance');
                const csvData = await csvResponse.json();
                
                if (csvData.success && csvData.data && Object.keys(csvData.data).length > 0) {
                    // 计算CSV数据的汇总
                    const summary = calculateCSVSummary(csvData.data);
                    
                    // 添加CSV数据汇总卡片
                    overviewHTML += `
                        <div style="grid-column: 1 / -1; margin-top: 20px;">
                            <h3 style="margin-bottom: 15px; color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px;">
                                账户余额汇总
                            </h3>
                            <div class="csv-summary-cards">
                                <div class="csv-summary-card">
                                    <div class="csv-summary-title">待到账金额</div>
                                    <div class="csv-summary-value income">¥${summary.pendingIncome.toLocaleString()}</div>
                                </div>
                                <div class="csv-summary-card">
                                    <div class="csv-summary-title">昨日推广支出</div>
                                    <div class="csv-summary-value expense">¥${summary.yesterdayPromoExpense.toLocaleString()}</div>
                                </div>
                                <div class="csv-summary-card">
                                    <div class="csv-summary-title">上月推广支出</div>
                                    <div class="csv-summary-value expense">¥${summary.monthlyPromoExpense.toLocaleString()}</div>
                                </div>
                                <div class="csv-summary-card">
                                    <div class="csv-summary-title">货款汇总</div>
                                    <div class="csv-summary-value balance">¥${summary.Totalamountofmargin.toLocaleString()}</div>
                                </div>
                                <div class="csv-summary-card">
                                    <div class="csv-summary-title">店铺保证汇总</div>
                                    <div class="csv-summary-value balance">¥${summary.Totalpaymentamount.toLocaleString()}</div>
                                </div>
                                <div class="csv-summary-card">
                                    <div class="csv-summary-title">活动保证金汇总</div>
                                    <div class="csv-summary-value balance">¥${summary.Totalactivitypaymentamount.toLocaleString()}</div>
                                </div>
                                <div class="csv-summary-card">
                                    <div class="csv-summary-title">总资产</div>
                                    <div class="csv-summary-value balance">¥${summary.totalAssets.toLocaleString()}</div>
                                </div>
                                <div class="csv-summary-card">
                                    <div class="csv-summary-title">店铺数量</div>
                                    <div class="csv-summary-value">${summary.shopCount}</div>
                                </div>
                                
                            </div>
                        </div>
                    `;
                }
            } catch (csvError) {
                console.error('获取CSV数据汇总失败:', csvError);
            }
            
            overviewCards.innerHTML = overviewHTML;
        }
    } catch (error) {
        console.error('获取财务概览数据失败:', error);
        overviewCards.innerHTML = '<div class="error-message">获取数据失败，请稍后重试</div>';
    }
}

// 加载财务数据函数
async function loadFinanceData(page = 1) {
    try {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        const reportType = document.getElementById('reportType').value;
        const pageSize = 10;

        const queryParams = new URLSearchParams({
            startDate,
            endDate,
            reportType,
            page,
            pageSize
        });

        const response = await fetch(`/api/finance/details?${queryParams}`);
        const data = await response.json();

        if (data.success) {
            const tableBody = document.getElementById('financeTableBody');
            const pagination = document.getElementById('financePagination');

            // 更新表格数据
            tableBody.innerHTML = data.data.items.map(item => `
                <tr>
                    <td>${item.date}</td>
                    <td>${item.type}</td>
                    <td style="color: ${item.amount >= 0 ? '#4CAF50' : '#F44336'}">
                        ${item.amount >= 0 ? '¥' : '-¥'}${Math.abs(item.amount).toLocaleString()}
                    </td>
                    <td>${item.paymentMethod}</td>
                    <td>${item.note}</td>
                    <td>
                        <button class="action-btn" onclick="showFinanceDetail('${item.date}')">查看详情</button>
                    </td>
                </tr>
            `).join('');

            // 更新分页控件
            const { currentPage, totalPages } = data.data.pagination;
            let paginationHTML = '';

            // 上一页按钮
            paginationHTML += `<button class="page-btn" ${currentPage === 1 ? 'disabled' : ''} onclick="loadFinanceData(${currentPage - 1})">上一页</button>`;

            // 页码按钮
            for (let i = 1; i <= totalPages; i++) {
                paginationHTML += `<button class="page-btn ${i === currentPage ? 'active' : ''}" onclick="loadFinanceData(${i})">${i}</button>`;
            }

            // 下一页按钮
            paginationHTML += `<button class="page-btn" ${currentPage === totalPages ? 'disabled' : ''} onclick="loadFinanceData(${currentPage + 1})">下一页</button>`;

            pagination.innerHTML = paginationHTML;
        }
    } catch (error) {
        console.error('加载财务数据失败:', error);
        document.getElementById('financeTableBody').innerHTML = '<tr><td colspan="6" class="error-message">加载数据失败，请稍后重试</td></tr>';
    }
}

// 筛选财务数据函数
function filterFinanceData() {
    loadFinanceData(1); // 重置到第一页并加载数据
}

// 显示财务详情函数
function showFinanceDetail(date) {
    // 这里可以实现查看详情的逻辑
    console.log('查看日期为', date, '的财务详情');
}

// 检查账户余额数据
async function checkAccountBalanceData() {
    const balanceLoading = document.getElementById('balanceLoading');
    balanceLoading.innerHTML = '<p>正在加载账户余额数据...</p>';
    
    try {
        const response = await fetch('/api/finance/account-balance');
        const data = await response.json();
        
        if (data.success) {
            // 加载账户余额数据
            loadAccountBalanceData(data.data);
        } else {
            balanceLoading.innerHTML = `<p>未找到账户余额数据: ${data.message}</p>`;
        }
    } catch (error) {
        console.error('检查账户余额数据失败:', error);
        balanceLoading.innerHTML = '<p>加载账户余额数据失败，请稍后重试</p>';
    }
}

// 修改全局变量
window.balanceViewType = window.balanceViewType || 'grid';
window.balanceData = window.balanceData || null;
window.currentBalancePage = window.currentBalancePage || 1;
window.itemsPerPage = window.itemsPerPage || 9;
window.filteredBalanceData = window.filteredBalanceData || null;
window.currentSortField = window.currentSortField || '';
window.currentSortOrder = window.currentSortOrder || 'asc';

// 切换每页显示数量
function changeItemsPerPage(value) {
    window.itemsPerPage = parseInt(value);
    window.currentBalancePage = 1;
    displayBalanceData();
}

// 排序函数
function sortBalanceData(field) {
    if (window.currentSortField === field) {
        window.currentSortOrder = window.currentSortOrder === 'asc' ? 'desc' : 'asc';
    } else {
        window.currentSortField = field;
        window.currentSortOrder = 'asc';
    }
    
    displayBalanceData();
}

// 获取排序后的店铺数据
function getSortedShops(shops) {
    if (!window.currentSortField) return shops;
    
    return shops.sort(([shopA, itemsA], [shopB, itemsB]) => {
        let valueA, valueB;
        
        switch (window.currentSortField) {
            case 'name':
                valueA = shopA;
                valueB = shopB;
                break;
            case 'totalAssets':
                valueA = calculateShopTotalAssets(itemsA);
                valueB = calculateShopTotalAssets(itemsB);
                break;
            case 'pendingIncome':
                valueA = calculateShopPendingIncome(itemsA);
                valueB = calculateShopPendingIncome(itemsB);
                break;
            case 'yesterdayPromo':
                valueA = calculateShopYesterdayPromo(itemsA);
                valueB = calculateShopYesterdayPromo(itemsB);
                break;
            case 'monthlyPromo':
                valueA = calculateShopMonthlyPromo(itemsA);
                valueB = calculateShopMonthlyPromo(itemsB);
                break;
            case 'Totalamountofmargin':
                valueA = calculateShopTotalamountofmargin(itemsA);
                valueB = calculateShopTotalamountofmargin(itemsB);
                break;
            case 'Totalpaymentamount':
                valueA = calculateShopTotalpaymentamount(itemsA);
                valueB = calculateShopTotalpaymentamount(itemsB);
                break;
            case 'Totalactivitypaymentamount':
                valueA = calculateShopTotalactivitypaymentamount(itemsA);
                valueB = calculateShopTotalactivitypaymentamount(itemsB);
                break;
        }
        
        if (typeof valueA === 'string') {
            const comparison = valueA.localeCompare(valueB);
            return window.currentSortOrder === 'asc' ? comparison : -comparison;
        } else {
            return window.currentSortOrder === 'asc' ? valueA - valueB : valueB - valueA;
        }
    });
}

// 切换账户余额视图类型
function switchBalanceView(viewType) {
    window.balanceViewType = viewType;
    document.getElementById('gridViewBtn').classList.toggle('active', viewType === 'grid');
    document.getElementById('listViewBtn').classList.toggle('active', viewType === 'list');
    displayBalanceData();
}

// 搜索账户余额数据
function searchBalanceData() {
    const searchTerm = document.getElementById('balanceSearch').value.toLowerCase().trim();
    if (!window.balanceData) return;
    
    if (searchTerm === '') {
        window.filteredBalanceData = window.balanceData;
    } else {
        window.filteredBalanceData = {};
        for (const [shop, items] of Object.entries(window.balanceData)) {
            if (shop.toLowerCase().includes(searchTerm)) {
                window.filteredBalanceData[shop] = items;
            }
        }
    }
    
    window.currentBalancePage = 1;
    displayBalanceData();
}

// 更新分页控件
function updateBalancePagination(totalPages) {
    const pagination = document.getElementById('balancePagination');
    let paginationHTML = '';
    
    // 上一页按钮
    paginationHTML += `<button class="page-btn" ${window.currentBalancePage === 1 ? 'disabled' : ''} 
        onclick="changePage(${window.currentBalancePage - 1})">上一页</button>`;
    
    // 页码按钮
    for (let i = 1; i <= totalPages; i++) {
        paginationHTML += `<button class="page-btn ${i === window.currentBalancePage ? 'active' : ''}" 
            onclick="changePage(${i})">${i}</button>`;
    }
    
    // 下一页按钮
    paginationHTML += `<button class="page-btn" ${window.currentBalancePage === totalPages ? 'disabled' : ''} 
        onclick="changePage(${window.currentBalancePage + 1})">下一页</button>`;
    
    pagination.innerHTML = paginationHTML;
}

// 切换页码
function changePage(page) {
    window.currentBalancePage = page;
    displayBalanceData();
}

// 修改加载账户余额数据函数
function loadAccountBalanceData(data) {
    window.balanceData = data;
    window.filteredBalanceData = data;
    window.currentBalancePage = 1;
    displayBalanceData();
}

// 显示账户余额数据
function displayBalanceData() {
    const balanceDataContainer = document.getElementById('balanceDataContainer');
    
    if (!window.filteredBalanceData || Object.keys(window.filteredBalanceData).length === 0) {
        balanceDataContainer.innerHTML = '<div class="no-data-message">没有账户余额数据</div>';
        return;
    }
    
    // 计算CSV数据的汇总
    const summary = calculateCSVSummary(window.filteredBalanceData);
    
    // 添加汇总信息
    let html = `
        <div class="csv-summary-cards">
            <div class="csv-summary-card">
                <div class="csv-summary-title">最新待到账金额</div>
                <div class="csv-summary-value income">¥${summary.pendingIncome.toLocaleString()}</div>
            </div>
            <div class="csv-summary-card">
                <div class="csv-summary-title">昨日推广支出</div>
                <div class="csv-summary-value expense">¥${summary.yesterdayPromoExpense.toLocaleString()}</div>
            </div>
            <div class="csv-summary-card">
                <div class="csv-summary-title">上月推广支出</div>
                <div class="csv-summary-value expense">¥${summary.monthlyPromoExpense.toLocaleString()}</div>
            </div>
            <div class="csv-summary-card">
                <div class="csv-summary-title">货款汇总</div>
                <div class="csv-summary-value balance">¥${summary.Totalamountofmargin.toLocaleString()}</div>
            </div>
            <div class="csv-summary-card">
                <div class="csv-summary-title">店铺保证汇总</div>
                <div class="csv-summary-value balance">¥${summary.Totalpaymentamount.toLocaleString()}</div>
            </div>
            <div class="csv-summary-card">
                <div class="csv-summary-title">活动保证金汇总</div>
                <div class="csv-summary-value balance">¥${summary.Totalactivitypaymentamount.toLocaleString()}</div>
            </div>
            <div class="csv-summary-card">
                <div class="csv-summary-title">总资产</div>
                <div class="csv-summary-value balance">¥${summary.totalAssets.toLocaleString()}</div>
            </div>
            <div class="csv-summary-card">
                <div class="csv-summary-title">店铺数量</div>
                <div class="csv-summary-value">${summary.shopCount}</div>
            </div>
        </div>
    `;
    
    let shops = Object.entries(window.filteredBalanceData);
    // 应用排序
    shops = getSortedShops(shops);
    
    const totalPages = Math.ceil(shops.length / window.itemsPerPage);
    const start = (window.currentBalancePage - 1) * window.itemsPerPage;
    const end = start + window.itemsPerPage;
    const currentPageShops = shops.slice(start, end);
    
    if (window.balanceViewType === 'grid') {
        html += '<div class="balance-grid">';
        currentPageShops.forEach(([shop, items]) => {
            html += generateShopCard(shop, items);
        });
        html += '</div>';
    } else {
        html += generateListView(currentPageShops);
    }
    
    balanceDataContainer.innerHTML = html;
    updateBalancePagination(totalPages);
}

// 生成店铺卡片HTML
function generateShopCard(shop, items) {
    let html = `
        <div class="shop-balance-card">
            <div class="shop-balance-header">
                <div class="shop-balance-name">${shop}</div>
            </div>
            <div class="shop-balance-content">
                <table class="balance-table">
                    <thead>
                        <tr>
                            <th>数据类型</th>
                            <th>数据名称</th>
                            <th>数据值</th>
                        </tr>
                    </thead>
                    <tbody>
    `;
    
    items.forEach(item => {
        const dataTypeBadgeClass = getDataTypeBadgeClass(item);
        const { valueClass, formattedValue } = formatDataValue(item.dataValue);
        
        html += `
            <tr>
                <td><span class="data-type-badge ${dataTypeBadgeClass}">${item.dataType}</span></td>
                <td>${item.dataName}</td>
                <td class="data-value ${valueClass}">${formattedValue}</td>
            </tr>
        `;
    });
    
    const totalAssets = calculateShopTotalAssets(items);
    html += `
                    <tr class="summary-row">
                        <td colspan="2">总资产</td>
                        <td class="data-value positive">¥${totalAssets.toLocaleString()}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    `;
    
    return html;
}

// 生成列表视图HTML
function generateListView(shops) {
    let html = `
        <table class="balance-list-view">
            <thead>
                <tr>
                    <th class="sort-header ${window.currentSortField === 'name' ? `sort-${window.currentSortOrder}` : ''}" 
                        onclick="sortBalanceData('name')">店铺名称</th>
                    <th class="sort-header ${window.currentSortField === 'totalAssets' ? `sort-${window.currentSortOrder}` : ''}" 
                        onclick="sortBalanceData('totalAssets')">总资产</th>
                    <th class="sort-header ${window.currentSortField === 'pendingIncome' ? `sort-${window.currentSortOrder}` : ''}" 
                        onclick="sortBalanceData('pendingIncome')">最新待到账金额</th>
                    <th class="sort-header ${window.currentSortField === 'Totalamountofmargin' ? `sort-${window.currentSortOrder}` : ''}" 
                        onclick="sortBalanceData('Totalamountofmargin')">货款汇总</th>
                    <th class="sort-header ${window.currentSortField === 'Totalpaymentamount' ? `sort-${window.currentSortOrder}` : ''}" 
                        onclick="sortBalanceData('Totalpaymentamount')">店铺保证汇总</th>
                    <th class="sort-header ${window.currentSortField === 'Totalactivitypaymentamount' ? `sort-${window.currentSortOrder}` : ''}" 
                        onclick="sortBalanceData('Totalactivitypaymentamount')">活动保证金汇总</th>
                    <th class="sort-header ${window.currentSortField === 'yesterdayPromo' ? `sort-${window.currentSortOrder}` : ''}" 
                        onclick="sortBalanceData('yesterdayPromo')">昨日推广支出</th>
                    <th class="sort-header ${window.currentSortField === 'monthlyPromo' ? `sort-${window.currentSortOrder}` : ''}" 
                        onclick="sortBalanceData('monthlyPromo')">上月推广支出</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    shops.forEach(([shop, items]) => {
        const totalAssets = calculateShopTotalAssets(items);
        const pendingIncome = calculateShopPendingIncome(items);
        const Totalamountofmargin = calculateShopTotalamountofmargin(items);
        const Totalpaymentamount = calculateShopTotalpaymentamount(items);
        const Totalactivitypaymentamount = calculateShopTotalactivitypaymentamount(items);
        const yesterdayPromo = calculateShopYesterdayPromo(items);
        const monthlyPromo = calculateShopMonthlyPromo(items);
        
        html += `
            <tr>
                <td>${shop}</td>
                <td class="data-value positive">¥${totalAssets.toLocaleString()}</td>
                <td class="data-value income">¥${pendingIncome.toLocaleString()}</td>
                <td class="data-value expense">¥${Totalamountofmargin.toLocaleString()}</td>
                <td class="data-value expense">¥${Totalpaymentamount.toLocaleString()}</td>
                <td class="data-value expense">¥${Totalactivitypaymentamount.toLocaleString()}</td>
                <td class="data-value expense">¥${yesterdayPromo.toLocaleString()}</td>
                <td class="data-value expense">¥${monthlyPromo.toLocaleString()}</td>
            </tr>
        `;
    });
    
    html += `
            </tbody>
        </table>
    `;
    
    return html;
}

// 辅助函数：获取数据类型徽章类名
function getDataTypeBadgeClass(item) {
    if (item.dataType.includes('推广') || item.dataName.includes('支出') || item.dataName.includes('花费')) {
        return 'expense';
    } else if (item.dataType.includes('待到账')) {
        return 'income';
    }
    return 'balance';
}

// 辅助函数：格式化数据值
function formatDataValue(value) {
    if (!isNaN(parseFloat(value))) {
        const numValue = parseFloat(value);
        if (numValue < 0 || value.startsWith('-')) {
            return {
                valueClass: 'negative',
                formattedValue: `-¥${Math.abs(numValue).toLocaleString()}`
            };
        }
        return {
            valueClass: 'positive',
            formattedValue: `¥${numValue.toLocaleString()}`
        };
    }
    return {
        valueClass: '',
        formattedValue: value
    };
}

// 辅助函数：计算店铺总资产
function calculateShopTotalAssets(items) {
    return items.reduce((total, item) => {
        if (item.dataType === '资金中心' && !isNaN(parseFloat(item.dataValue))) {
            return total + parseFloat(item.dataValue);
        }
        return total;
    }, 0);
}

// 辅助函数：计算店铺待到账金额
function calculateShopPendingIncome(items) {
    return items.reduce((total, item) => {
        if (item.dataType === '资金中心' && item.dataName === '待到账金额' && !isNaN(parseFloat(item.dataValue))) {
            return total + parseFloat(item.dataValue);
        }
        return total;
    }, 0);
}
// 辅助函数：计算店铺货款汇总
function calculateShopTotalamountofmargin(items) {
    return items.reduce((total, item) => {
        if (item.dataType === '资金中心' && item.dataName === '货款账户' && !isNaN(parseFloat(item.dataValue))) {
            return total + parseFloat(item.dataValue);
        }
        return total;
    }, 0);
}
// 辅助函数：计算店铺待到账金额
function calculateShopTotalpaymentamount(items) {
    return items.reduce((total, item) => {
        if (item.dataType === '资金中心' && item.dataName === '店铺保证金账户' && !isNaN(parseFloat(item.dataValue))) {
            return total + parseFloat(item.dataValue);
        }
        return total;
    }, 0);
}
// 辅助函数：计算店铺活动保证金汇总
function calculateShopTotalactivitypaymentamount(items) {
    return items.reduce((total, item) => {
        if (item.dataType === '资金中心' && item.dataName === '活动保证金账户' && !isNaN(parseFloat(item.dataValue))) {
            return total + parseFloat(item.dataValue);
        }
        return total;
    }, 0);
}

// 辅助函数：计算店铺昨日推广支出
function calculateShopYesterdayPromo(items) {
    return items.reduce((total, item) => {
        if (item.dataType.includes('推广费日账单') && item.dataName.includes('正常支出数据') && !isNaN(parseFloat(item.dataValue))) {
            const value = parseFloat(item.dataValue);
            return total + (value < 0 || item.dataValue.includes('-') ? Math.abs(value) : value);
        }
        return total;
    }, 0);
}

// 辅助函数：计算店铺月推广支出
function calculateShopMonthlyPromo(items) {
    return items.reduce((total, item) => {
        if (item.dataType.includes('推广费月账单') && (item.dataName.includes('现金总花费') || item.dataName.includes('支出')) && !isNaN(parseFloat(item.dataValue))) {
            const value = parseFloat(item.dataValue);
            return total + (value < 0 || item.dataValue.includes('-') ? Math.abs(value) : value);
        }
        return total;
    }, 0);
}