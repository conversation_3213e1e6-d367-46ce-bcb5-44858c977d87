from functools import wraps
from flask import request, jsonify
import json
import os

ACCOUNTS_FILE = '宜承账号.json'
VALID_ROLES = ['admin', 'yunying', 'art', 'caigou', 'caiwu', 'kefu']

def read_accounts():
    """读取账号信息"""
    try:
        if not os.path.exists(ACCOUNTS_FILE):
            # 如果文件不存在，创建一个包含默认admin账号的示例文件
            default_data = {
                "zhanghu": [
                    {
                        "user": "admin",
                        "password": "admin", # 初始密码，建议修改
                        "role": "admin",
                        "tags": ["系统管理员"]
                    }
                ]
            }
            with open(ACCOUNTS_FILE, 'w', encoding='utf-8') as f:
                json.dump(default_data, f, ensure_ascii=False, indent=4)
            print(f"警告: {ACCOUNTS_FILE} 不存在，已创建包含默认 admin 账号的示例文件。")
            return default_data
        
        with open(ACCOUNTS_FILE, 'r', encoding='utf-8') as f:
            accounts_data = json.load(f)
            if "zhanghu" not in accounts_data or not isinstance(accounts_data["zhanghu"], list):
                 print(f"错误: {ACCOUNTS_FILE} 格式不正确，缺少 'zhanghu' 列表。")
                 # 返回一个默认结构以防止崩溃，但可能是空的
                 return {"zhanghu": []}
            return accounts_data
    except json.JSONDecodeError:
        print(f"错误: {ACCOUNTS_FILE} JSON 解析失败。")
        return {"zhanghu": []}
    except Exception as e:
        print(f"读取账号文件失败: {e}")
        return {"zhanghu": []}

def write_accounts(accounts_data):
    """写入账号信息"""
    try:
        with open(ACCOUNTS_FILE, 'w', encoding='utf-8') as f:
            json.dump(accounts_data, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        print(f"写入账号文件失败: {e}")
        return False

def admin_required(f):
    """检查用户是否为管理员的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user_role = request.cookies.get('userRole')
        if user_role != 'admin':
            return jsonify({"success": False, "message": "权限不足，需要管理员权限"}), 403
        return f(*args, **kwargs)
    return decorated_function 