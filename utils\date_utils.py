from datetime import datetime, timedelta

def get_yesterday_date():
    """获取昨日日期字符串 (YYYYMMDD格式)"""
    yesterday = datetime.now() - timedelta(days=1)
    return yesterday.strftime("%Y%m%d")

def get_day_before_yesterday_date():
    """获取前天日期字符串 (YYYYMMDD格式)"""
    day_before_yesterday = datetime.now() - timedelta(days=2)
    return day_before_yesterday.strftime("%Y%m%d")

def format_date(date_str, format_type='monthly'):
    """格式化日期字符串
    
    Args:
        date_str: 原始日期字符串 (YYYY-MM-DD格式)
        format_type: 目标格式类型 ('monthly'或'daily')
    
    Returns:
        str: 格式化后的日期字符串
    """
    if format_type == 'monthly':
        # 年月格式
        dt = datetime.strptime(date_str, '%Y-%m-%d')
        return dt.strftime('%m月')
    else:
        # 月日格式
        dt = datetime.strptime(date_str, '%Y-%m-%d')
        return dt.strftime('%m-%d') 