/**
 * utils.js - Common utility functions for the application
 */

// Format date from YYYYMMDD to YYYY-MM-DD
export function formatDate(dateString) {
    if (!dateString) return '无数据';
    if (dateString.length === 8) {
        const year = dateString.substring(0, 4);
        const month = dateString.substring(4, 6);
        const day = dateString.substring(6, 8);
        return `${year}-${month}-${day}`;
    }
    return dateString;
}

// Format currency with locale
export function formatCurrency(value, minimumFractionDigits = 2, maximumFractionDigits = 2) {
    return parseFloat(value).toLocaleString('zh-CN', {
        minimumFractionDigits,
        maximumFractionDigits
    });
}

// Format percentage
export function formatPercentage(value, minimumFractionDigits = 1, maximumFractionDigits = 1) {
    return parseFloat(value).toLocaleString('zh-CN', {
        minimumFractionDigits,
        maximumFractionDigits
    }) + '%';
}

// Create a notification element and show it
export function showNotification(message, type = 'info', duration = 3000) {
    // Remove any existing notification
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Add to body
    document.body.appendChild(notification);
    
    // Show with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Hide after duration
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, duration);
}

// Load a script dynamically and return a promise
export function loadScript(src, id) {
    return new Promise((resolve, reject) => {
        if (document.getElementById(id)) {
            resolve(); // Script already loaded
            return;
        }
        
        const script = document.createElement('script');
        script.src = src;
        script.id = id;
        
        script.onload = () => resolve();
        script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
        
        document.head.appendChild(script);
    });
}

// Add CSS to document
export function addStylesheet(css, id) {
    if (document.getElementById(id)) {
        return; // Style already added
    }
    
    const style = document.createElement('style');
    style.id = id;
    style.textContent = css;
    document.head.appendChild(style);
}

// Create a DocumentFragment from HTML string
export function createElementFromHTML(htmlString) {
    const div = document.createElement('div');
    div.innerHTML = htmlString.trim();
    
    // Use DocumentFragment for better performance
    const fragment = document.createDocumentFragment();
    while (div.firstChild) {
        fragment.appendChild(div.firstChild);
    }
    
    return fragment;
}

// Debounce function for search inputs etc.
export function debounce(func, wait = 300) {
    let timeout;
    return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}
