import os
import csv
from config.settings import ENCODINGS

def read_promotion_data(date_str):
    """
    读取推广金额数据
    
    Args:
        date_str: 日期字符串，格式为YYYYMMDD
        
    Returns:
        dict: 以店铺名为键，推广金额为值的字典
    """
    promo_data = {}
    csv_path = os.path.join('static', 'temp', f"{date_str}账户余额.csv")

    # 如果指定日期的文件不存在，尝试查找最近的文件
    if not os.path.exists(csv_path):
        print(f"推广金额文件不存在: {csv_path}")

        # 获取temp目录下所有账户余额文件
        temp_dir = os.path.join('static', 'temp')
        if os.path.exists(temp_dir):
            files = [f for f in os.listdir(temp_dir) if f.endswith('账户余额.csv')]

            if files:
                # 按文件名排序（日期格式YYYYMMDD会自然排序）
                files.sort(reverse=True)
                csv_path = os.path.join(temp_dir, files[0])
                print(f"使用最近的账户余额文件: {files[0]}")
            else:
                print("未找到任何账户余额文件")
                return promo_data
        else:
            print(f"目录不存在: {temp_dir}")
            return promo_data

    # 尝试不同的编码方式读取文件
    for encoding in ENCODINGS:
        try:
            with open(csv_path, 'r', encoding=encoding) as f:
                reader = csv.reader(f)
                for row in reader:
                    if row and len(row) >= 4:
                        shop_name = row[0].strip()
                        category = row[1].strip() if len(row) > 1 else ""
                        type_name = row[2].strip() if len(row) > 2 else ""
                        amount_str = row[3].strip() if len(row) > 3 else ""

                        # 判断是否为推广费相关数据
                        if category == "推广费日账单" and type_name == "正常支出数据" and amount_str:
                            try:
                                # 尝试转换金额为浮点数
                                amount = float(amount_str)
                                if shop_name in promo_data:
                                    promo_data[shop_name] += amount
                                else:
                                    promo_data[shop_name] = amount
                            except ValueError:
                                pass
                                #print(f"无法转换金额为数字: {amount_str}")
            print(f"成功使用 {encoding} 编码读取推广费数据: {csv_path}")
            return promo_data
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"使用 {encoding} 编码读取推广费数据失败: {e}")
            continue

    print(f"无法读取推广费数据文件: {csv_path}")
    return promo_data 