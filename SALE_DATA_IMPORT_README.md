# 销售数据导入工具

这是一个用于将销售主题分析Excel文件导入到SQLite数据库的Python工具。

## 📋 功能特性

- ✅ **批量导入**: 支持一次性导入多个Excel文件
- 🔄 **增量更新**: 自动检测并更新已存在的记录（基于店铺编码+日期）
- 🎯 **数据清理**: 自动清理和标准化数据格式
- 📊 **统计信息**: 导入完成后显示详细的数据库统计
- 🚀 **高性能**: 分批处理，支持大文件导入
- 🛡️ **数据完整性**: 重复数据检测和处理

## 📁 文件结构

```
项目根目录/
├── sale_data_importer.py          # 主导入工具
├── sale_import_example.py         # 使用示例
├── check_sale_excel_structure.py  # Excel文件结构分析工具
├── static/
│   ├── db/
│   │   └── sale_data.db           # SQLite数据库文件
│   └── sale_data_xlsx/            # Excel文件目录
│       ├── 20250611155342.xlsx
│       ├── 销售主题分析_多维分析_20250610144257_89540908_1.xlsx
│       ├── 销售主题分析_多维分析_20250610143852_89539537_1.xlsx
│       └── 销售主题分析_多维分析_20250610143147_89537062_1.xlsx
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install pandas openpyxl sqlite3
```

### 2. 基本使用

#### 导入所有Excel文件
```bash
python sale_data_importer.py
```

#### 导入特定文件
```bash
# 导入2025年6月10日的文件
python sale_data_importer.py --file "*20250610*.xlsx"

# 导入特定文件名
python sale_data_importer.py --file "20250611155342.xlsx"
```

#### 使用自定义路径
```bash
python sale_data_importer.py --db "custom/path/database.db" --dir "custom/excel/dir"
```

### 3. 程序化使用

```python
from sale_data_importer import SaleDataImporter

# 创建导入器实例
importer = SaleDataImporter()

# 导入所有文件
importer.import_all_files()

# 导入特定文件
importer.import_all_files("*20250610*.xlsx")

# 查看统计信息
importer.show_database_stats()
```

## 📊 数据映射

Excel文件的列名会自动映射到数据库字段：

| Excel列名 | 数据库字段 | 类型 | 说明 |
|-----------|------------|------|------|
| 店铺 | shop_name | TEXT | 店铺名称 |
| 日期 | date | TEXT | 销售日期 |
| 店铺编码 | shop_code | INTEGER | 店铺唯一标识 |
| 销售单数 | sales_orders | INTEGER | 销售订单数 |
| 实发单数 | actual_orders | INTEGER | 实际发货订单数 |
| 销售数量 | sales_quantity | INTEGER | 销售商品数量 |
| 实发数量 | actual_quantity | INTEGER | 实际发货数量 |
| 销售金额 | sales_amount | REAL | 销售总金额 |
| 实发金额 | actual_amount | REAL | 实际发货金额 |
| 销售成本 | sales_cost | REAL | 销售成本 |
| 销售毛利 | sales_profit | REAL | 销售毛利 |
| 销售毛利率 | sales_margin_rate | TEXT | 销售毛利率(%) |
| 净销售额 | net_sales_amount | REAL | 净销售金额 |
| 净销量 | net_sales_quantity | INTEGER | 净销售数量 |
| ... | ... | ... | ... |

## 🎯 命令行参数

```
usage: sale_data_importer.py [-h] [--file FILE] [--db DB] [--dir DIR]

销售数据导入工具

options:
  -h, --help   显示帮助信息
  --file FILE  指定要导入的文件模式，例如: *20250610*.xlsx
  --db DB      数据库文件路径 (默认: static/db/sale_data.db)
  --dir DIR    Excel文件目录 (默认: static/sale_data_xlsx)
```

## 📈 导入过程

1. **初始化数据库**: 创建表和索引（如果不存在）
2. **文件扫描**: 扫描指定目录下的Excel文件
3. **数据读取**: 逐个读取Excel文件
4. **数据清理**: 标准化日期格式、处理空值、转换数据类型
5. **批量导入**: 分批插入数据，避免内存溢出
6. **重复检测**: 根据(店铺编码,日期)检测重复记录
7. **统计报告**: 显示导入结果和数据库统计信息

## 🏗️ 数据库结构

```sql
CREATE TABLE sale_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    shop_name TEXT NOT NULL,                    -- 店铺名称
    date TEXT NOT NULL,                         -- 销售日期
    shop_code INTEGER NOT NULL,                 -- 店铺编码
    sales_orders INTEGER,                       -- 销售单数
    actual_orders INTEGER,                      -- 实发单数
    sales_quantity INTEGER,                     -- 销售数量
    actual_quantity INTEGER,                    -- 实发数量
    actual_amount REAL,                         -- 实发金额
    sales_amount REAL,                          -- 销售金额
    sales_cost REAL,                           -- 销售成本
    actual_cost REAL,                          -- 实发成本
    sales_profit REAL,                         -- 销售毛利
    sales_margin_rate TEXT,                    -- 销售毛利率
    return_orders INTEGER,                      -- 退货单数
    return_quantity INTEGER,                    -- 退货数量
    actual_return_quantity INTEGER,            -- 实退数量
    return_amount REAL,                        -- 退货金额
    actual_return_amount REAL,                 -- 实退金额
    return_cost REAL,                          -- 退货成本
    actual_return_cost REAL,                   -- 实退成本
    return_profit REAL,                        -- 退货毛利
    net_sales_orders INTEGER,                  -- 净销售单数
    net_sales_quantity INTEGER,                -- 净销量
    net_sales_amount REAL,                     -- 净销售额
    net_sales_cost REAL,                       -- 净销售成本
    net_sales_profit REAL,                     -- 净销售毛利
    basic_amount REAL,                         -- 基本金额
    paid_amount REAL,                          -- 已付金额
    discount_amount REAL,                      -- 优惠金额
    shipping_income REAL,                      -- 运费收入
    shipping_expense REAL,                     -- 运费支出
    net_margin_rate TEXT,                      -- 净毛利率
    overseas_shipping_expense REAL,            -- 境外运费支出
    overseas_total_income REAL,               -- 境外收入总计
    overseas_total_expense REAL,              -- 境外支出总计
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(shop_code, date)                    -- 唯一约束
);
```

## 🔧 高级功能

### 数据完整性

- **唯一性约束**: 组合键(shop_code, date)确保数据唯一性
- **更新策略**: 遇到重复记录时自动更新现有数据
- **数据验证**: 自动验证必要字段的完整性

### 性能优化

- **分批处理**: 每批1000条记录，避免内存问题
- **索引优化**: 自动创建查询索引提升性能
- **事务管理**: 批量提交减少磁盘I/O

### 错误处理

- **容错机制**: 单条记录错误不影响整体导入
- **详细日志**: 显示处理进度和错误信息
- **统计报告**: 显示成功、更新、跳过的记录数

## 📋 使用示例

### 示例1: 导入新的日期数据
```bash
# 导入2025年6月11日的新数据
python sale_data_importer.py --file "*20250611*.xlsx"
```

### 示例2: 重新导入历史数据
```bash
# 重新导入2025年6月10日的所有数据
python sale_data_importer.py --file "*20250610*.xlsx"
```

### 示例3: 查看当前数据状态
```python
from sale_data_importer import SaleDataImporter

importer = SaleDataImporter()
importer.show_database_stats()
```

### 示例4: 数据查询
```python
import sqlite3

conn = sqlite3.connect('static/db/sale_data.db')
cursor = conn.cursor()

# 查询销售额最高的店铺
cursor.execute("""
    SELECT shop_name, SUM(sales_amount) as total_sales 
    FROM sale_data 
    GROUP BY shop_code, shop_name 
    ORDER BY total_sales DESC 
    LIMIT 10
""")

top_shops = cursor.fetchall()
for shop_name, total_sales in top_shops:
    print(f"{shop_name}: ¥{total_sales:,.2f}")

conn.close()
```

## 🛠️ 故障排除

### 常见问题

1. **Excel文件读取失败**
   - 确保Excel文件未被其他程序占用
   - 检查文件是否损坏
   - 确认文件格式为.xlsx

2. **数据库连接错误**
   - 确保数据库目录存在
   - 检查文件权限

3. **数据类型错误**
   - 检查Excel文件中的数据格式
   - 特别注意日期格式是否正确

4. **内存不足**
   - 减少batch_size参数
   - 单独处理大文件

### 性能建议

- 定期清理临时文件
- 使用SSD存储提升I/O性能
- 在低峰期进行大批量导入
- 定期分析数据库索引使用情况

## 📝 数据分析建议

导入数据后，可以进行以下分析：

1. **销售趋势分析**: 按日期统计销售额变化
2. **店铺表现分析**: 比较不同店铺的销售业绩
3. **毛利率分析**: 分析各店铺的盈利能力
4. **退货率分析**: 监控退货情况
5. **运营效率分析**: 对比销售与实发数据的差异

## 📞 技术支持

如有问题或建议，请联系开发团队。

---
*最后更新: 2025年6月11日* 