// 检查登录状态
function checkLoginStatus() {
    const cookies = document.cookie.split(';');
    const loginAuth = cookies.find(cookie => cookie.trim().startsWith('loginAuth='));
    if (!loginAuth || loginAuth.split('=')[1].trim() !== 'true') {
        window.location.href = 'login.html';
        return false;
    }
    return true;
}

// 加载运营数据 (支持单日或时段)
async function loadDailyOperationData(dateOrRange = '') {
    try {
        let url = '/api/daily-operation-data';
        if (dateOrRange) {
            // 检查是单个日期还是日期范围
            if (dateOrRange.includes(',')) {
                // 日期范围格式为 "YYYY-MM-DD,YYYY-MM-DD"
                const [startDate, endDate] = dateOrRange.split(',');
                url = `/api/daily-operation-data?startDate=${startDate}&endDate=${endDate}`;
            } else {
                // 单个日期
                url = `/api/daily-operation-data?date=${dateOrRange}`;
            }
        }
        // 如果 dateOrRange 为空，则请求默认（通常是昨天）

        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error('加载运营数据失败:', error);
        return { success: false, message: error.message }; // 返回更详细的错误信息
    }
}


// 加载店铺运营数据
async function loadShopOperatorData() {
    try {
        const response = await fetch('/api/shops'); // 假设这个 API 返回 { success: true, data: [{ name: 'shop1', operator: 'op1' }, ...] }
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const responseData = await response.json();

        // 将数组转换为以店铺名为键的对象
        const shopData = {};
        if (responseData && responseData.success && Array.isArray(responseData.data)) {
            responseData.data.forEach(shop => {
                if (shop.name) {
                    // 存储整个 shop 对象或者只存储需要的信息
                    shopData[shop.name] = { operator: shop.operator || '未知运营' }; // 假设API返回结构
                }
            });
        } else {
            console.warn('加载店铺运营数据格式不符合预期:', responseData);
        }
        return shopData; // 返回 { '店铺名': { operator: '运营名' }, ... }
    } catch (error) {
        console.error('加载店铺运营数据失败:', error);
        return {};
    }
}

// 应用程序状态管理（使用单例模式防止重复声明）
window.AppState = window.AppState || {
    shopOperatorMap: {},
    rawOperationData: null, // Store the raw data from API
    currentOperationDate: '未知日期',
    operatorDiversityData: [], // Processed data for op table
    spuListingData: [], // Processed data for spu table
    fixedTable1Data: [], // Processed data for fixed table 1
    fixedTable2Data: [], // Processed data for fixed table 2
    operatorDiversityFilters: {},
    spuListingFilters: {},
    fixedTable1Filters: {},
    fixedTable2Filters: {},

    // 初始化状态
    init(shopOperatorMap, rawData = null, dateLabel = '未知日期') {
        this.shopOperatorMap = shopOperatorMap;
        this.rawOperationData = rawData;
        this.currentOperationDate = dateLabel;
        // Initial processing
        if (rawData) {
            this.operatorDiversityData = processOperatorDiversityData(rawData, this.shopOperatorMap);
            this.spuListingData = processSpuListingData(rawData, this.shopOperatorMap);
        } else {
            this.operatorDiversityData = [];
            this.spuListingData = [];
        }
        this.fixedTable1Data = [];
        this.fixedTable2Data = [];
        this.operatorDiversityFilters = {};
        this.spuListingFilters = {};
        this.fixedTable1Filters = {};
        this.fixedTable2Filters = {};
    },
};


// --- 数据处理函数 ---

// 处理运营多样性数据 (原 processDailyOperationData)
function processOperatorDiversityData(rawData, shopOperatorMap) {
    if (!rawData || typeof rawData !== 'object') {
        console.error("Invalid daily operation data received:", rawData);
        return [];
    }
    // 提取运营-店铺-SPU-商品ID结构
    const operationData = {};

    // 先按运营分组
    Object.entries(rawData).forEach(([sku, shopsObj]) => {
        if (!shopsObj || typeof shopsObj !== 'object') return; // Skip invalid entries

        Object.entries(shopsObj).forEach(([shopName, shopData]) => {
            if (!shopData || typeof shopData !== 'object') return; // Skip invalid entries

            const operatorName = shopOperatorMap?.[shopName] || '未知运营';
            const count = shopData.商品数量 || 0;
            const productIds = shopData.商品ID列表 || [];

            if (count === 0 && productIds.length === 0) return; // Skip if no data for this entry

            // Init Operator
            if (!operationData[operatorName]) {
                operationData[operatorName] = { shops: {}, totalShops: 0, totalSpus: 0, totalLinks: 0, distinctSpus: new Set() };
            }

            // Init Shop
            if (!operationData[operatorName].shops[shopName]) {
                operationData[operatorName].shops[shopName] = { spus: {}, totalSpus: 0, totalLinks: 0 };
            }

            // Init/Update SPU
            if (!operationData[operatorName].shops[shopName].spus[sku]) {
                operationData[operatorName].shops[shopName].spus[sku] = { 商品数量: 0, 商品ID列表: [] };
                operationData[operatorName].shops[shopName].totalSpus++; // Count SPU per shop
            }
            operationData[operatorName].shops[shopName].spus[sku].商品数量 += count;
            // Make product IDs unique per SPU within a shop for this operator
            operationData[operatorName].shops[shopName].spus[sku].商品ID列表 = [...new Set([...operationData[operatorName].shops[shopName].spus[sku].商品ID列表, ...productIds])];


            // Update totals for shop
            operationData[operatorName].shops[shopName].totalLinks += count; // Accumulate link counts for the shop

            // Track distinct SPUs and total links for operator
            operationData[operatorName].distinctSpus.add(sku);
            operationData[operatorName].totalLinks += count; // Accumulate total links for operator
        });
    });

    // Final calculation and conversion to array
    return Object.entries(operationData).map(([operatorName, opData]) => {
        // Calculate final operator totals
        opData.totalShops = Object.keys(opData.shops).length; // Correct shop count
        opData.totalSpus = opData.distinctSpus.size;         // Correct distinct SPU count

        // Sort shops within operator (optional, e.g., by name)
        const sortedShops = Object.entries(opData.shops)
            .sort(([nameA], [nameB]) => nameA.localeCompare(nameB, 'zh-CN'))
            .reduce((obj, [key, value]) => {
                // Sort SPUs within shop (optional, e.g., by name)
                value.spus = Object.entries(value.spus)
                    .sort(([skuA], [skuB]) => skuA.localeCompare(skuB))
                    .reduce((spuObj, [spuKey, spuValue]) => {
                        // Calculate the correct link count for the shop based on unique IDs per SPU
                        value.totalLinks = Object.values(value.spus).reduce((sum, s) => sum + s.商品数量, 0);
                        spuObj[spuKey] = spuValue;
                        return spuObj;
                    }, {});
                obj[key] = value;
                return obj;
            }, {});


        return {
            operatorName,
            totalShops: opData.totalShops,
            totalSpus: opData.totalSpus, // Use distinct SPU count for operator header
            totalLinks: opData.totalLinks,
            shops: sortedShops // Use sorted shops
        };
    }).sort((a, b) => b.totalLinks - a.totalLinks); // Default sort operators by total links
}

// 处理SPU上架数据
function processSpuListingData(rawData, shopOperatorMap) {
    const spuData = {};
    // { 'SPU_Name': { operators: {'OpName': { links: 0, productIds: [] }}, totalLinks: 0, totalOperators: 0 } }

    if (!rawData || typeof rawData !== 'object') return [];

    Object.entries(rawData).forEach(([sku, shopsObj]) => {
        if (!spuData[sku]) {
            spuData[sku] = { operators: {}, totalLinks: 0, distinctOperators: new Set() };
        }

        Object.entries(shopsObj).forEach(([shopName, shopDetails]) => {
            const operatorName = shopOperatorMap[shopName] || '未知运营';
            const count = shopDetails.商品数量 || 0;
            const ids = shopDetails.商品ID列表 || [];

            if (count === 0 && ids.length === 0) return;

            if (!spuData[sku].operators[operatorName]) {
                spuData[sku].operators[operatorName] = { links: 0, productIds: [] };
            }

            spuData[sku].operators[operatorName].links += count;
            spuData[sku].operators[operatorName].productIds.push(...ids);
            spuData[sku].totalLinks += count;
            spuData[sku].distinctOperators.add(operatorName);
        });
    });

    return Object.entries(spuData).map(([spuName, spuDetails]) => ({
        spuName,
        totalOperators: spuDetails.distinctOperators.size,
        totalLinks: spuDetails.totalLinks,
        operators: Object.entries(spuDetails.operators)
            .sort(([opA], [opB]) => opA.localeCompare(opB, 'zh-CN'))
            .reduce((obj, [key, value]) => {
                // Make product IDs unique per operator for this SPU
                value.productIds = [...new Set(value.productIds)];
                obj[key] = value;
                return obj;
            }, {})
    })).sort((a, b) => b.totalLinks - a.totalLinks); // Sort SPUs by total links
}


// --- 表格生成函数 ---

// 生成运营多样性表格 (原 generateDailyOperationTable)
function generateOperatorDiversityTable(operatorDiversityData) {
    if (!operatorDiversityData || operatorDiversityData.length === 0) {
        return '<div class="empty-state">暂无运营数据</div>';
    }

    let tableHtml = `
        <div class="data-table-container" style="max-height: ${operatorDiversityData.length > 10 ? '450px' : '350px'}">
            <table class="data-table daily-operation-table" id="operatorDiversityTable">
                 <thead>
                    <tr>
                        <th style="width: 40px;"></th> <!-- Expand icon column -->
                        <th>运营 / 店铺 / SPU <input type="text" placeholder="搜索名称..." class="header-search" data-column="name"></th>
                        <th style="width: 90px; text-align: right;">店铺数</th>
                        <th style="width: 90px; text-align: right;">SPU数</th>
                        <th style="width: 90px; text-align: right;">链接数</th>
                        <th>商品ID列表 <input type="text" placeholder="搜索ID..." class="header-search" data-column="id"></th>
                    </tr>
                </thead>
                <tbody>
    `;

    operatorDiversityData.forEach((operator, opIndex) => {
        const operatorId = `op-${opIndex}`;
        // Operator Row (Level 1)
        tableHtml += `
            <tr class="operator-row level-1" data-level="1" data-operator-id="${operatorId}" data-operator-name="${operator.operatorName}">
                <td class="expand-control"><span class="expand-icon" onclick="toggleOperationRow(this)">▶</span></td>
                <td class="operator-name">${operator.operatorName}</td>
                <td class="data-cell number-cell">${operator.totalShops}</td>
                <td class="data-cell number-cell">${operator.totalSpus}</td> <!-- Operator's distinct SPU count -->
                <td class="data-cell number-cell">${operator.totalLinks}</td>
                <td class="data-cell"></td> <!-- Empty for operator level -->
            </tr>
        `;

        Object.entries(operator.shops).forEach(([shopName, shopData], shopIndex) => {
            const shopId = `${operatorId}-shop-${shopIndex}`;
            // Shop Row (Level 2) - Initially Hidden
            tableHtml += `
                <tr class="shop-row level-2 hidden-row" data-level="2" data-operator-id="${operatorId}" data-shop-id="${shopId}" data-shop-name="${shopName}">
                    <td class="expand-control"><span class="expand-icon" onclick="toggleOperationRow(this)">▶</span></td>
                    <td class="shop-name indent-1">${shopName}</td>
                    <td class="data-cell"></td> <!-- Empty for shop level -->
                    <td class="data-cell number-cell">${shopData.totalSpus}</td> <!-- Shop's SPU count -->
                    <td class="data-cell number-cell">${shopData.totalLinks}</td> <!-- Shop's link count -->
                    <td class="data-cell"></td> <!-- Empty for shop level -->
                </tr>
            `;

            Object.entries(shopData.spus).forEach(([spuName, spuData], spuIndex) => {
                const spuId = `${shopId}-spu-${spuIndex}`;
                // SPU Row (Level 3) - Initially Hidden
                tableHtml += `
                    <tr class="spu-row level-3 hidden-row" data-level="3" data-operator-id="${operatorId}" data-shop-id="${shopId}" data-spu-id="${spuId}" data-spu-name="${spuName}">
                        <td class="expand-control"></td> <!-- No expand icon for SPU level -->
                        <td class="spu-name indent-2">${spuName}</td>
                        <td class="data-cell"></td> <!-- Empty -->
                        <td class="data-cell"></td> <!-- Empty -->
                        <td class="data-cell number-cell">${spuData.商品数量}</td> <!-- SPU's link count -->
                        <td class="data-cell product-id-cell">
                            <div class="product-id-list-inline">
                                ${spuData.商品ID列表 && spuData.商品ID列表.length > 0
                                    ? spuData.商品ID列表.map(id => `<span class="product-id-badge" title="${id}">${id}</span>`).join('')
                                    : '<span class="text-muted">无ID</span>'
                                }
                            </div>
                        </td>
                    </tr>
                `;
            });
        });
    });

    tableHtml += `
                </tbody>
            </table>
        </div>
    `;

    return tableHtml;
}

// 生成SPU上架分布表格
function generateSpuListingTable(spuListingData) {
    if (!spuListingData || spuListingData.length === 0) {
        return '<div class="empty-state">暂无SPU上架数据</div>';
    }

    let tableHtml = `
        <div class="data-table-container" style="max-height: ${spuListingData.length > 10 ? '450px' : '350px'}">
            <table class="data-table spu-listing-table" id="spuListingTable">
                 <thead>
                        <tr>
                            <th style="width: 40px;"></th> <!-- Expand icon -->
                            <th>SPU <input type="text" placeholder="搜索SPU..." class="header-search" data-column="spu"></th>
                            <th>运营 <input type="text" placeholder="搜索运营..." class="header-search" data-column="operator"></th>
                            <th style="width: 90px; text-align: right;">总链接数</th>
                            <th>商品ID列表 <input type="text" placeholder="搜索ID..." class="header-search" data-column="id"></th>
                        </tr>
                    </thead>
                <tbody>
    `;

    spuListingData.forEach((spu, spuIndex) => {
        const spuId = `spu-${spuIndex}`;
        // SPU Row (Level 1)
        tableHtml += `
            <tr class="spu-row level-1" data-level="1" data-spu-id="${spuId}" data-spu-name="${spu.spuName}">
                <td class="expand-control"><span class="expand-icon" onclick="toggleSpuRow(this)">▶</span></td>
                <td class="spu-name">${spu.spuName}</td>
                <td class="data-cell number-cell">${spu.totalOperators}</td> <!-- Operator Count -->
                <td class="data-cell number-cell">${spu.totalLinks}</td> <!-- Total Links -->
                <td class="data-cell"></td> <!-- Empty for SPU level -->
            </tr>
        `;

        Object.entries(spu.operators).forEach(([operatorName, opData], opIndex) => {
            const operatorId = `${spuId}-op-${opIndex}`;
            // Operator Row (Level 2) - Initially Hidden
            tableHtml += `
                <tr class="operator-row level-2 hidden-row" data-level="2" data-spu-id="${spuId}" data-operator-id="${operatorId}" data-operator-name="${operatorName}">
                    <td class="expand-control"></td> <!-- No expand for operator level here -->
                    <td class="indent-1"></td> <!-- Indent under SPU -->
                    <td class="operator-name">${operatorName}</td>
                    <td class="data-cell number-cell">${opData.links}</td> <!-- Links for this Op/SPU -->
                    <td class="data-cell product-id-cell">
                         <div class="product-id-list-inline">
                            ${opData.productIds && opData.productIds.length > 0
                                ? opData.productIds.map(id => `<span class="product-id-badge" title="${id}">${id}</span>`).join('')
                                : '<span class="text-muted">无ID</span>'
                            }
                        </div>
                    </td>
                </tr>
            `;
        });
    });

    tableHtml += `
                </tbody>
            </table>
        </div>
    `;

    return tableHtml;
}


// --- UI构建与更新 ---

// 页面结构构建
function buildUI() {
    const container = document.querySelector('.container');
    if (!container) {
        console.error('未找到容器元素');
        return;
    }

    container.innerHTML = ''; // 清空容器

    // 添加主要页面结构
    const header = createHeader();
    if (header) container.appendChild(header);

    // Section for Operator Diversity Table (Original Table with modifications)
    const operatorDiversitySection = createTableSection(
        'operatorDiversityContainer',
        '运营上架多样性', // Title
        'operatorDiversityDate', // Date display ID
        'toggleOperatorDiversityVisibility', // Toggle function name
        'exportOperatorDiversityData' // Export function name
    );
     if (operatorDiversitySection) {
         // Add global controls (date picker, query button) ONLY to this section
        createDateAndQueryControls(operatorDiversitySection.querySelector('.section-controls'));
        container.appendChild(operatorDiversitySection);
     }


    // Section for SPU Listing Table (New Table)
    const spuListingSection = createTableSection(
        'spuListingContainer',
        'SPU上架分布', // Title
        'spuListingDate', // Date display ID
        'toggleSpuListingVisibility', // Toggle function name
        'exportSpuListingData' // Export function name
    );
    if (spuListingSection) container.appendChild(spuListingSection);

    // 添加固定数据表格1
    const fixedTable1Section = createTableSection(
        'fixedTable1Container',
        '固定数据表1(运营模式)', // Title
        'fixedTable1Date', // Date display ID
        'toggleFixedTable1Visibility', // Toggle function name
        'exportFixedTable1Data' // Export function name
    );
    if (fixedTable1Section) container.appendChild(fixedTable1Section);

    // 添加固定数据表格2
    const fixedTable2Section = createTableSection(
        'fixedTable2Container',
        '固定数据表2(SPU模式)', // Title
        'fixedTable2Date', // Date display ID
        'toggleFixedTable2Visibility', // Toggle function name
        'exportFixedTable2Data' // Export function name
    );
    if (fixedTable2Section) container.appendChild(fixedTable2Section);

    addCustomStyles(); // 添加样式
}

// 创建页面标题
function createHeader() {
    const header = document.createElement('div');
    header.className = 'header';
    header.innerHTML = '<h1>店铺商品上架管理</h1>'; // 更新标题
    return header;
}

// 创建表格区域通用函数
function createTableSection(containerId, titleText, dateId, toggleFnName, exportFnName) {
    const section = document.createElement('div');
    section.className = 'data-section'; // Use a common class for styling sections
    section.id = containerId;

    const header = document.createElement('div');
    header.className = 'section-header'; // Common header style

    const titleContainer = document.createElement('div');
    titleContainer.className = 'title-container';
    titleContainer.innerHTML = `
        <h2>${titleText} <span id="${dateId}" class="date-display">加载中...</span></h2>
        <button class="toggle-button" onclick="${toggleFnName}()">显示</button>
    `;

    const controlsContainer = document.createElement('div');
    controlsContainer.className = 'section-controls'; // Placeholder for specific controls

    // Add Export button if export function name is provided
    if (exportFnName && window[exportFnName]) { // Check if function exists
        const exportButton = document.createElement('button');
        exportButton.className = 'section-export-btn';
        exportButton.textContent = '导出数据';
        exportButton.onclick = window[exportFnName]; // Call global function by name
        controlsContainer.appendChild(exportButton);
    }

    header.appendChild(titleContainer);
    header.appendChild(controlsContainer); // Add controls container (might be empty for SPU table)

    const content = document.createElement('div');
    content.className = 'section-content hidden'; // 默认隐藏状态
    content.id = containerId + 'Content'; // e.g., operatorDiversityContainerContent
    content.innerHTML = '<div class="loading">加载中...</div>'; // Initial state

    section.appendChild(header);
    section.appendChild(content);

    return section;
}

// 创建日期选择和查询按钮 (只用于第一个表格区域)
function createDateAndQueryControls(parentContainer) {
    if (!parentContainer) return;

    // Date Selection Logic
    const dateSelector = document.createElement('div');
    dateSelector.className = 'date-selector';

    const rangeTypeLabel = document.createElement('label');
    rangeTypeLabel.htmlFor = 'dateRangeType';
    rangeTypeLabel.textContent = '查询类型:';

    const rangeTypeSelect = document.createElement('select');
    rangeTypeSelect.id = 'dateRangeType';
    rangeTypeSelect.className = 'date-range-type';
    rangeTypeSelect.innerHTML = `
        <option value="single" selected>单日查询</option>
        <option value="range">时段查询</option>
    `;

    const singleDateContainer = document.createElement('div');
    singleDateContainer.id = 'singleDateContainer';
    singleDateContainer.className = 'date-input-container';
    const datePicker = document.createElement('input');
    datePicker.type = 'date';
    datePicker.id = 'operationDatePicker';
    datePicker.className = 'operation-date-picker';
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    datePicker.value = yesterday.toISOString().split('T')[0];
    singleDateContainer.appendChild(datePicker);

    const rangeDateContainer = document.createElement('div');
    rangeDateContainer.id = 'rangeDateContainer';
    rangeDateContainer.className = 'date-input-container';
    rangeDateContainer.style.display = 'none'; // Initially hidden

    const endDate = new Date();
    endDate.setDate(endDate.getDate() - 1); // Yesterday
    const startDate = new Date(endDate);
    startDate.setDate(startDate.getDate() - 6); // Default 7 days

    const startDatePicker = document.createElement('input');
    startDatePicker.type = 'date';
    startDatePicker.id = 'startDatePicker';
    startDatePicker.className = 'date-range-picker';
    startDatePicker.value = startDate.toISOString().split('T')[0];

    const endDatePicker = document.createElement('input');
    endDatePicker.type = 'date';
    endDatePicker.id = 'endDatePicker';
    endDatePicker.className = 'date-range-picker';
    endDatePicker.value = endDate.toISOString().split('T')[0];

    rangeDateContainer.appendChild(document.createTextNode('从: '));
    rangeDateContainer.appendChild(startDatePicker);
    rangeDateContainer.appendChild(document.createTextNode(' 到: '));
    rangeDateContainer.appendChild(endDatePicker);

    rangeTypeSelect.addEventListener('change', function () {
        if (this.value === 'single') {
            singleDateContainer.style.display = 'inline-block';
            rangeDateContainer.style.display = 'none';
        } else {
            singleDateContainer.style.display = 'none';
            rangeDateContainer.style.display = 'flex';
        }
    });
    // Trigger change once to set initial state
    // setTimeout(() => rangeTypeSelect.dispatchEvent(new Event('change')), 0); // Ensure it runs after element is in DOM

    dateSelector.appendChild(rangeTypeLabel);
    dateSelector.appendChild(rangeTypeSelect);
    dateSelector.appendChild(singleDateContainer);
    dateSelector.appendChild(rangeDateContainer);

    // Query Button
    const queryButton = document.createElement('button');
    queryButton.className = 'operation-query-btn';
    queryButton.textContent = '查询';
    queryButton.addEventListener('click', handleQueryButtonClick); // Use central handler

    parentContainer.appendChild(dateSelector);
    parentContainer.appendChild(queryButton);

    // Trigger initial state correctly
     requestAnimationFrame(() => {
        if (rangeTypeSelect.value === 'single') {
             singleDateContainer.style.display = 'inline-block';
             rangeDateContainer.style.display = 'none';
         } else {
             singleDateContainer.style.display = 'none';
             rangeDateContainer.style.display = 'flex';
         }
     });
}

// 统一查询按钮处理
async function handleQueryButtonClick() {
    const rangeType = document.getElementById('dateRangeType').value;
    let dateParam = '';
    let queryDesc = ''; // For display

    if (rangeType === 'single') {
        const singleDate = document.getElementById('operationDatePicker').value;
        if (!singleDate) {
            alert('请选择查询日期');
            return;
        }
        dateParam = singleDate;
        queryDesc = singleDate;
    } else { // range
        const startDate = document.getElementById('startDatePicker').value;
        const endDate = document.getElementById('endDatePicker').value;
        if (!startDate || !endDate) {
            alert('请选择开始和结束日期');
            return;
        }
        if (new Date(startDate) > new Date(endDate)) {
            alert('开始日期不能晚于结束日期');
            return;
        }
        dateParam = `${startDate},${endDate}`; // Send as comma-separated for loadDailyOperationData
        queryDesc = `${startDate} 至 ${endDate}`;
    }

    showLoadingIndicator(); // Show loading before fetch
    const response = await loadDailyOperationData(dateParam);

    // Update BOTH tables using the SAME response data
    updateOperatorDiversityTable(response, queryDesc);
    updateSpuListingTable(response, queryDesc);

    // Setup search listeners AFTER tables are potentially re-rendered
    setupTableSearch('operatorDiversityTable');
    setupTableSearch('spuListingTable');

    hideLoadingIndicator(); // Hide loading after update
}


// 更新运营多样性表格
function updateOperatorDiversityTable(response, queryDesc = null) {
    const container = document.getElementById('operatorDiversityContainerContent');
    const dateDisplay = document.getElementById('operatorDiversityDate');
    const displayDate = response?.date || queryDesc || window.AppState.currentOperationDate || '查询结果';

    if(dateDisplay) dateDisplay.textContent = displayDate;

    if (!response || !response.success) {
        console.error("加载运营数据失败:", response?.message || "未知错误");
        if (container) container.innerHTML = `<div class="empty-state error-state">加载运营数据失败: ${response?.message || '请稍后重试'}</div>`;
        window.AppState.rawOperationData = null;
        window.AppState.operatorDiversityData = [];
        window.AppState.currentOperationDate = '加载失败';
        return;
    }

    window.AppState.rawOperationData = response.data;
    window.AppState.currentOperationDate = displayDate;
    window.AppState.operatorDiversityData = processOperatorDiversityData(response.data, window.AppState.shopOperatorMap);
    const filteredData = applyOperatorDiversityFilters(window.AppState.operatorDiversityData, window.AppState.operatorDiversityFilters);

    if (container) {
        if (filteredData && filteredData.length > 0) {
            container.innerHTML = generateOperatorDiversityTable(filteredData);
        } else {
            container.innerHTML = '<div class="empty-state">无数据显示 (可能已被筛选)</div>';
        }
        reattachSearchListeners('operatorDiversityTable'); // Re-attach listeners
    }
}

// 更新SPU分布表格
function updateSpuListingTable(response, queryDesc = null) {
    const container = document.getElementById('spuListingContainerContent');
    const dateDisplay = document.getElementById('spuListingDate');
    const displayDate = response?.date || queryDesc || window.AppState.currentOperationDate || '查询结果';

    if(dateDisplay) dateDisplay.textContent = displayDate;

    // SPU table relies on the raw data loaded by the query
    if (!window.AppState.rawOperationData) {
         if (container) {
             container.innerHTML = '<div class="empty-state">请先查询运营数据</div>';
         }
         window.AppState.spuListingData = [];
         return;
    }

    if (!response || !response.success) {
        // Data loading failed, but raw data might exist from previous success
        console.warn("更新SPU表时遇到错误响应，将尝试使用现有数据:", response?.message);
        // We don't clear SPU data here, try to render with existing if possible
    }


    // Process raw data (ensure rawData is up-to-date from AppState)
    window.AppState.spuListingData = processSpuListingData(window.AppState.rawOperationData, window.AppState.shopOperatorMap);

    // Apply filters if any
    const filteredData = applySpuListingFilters(window.AppState.spuListingData, window.AppState.spuListingFilters);

    // Render table
    if (container) {
        if (filteredData && filteredData.length > 0) {
            container.innerHTML = generateSpuListingTable(filteredData); // Use filtered data
        } else {
            container.innerHTML = '<div class="empty-state">无数据显示 (可能已被筛选)</div>';
        }
        reattachSearchListeners('spuListingTable'); // Re-attach listeners
    }
}


// --- 搜索与过滤 ---

// 设置表格搜索监听
function setupTableSearch(tableId) {
    const table = document.getElementById(tableId);
    if (!table) return;

    const searchInputs = table.querySelectorAll('thead .header-search');
    searchInputs.forEach(input => {
        // Remove existing listener to avoid duplicates if called multiple times
        input.removeEventListener('input', handleSearchInput);
        // Add the listener
        input.addEventListener('input', debounce(handleSearchInput, 300)); // Use debounce
    });
}

// Search input handler
function handleSearchInput() {
    const input = this; // The input element that triggered the event
    const table = input.closest('table');
    if (!table) return;

    const tableId = table.id;
    const column = input.dataset.column;
    const query = input.value.toLowerCase().trim();

    let filters;
    let updateFunction;
    let responseData;

    if (tableId === 'operatorDiversityTable') {
        filters = window.AppState.operatorDiversityFilters;
        updateFunction = updateOperatorDiversityTable;
        responseData = {
            success: true,
            data: window.AppState.rawOperationData,
            date: window.AppState.currentOperationDate
        };
    } else if (tableId === 'spuListingTable') {
        filters = window.AppState.spuListingFilters;
        updateFunction = updateSpuListingTable;
        responseData = {
            success: true,
            data: window.AppState.rawOperationData,
            date: window.AppState.currentOperationDate
        };
    } else if (tableId === 'fixedTable1') {
        filters = window.AppState.fixedTable1Filters;
        updateFunction = updateFixedTable1;
        responseData = {
            success: true,
            data: window.AppState.fixedTable1Data, // 传递已处理的数据
            date: '固定数据'
        };
    } else if (tableId === 'fixedTable2') {
        filters = window.AppState.fixedTable2Filters;
        updateFunction = updateFixedTable2;
        responseData = {
            success: true,
            data: window.AppState.fixedTable2Data, // 传递已处理的数据
            date: '固定数据'
        };
    } else {
        return; // Unknown table
    }

    filters[column] = query; // Update filter state

    // Trigger the update function for the specific table
    updateFunction(responseData);
}


// Debounce function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func.apply(this, args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 应用运营多样性过滤器
function applyOperatorDiversityFilters(data, filters) {
    if (!data) return []; // Handle null or undefined data
    if (!filters || Object.keys(filters).filter(k => filters[k]).length === 0) {
        return data; // No active filters applied
    }

    const nameFilter = filters.name ? filters.name.toLowerCase() : '';
    const idFilter = filters.id ? filters.id.toLowerCase() : '';

    return data.map(operator => {
        // Filter shops
        const filteredShopsEntries = Object.entries(operator.shops).map(([shopName, shopData]) => {
            // Filter SPUs
            const filteredSpusEntries = Object.entries(shopData.spus).map(([spuName, spuData]) => {
                // Filter Product IDs
                const filteredIds = idFilter ? (spuData.商品ID列表 || []).filter(id => String(id).toLowerCase().includes(idFilter)) : (spuData.商品ID列表 || []);

                // Check if SPU name matches OR any ID matches (if ID filter is active)
                const spuNameMatches = spuName.toLowerCase().includes(nameFilter);
                const idMatches = idFilter && filteredIds.length > 0; // Only true if ID filter active and found IDs
                const spuMatches = spuNameMatches || idMatches || (!nameFilter && !idFilter); // Match if name matches, or ID matches (if filtering by ID), or no filters active for this level


                if (!spuMatches && (nameFilter || idFilter)) return null; // SPU doesn't match active filters

                return [spuName, { ...spuData, 商品ID列表: filteredIds, 商品数量: idFilter ? filteredIds.length : spuData.商品数量 }]; // Return filtered SPU with updated count/IDs only if ID filter is active
            }).filter(entry => entry !== null); // Remove non-matching SPUs

            const filteredSpus = Object.fromEntries(filteredSpusEntries);

            // Check if shop name matches OR any SPU remains after filtering
            const shopNameMatches = shopName.toLowerCase().includes(nameFilter);
            const hasRemainingSpus = Object.keys(filteredSpus).length > 0;
             const shopMatches = shopNameMatches || hasRemainingSpus || !nameFilter; // Match if name matches, or SPUs remain, or no name filter active

            if (!shopMatches && nameFilter) return null; // Shop doesn't match active name filter

            // Recalculate shop totals based on filtered SPUs
            let shopTotalLinks = 0;
            filteredSpusEntries.forEach(([spuName, spuData]) => {
                 // Use filtered count if ID filter is active, otherwise use original count
                shopTotalLinks += spuData.商品数量;
            });


            return [shopName, { ...shopData, spus: filteredSpus, totalLinks: shopTotalLinks, totalSpus: Object.keys(filteredSpus).length }];

        }).filter(entry => entry !== null); // Remove non-matching shops

        const filteredShops = Object.fromEntries(filteredShopsEntries);

        // Check if operator name matches OR any shop remains after filtering
        const operatorNameMatches = operator.operatorName.toLowerCase().includes(nameFilter);
        const hasRemainingShops = Object.keys(filteredShops).length > 0;
        const operatorMatches = operatorNameMatches || hasRemainingShops || !nameFilter; // Match if name matches, or shops remain, or no name filter active


        if (!operatorMatches && nameFilter) return null; // Operator doesn't match active name filter

        // Recalculate operator totals
        let opTotalLinks = 0;
        const opDistinctSpus = new Set();
        filteredShopsEntries.forEach(([_, shopData]) => {
            opTotalLinks += shopData.totalLinks;
            Object.keys(shopData.spus).forEach(spu => opDistinctSpus.add(spu));
        });

        return { ...operator, shops: filteredShops, totalLinks: opTotalLinks, totalShops: Object.keys(filteredShops).length, totalSpus: opDistinctSpus.size };

    }).filter(op => op !== null); // Remove non-matching operators
}


// 应用SPU分布过滤器
function applySpuListingFilters(data, filters) {
    if (!data) return []; // Handle null or undefined data
    if (!filters || Object.keys(filters).filter(k => filters[k]).length === 0) {
        return data; // No active filters applied
    }

    const spuFilter = filters.spu ? filters.spu.toLowerCase() : '';
    const operatorFilter = filters.operator ? filters.operator.toLowerCase() : '';
    const idFilter = filters.id ? filters.id.toLowerCase() : '';

    return data.map(spu => {
        // Filter operators
        const filteredOperatorsEntries = Object.entries(spu.operators).map(([operatorName, opData]) => {
            // Filter Product IDs within the operator
             const filteredIds = idFilter ? (opData.productIds || []).filter(id => String(id).toLowerCase().includes(idFilter)) : (opData.productIds || []);

             // Operator matches if name matches OR it has matching IDs (if filtering by ID)
            const operatorNameMatches = operatorName.toLowerCase().includes(operatorFilter);
            const hasMatchingIds = idFilter && filteredIds.length > 0;
            const operatorMatches = operatorNameMatches || hasMatchingIds || (!operatorFilter && !idFilter); // Match if name matches, or IDs match, or no filters active


            if (!operatorMatches && (operatorFilter || idFilter)) return null; // Operator doesn't match active filters

            // Return the operator entry with potentially filtered IDs
             return [operatorName, { ...opData, productIds: filteredIds, links: idFilter ? filteredIds.length : opData.links }]; // Update link count based on filtered IDs only if ID filter is active

        }).filter(entry => entry !== null); // Remove non-matching operators

        const filteredOperators = Object.fromEntries(filteredOperatorsEntries);

        // Check if SPU name matches OR any operator remains after filtering
        const spuNameMatches = spu.spuName.toLowerCase().includes(spuFilter);
        const hasRemainingOperators = Object.keys(filteredOperators).length > 0;
        const spuMatches = spuNameMatches || hasRemainingOperators || !spuFilter; // Match if name matches, or operators remain, or no SPU filter


        if (!spuMatches && spuFilter) return null; // SPU doesn't match active SPU filter

        // Recalculate totals based on filtered operators
        let filteredTotalLinks = 0;
        filteredOperatorsEntries.forEach(([_, opData]) => {
            filteredTotalLinks += opData.links; // Use the potentially updated link count
        });

        return { ...spu, operators: filteredOperators, totalOperators: Object.keys(filteredOperators).length, totalLinks: filteredTotalLinks };

    }).filter(spu => spu !== null); // Remove non-matching SPUs
}

// 应用固定数据表格1过滤器 (使用与运营多样性表格相同的逻辑)
function applyFixedTable1Filters(data, filters) {
    return applyOperatorDiversityFilters(data, filters);
}

// 应用固定数据表格2过滤器 (使用与SPU分布表格相同的逻辑)
function applyFixedTable2Filters(data, filters) {
    return applySpuListingFilters(data, filters);
}

// --- 导出函数 ---

// 导出运营多样性数据为CSV
function exportOperatorDiversityData() {
    // Use the currently displayed (potentially filtered) data for export
    const dataToExport = applyOperatorDiversityFilters(window.AppState.operatorDiversityData, window.AppState.operatorDiversityFilters);

    if (!dataToExport || dataToExport.length === 0) {
        alert('暂无数据可导出 (可能已被筛选)');
        return;
    }

    const date = window.AppState.currentOperationDate;

    try {
        let csvContent = '运营,店铺,SPU,商品ID,链接数(SPU),日期\n';

        dataToExport.forEach(operator => {
            const operatorName = operator.operatorName;
            Object.entries(operator.shops).forEach(([shopName, shopData]) => {
                Object.entries(shopData.spus).forEach(([spuName, spuData]) => {
                    const linkCount = spuData.商品数量 || 0;
                    if (spuData.商品ID列表 && spuData.商品ID列表.length > 0) {
                        spuData.商品ID列表.forEach(productId => {
                            const safeOperator = `"${operatorName.replace(/"/g, '""')}"`;
                            const safeShop = `"${shopName.replace(/"/g, '""')}"`;
                            const safeSpu = `"${spuName.replace(/"/g, '""')}"`;
                            const safeId = `"${String(productId).replace(/"/g, '""')}"`;
                            const safeDate = `"${date.replace(/"/g, '""')}"`;
                            csvContent += `${safeOperator},${safeShop},${safeSpu},${safeId},${linkCount},${safeDate}\n`;
                        });
                    } else if (linkCount > 0) { // Include row even if IDs were filtered out but count > 0
                         const safeOperator = `"${operatorName.replace(/"/g, '""')}"`;
                         const safeShop = `"${shopName.replace(/"/g, '""')}"`;
                         const safeSpu = `"${spuName.replace(/"/g, '""')}"`;
                         const safeDate = `"${date.replace(/"/g, '""')}"`;
                         csvContent += `${safeOperator},${safeShop},${safeSpu},"",${linkCount},${safeDate}\n`;
                    } else if (!idFilterActive(window.AppState.operatorDiversityFilters)) { // Include if no ID filter active
                         const safeOperator = `"${operatorName.replace(/"/g, '""')}"`;
                         const safeShop = `"${shopName.replace(/"/g, '""')}"`;
                         const safeSpu = `"${spuName.replace(/"/g, '""')}"`;
                         const safeDate = `"${date.replace(/"/g, '""')}"`;
                         csvContent += `${safeOperator},${safeShop},${safeSpu},"",${linkCount},${safeDate}\n`;
                    }
                });
            });
        });

        let encodedUri = "data:text/csv;charset=utf-8,\uFEFF" + encodeURIComponent(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        const safeDateString = date.replace(/[\s至,]/g, '_').replace(/_+/g, '_');
        const fileName = `运营多样性数据_${safeDateString}.csv`;
        link.setAttribute("download", fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        console.error('导出运营多样性数据失败:', error);
        alert('导出数据失败：' + error.message);
    }
}

// 导出SPU分布数据为CSV
function exportSpuListingData() {
     // Use the currently displayed (potentially filtered) data
    const dataToExport = applySpuListingFilters(window.AppState.spuListingData, window.AppState.spuListingFilters);

    if (!dataToExport || dataToExport.length === 0) {
        alert('暂无SPU数据可导出 (可能已被筛选)');
        return;
    }
    const date = window.AppState.currentOperationDate;

    try {
        let csvContent = 'SPU,运营,链接数,商品ID列表,日期\n';
        dataToExport.forEach(spu => {
            Object.entries(spu.operators).forEach(([opName, opData]) => {
                const safeSpu = `"${spu.spuName.replace(/"/g, '""')}"`;
                const safeOp = `"${opName.replace(/"/g, '""')}"`;
                const links = opData.links; // Use filtered link count
                const ids = `"${(opData.productIds || []).join(', ').replace(/"/g, '""')}"`; // Join filtered IDs
                const safeDate = `"${date.replace(/"/g, '""')}"`;
                if (links > 0 || !idFilterActive(window.AppState.spuListingFilters)) { // Only include row if links > 0 or no ID filter active
                    csvContent += `${safeSpu},${safeOp},${links},${ids},${safeDate}\n`;
                }
            });
        });

        let encodedUri = "data:text/csv;charset=utf-8,\uFEFF" + encodeURIComponent(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        const safeDateString = date.replace(/[\s至,]/g, '_').replace(/_+/g, '_');
        const fileName = `SPU上架数据_${safeDateString}.csv`;
        link.setAttribute("download", fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        console.error('导出SPU数据失败:', error);
        alert('导出数据失败：' + error.message);
    }
}

// Helper to check if ID filter is active
function idFilterActive(filters) {
    return filters && filters.id && filters.id.length > 0;
}

// --- 辅助函数 ---

// 显示加载指示器
function showLoadingIndicator() {
    let indicator = document.getElementById('loading-indicator');
    if (!indicator) {
        indicator = document.createElement('div');
        indicator.id = 'loading-indicator';
        indicator.innerHTML = '<div class="spinner"></div> 加载中...';
        // 基本样式，可以在 CSS 中进一步美化
        indicator.style.position = 'fixed';
        indicator.style.top = '0';
        indicator.style.left = '0';
        indicator.style.width = '100%';
        indicator.style.height = '100%';
        indicator.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
        indicator.style.display = 'flex';
        indicator.style.justifyContent = 'center';
        indicator.style.alignItems = 'center';
        indicator.style.zIndex = '9999';
        indicator.style.fontSize = '1.2em';
        document.body.appendChild(indicator);

         // Spinner CSS (simple example)
        const style = document.createElement('style');
        style.textContent = `
        :root {
            --primary-color: #2563eb;
            --primary-light: #dbeafe;
            --primary-dark: #1e40af;
            --error-color: #dc2626;
            --error-light: #fee2e2;
            --error-border: #fecaca;
            --success-color: #059669;
            --success-light: #d1fae5;
            --success-border: #a7f3d0;
        }

        .spinner {
          border: 4px solid var(--primary-light);
          width: 36px;
          height: 36px;
          border-radius: 50%;
          border-left-color: var(--primary-color);
          animation: spin 1s ease infinite;
          margin-right: 10px;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        .error-message {
            color: var(--error-color);
            padding: 20px;
            text-align: center;
            font-weight: bold;
            border: 1px solid var(--error-border);
            background-color: var(--error-light);
            margin: 20px;
            border-radius: 6px;
        }
      `;
        document.head.appendChild(style);

    }
    indicator.style.display = 'flex';
}

// 隐藏加载指示器
function hideLoadingIndicator() {
    const indicator = document.getElementById('loading-indicator');
    if (indicator) {
        indicator.style.display = 'none';
    }
}

// 添加自定义样式
function addCustomStyles() {
    // 防止重复添加样式
    if (document.getElementById('product-management-styles')) {
        return;
    }

    const styleElement = document.createElement('style');
    styleElement.id = 'product-management-styles';
    styleElement.textContent = `
        /* 整体配色方案 - 现代化风格 */
        :root {
            /* 主色调 - 采用更现代的蓝紫色调 */
            --primary-color: #6366f1; /* Indigo 500 */
            --primary-dark: #4338ca;  /* Indigo 700 */
            --primary-light: #e0e7ff; /* Indigo 100 */
            --primary-lighter: #eef2ff; /* Indigo 50 */

            /* 辅助色 */
            --secondary-color: #64748b; /* Slate 500 */
            --secondary-light: #f1f5f9; /* Slate 100 */

            /* 所有表格统一使用这个主色调 */
            --accent-color: #6366f1;   /* 与primary-color相同 */
            --accent-dark: #4338ca;    /* 与primary-dark相同 */
            --accent-light: #e0e7ff;   /* 与primary-light相同 */
            --accent-border: rgba(99, 102, 241, 0.3);  /* 半透明边框 */

            /* 文字色 */
            --text-color: #1e293b; /* Slate 800 */
            --text-light: #475569; /* Slate 600 */
            --text-muted: #94a3b8; /* Slate 400 */

            /* 界面元素 */
            --bg-color: #f8fafc; /* Slate 50 */
            --border-color: #e2e8f0; /* Slate 200 */
            --hover-color: #f1f5f9; /* Slate 100 - slightly different hover */
            --shadow-color: rgba(99, 102, 241, 0.1); /* Primary color shadow */
            --section-bg: #ffffff;

            /* 状态色 */
            --success-color: #10b981; /* Emerald 500 */
            --warning-color: #f59e0b; /* Amber 500 */
            --danger-color: #ef4444;  /* Red 500 */

            /* 高亮色 - 用于新上数据 */
            --highlight-color: #f59e0b; /* Amber 500 */
            --highlight-bg: #fffbeb;   /* Amber 50 */

            /* 渐变 */
            --gradient-start: var(--primary-color);
            --gradient-end: #8b5cf6; /* Violet 500 */
        }

        body {
            background-color: var(--bg-color);
            margin: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-color);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            line-height: 1.5;
        }

        .container {
            max-width: 100%;
            margin: 24px auto;
            padding: 0 24px;
        }

        /* 页面头部样式 */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .header h1 {
            margin: 0;
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--primary-dark);
        }

         /* Data Section Styling */
        .data-section {
            margin-bottom: 30px;
            background-color: var(--section-bg);
            border-radius: 8px;
            box-shadow: 0 1px 3px var(--shadow-color);
            border: 1px solid var(--border-color);
            overflow: hidden; /* Prevent content overflow */
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px; /* Slightly reduced padding */
            border-bottom: 1px solid var(--border-color);
             background-color: var(--primary-lighter); /* Header background */
        }

        .title-container {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-grow: 1; /* Allow title to take space */
        }

        .title-container h2 {
            margin: 0;
            font-size: 1.15rem; /* Adjusted size */
            font-weight: 600;
            color: var(--primary-dark); /* Darker title */
        }

        .date-display {
            font-size: 0.85rem; /* Adjusted size */
            color: var(--text-light);
            font-weight: normal;
             background-color: #fff;
             padding: 2px 6px;
             border-radius: 4px;
             border: 1px solid var(--border-color);
        }

        .toggle-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px; /* Adjusted size */
            font-size: 0.8rem; /* Adjusted size */
            cursor: pointer;
            transition: background-color 0.2s;
             white-space: nowrap;
             margin-left: 10px;
        }

        .toggle-button:hover {
            background-color: var(--primary-dark);
        }

        /* Controls within section header */
        .section-controls {
           display: flex;
           align-items: center;
           gap: 16px;
           flex-shrink: 0; /* Prevent controls from shrinking too much */
        }

        /* Date Selector Styles */
        .date-selector {
            display: flex;
            align-items: center;
            gap: 8px;
             background-color: #fff;
             padding: 4px 8px;
             border-radius: 4px;
             border: 1px solid var(--border-color);
        }
        .date-selector label {
             font-size: 0.85rem;
             color: var(--text-light);
        }

        .date-range-type {
            padding: 6px 10px; /* Adjusted size */
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 0.85rem; /* Adjusted size */
            background-color: white;
             margin-left: 4px;
        }

        .date-input-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .operation-date-picker,
        .date-range-picker {
            padding: 6px 10px; /* Adjusted size */
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 0.85rem; /* Adjusted size */
             max-width: 130px; /* Limit width */
        }

        .operation-query-btn,
        .section-export-btn {
            padding: 7px 14px; /* Adjusted size */
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 0.85rem; /* Adjusted size */
            cursor: pointer;
            transition: background-color 0.2s;
             white-space: nowrap;
        }

        .operation-query-btn:hover {
            background-color: var(--primary-dark);
        }
         .section-export-btn {
            background-color: var(--secondary-color); /* Different color for export */
         }
         .section-export-btn:hover {
            background-color: var(--text-light);
         }


        /* 表格容器内填充和边距调整 */
        .section-content {
           /* 原有样式 */
           overflow: hidden; /* Important for toggle */
           transition: max-height 0.3s ease-out; /* Smoother transition */
           max-height: 2000px; /* Default max height, adjust as needed */
           /* 新增样式 */
           padding: 0; /* 移除填充以适配滚动容器 */
        }
        .section-content.hidden {
           max-height: 0;
        }
        
        /* 为每个表格增加边距与分隔 */
        .data-table-container {
           /* 原有样式保持不变 */
           /* 新增样式 */
           border: 1px solid var(--border-color);
           border-top: none; /* 顶部边框已由Section提供 */
           margin-bottom: 0;
           
           /* 滚动条美化 */
           scrollbar-width: thin; /* Firefox */
           scrollbar-color: var(--primary-light) var(--bg-color); /* Firefox */
        }
        
        /* Webkit滚动条美化 (Chrome, Safari, Edge) */
        .data-table-container::-webkit-scrollbar {
           width: 8px;
           height: 8px;
        }
        .data-table-container::-webkit-scrollbar-track {
           background: var(--bg-color);
        }
        .data-table-container::-webkit-scrollbar-thumb {
           background-color: var(--primary-light);
           border-radius: 4px;
           border: 2px solid var(--bg-color);
        }
        .data-table-container::-webkit-scrollbar-thumb:hover {
           background-color: var(--primary-color);
        }
        
        /* 确保表头在滚动时始终可见 */
        .data-table thead {
           position: sticky;
           top: 0;
           z-index: 6;
           background-color: var(--primary-lighter);
        }
        
        /* 空状态和加载状态调整 */
        .section-content .empty-state,
        .section-content .loading {
           max-height: 200px; /* 限制空状态高度 */
           overflow: hidden;
           margin: 0;
        }

        /* 通用表格容器样式 */
        .data-table-container {
            overflow-x: auto;
            width: 100%;
            /* box-shadow: 0 2px 4px var(--shadow-color); */ /* Shadow removed, section has shadow */
            max-height: 450px; /* 限制表格高度 */
            overflow-y: auto; /* 添加垂直滚动条 */
            position: relative; /* 为固定表头做准备 */
        }

        /* 通用表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
            flex-grow: 1;
            background-color: #ffffff;
            /* border-radius: 8px; */ /* No radius needed, section has it */
            min-width: 800px; /* 确保表格有最小宽度 */
            border-top: 1px solid var(--border-color); /* Separator line */
        }

        /* 表格 Header Search Input */
        .header-search {
            padding: 4px 8px;
            margin-left: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 0.8rem;
            width: 120px; /* Adjust as needed */
            font-weight: normal;
        }
        th .header-search { /* Specificity */
           vertical-align: middle;
        }

        /* 表格头样式 (通用) */
        .data-table th {
            background-color: var(--primary-lighter);
            color: var(--primary-dark);
            padding: 10px 10px; /* Adjusted padding */
            text-align: left;
            font-weight: 600;
            font-size: 0.85rem;
            border-bottom: 1px solid var(--primary-light);
            position: sticky;
            top: 0;
            z-index: 5;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05); /* 表头阴影，使其看起来像是浮在表格上方 */
        }
         /* Right align specific headers */
        .daily-operation-table th:nth-child(3),
        .daily-operation-table th:nth-child(4),
        .daily-operation-table th:nth-child(5),
        .spu-listing-table th:nth-child(4) {
            text-align: right;
        }


        /* 表格单元格样式 (通用) */
        .data-table td {
            padding: 9px 10px; /* Adjusted padding */
            border-bottom: 1px solid var(--border-color);
            font-size: 0.875rem;
            vertical-align: middle;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: var(--text-light);
        }
        .data-table .level-1 td:not(.expand-control) { /* Darker text for top level */
            color: var(--text-color);
        }


        /* 右对齐数字列 */
        .data-table .number-cell {
            text-align: right;
            font-family: 'Roboto Mono', monospace; /* Monospace for numbers */
            font-size: 0.85rem;
        }

        /* 商品ID列表换行 */
        .data-table td.product-id-cell {
            white-space: normal;
            vertical-align: top;
            line-height: 1.4;
        }

        /* 表格行悬停效果 */
        .data-table tbody tr:hover {
            background-color: var(--hover-color);
        }

        /* 商品ID徽章样式 */
        .product-id-list-inline {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            padding-top: 2px;
        }

        .product-id-badge {
            display: inline-block;
            background-color: var(--accent-light);
            color: var(--accent-dark);
            padding: 1px 5px; /* Slightly smaller */
            border-radius: 4px;
            font-size: 0.75rem;
            margin: 1px 0;
            /* border: 1px solid var(--accent-border); */ /* Removed border */
            max-width: 110px; /* Adjusted */
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: 'Roboto Mono', monospace;
             white-space: nowrap;
        }
         .product-id-badge:hover {
             background-color: var(--accent-color);
             color: white;
         }

        /* 表格层级样式 */
        .data-table .level-1 {
            background-color: var(--primary-light);
            font-weight: 600;
        }
        .data-table .level-1 td { /* Ensure level 1 text is readable */
            color: var(--primary-dark);
        }
        .data-table .level-1 .number-cell {
             color: var(--primary-dark); /* Keep number color consistent */
        }


        .data-table .level-2 {
            background-color: var(--primary-lighter); /* Lighter than level 1 */
        }

        .data-table .level-3 {
            background-color: #ffffff; /* Normal background */
            font-size: 0.85rem;
        }
        .data-table .level-2 td,
        .data-table .level-3 td {
             color: var(--text-light);
        }
        .data-table .level-2 .number-cell,
        .data-table .level-3 .number-cell {
             color: var(--text-light);
        }

        /* Expand icon alignment */
        .expand-control {
            text-align: center;
            vertical-align: middle;
            padding: 5px !important;
            width: 40px;
        }

        .expand-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 18px; /* Slightly smaller */
            height: 18px;
            font-size: 0.65rem; /* Smaller icon */
            color: var(--primary-color);
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.7); /* White background */
            /* border: 1px solid var(--primary-light); */
            transition: all 0.2s ease;
            cursor: pointer;
             box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .expand-icon.expanded {
            /* transform: rotate(90deg); */ /* Keep rotation */
            background-color: var(--primary-color);
            color: white;
        }
         tr:hover .expand-icon {
             background-color: white;
         }
         tr:hover .expand-icon.expanded {
             background-color: var(--primary-dark);
             color: white;
         }


        /* 缩进和连接线样式 */
        .indent-1 {
            padding-left: 30px !important;
            position: relative;
        }
        .indent-2 {
            padding-left: 55px !important;
            position: relative;
        }
        /* Connector lines */
        .indent-1::before, .indent-2::before {
            content: '';
            position: absolute;
            top: -10px; /* Extend line slightly above */
            bottom: 0;
            width: 1px;
            background-color: var(--accent-border);
            z-index: 1;
        }
        .indent-1::before { left: 12px; }
        .indent-2::before { left: 37px; }

        .indent-1::after, .indent-2::after {
            content: '';
            position: absolute;
            top: 50%;
            height: 1px;
            background-color: var(--accent-border);
            width: 10px;
            transform: translateY(-50%);
            z-index: 1;
        }
        .indent-1::after { left: 12px; }
        .indent-2::after { left: 37px; }
         /* Ensure text is above lines */
        .shop-name, .spu-name {
            position: relative;
            z-index: 2;
            background: inherit; /* Match row background to cover line */
            padding-right: 5px; /* Space before next cell */
        }


        /* Loading and Empty/Error States */
        .loading, .empty-state {
            padding: 30px 20px; /* Adjusted padding */
            text-align: center;
            color: var(--text-light);
            font-style: italic;
            /* background-color: var(--bg-color); */ /* Removed background, part of section now */
            /* border-radius: 10px; */
            margin: 0; /* No margin needed */
            /* border: 1px dashed var(--border-color); */
             min-height: 100px;
             display: flex;
             align-items: center;
             justify-content: center;
             font-size: 0.9rem;
        }
        .empty-state.error-state {
            color: var(--danger-color);
            background-color: rgba(254, 226, 226, 0.3); /* Lighter error bg */
            /* border: 1px solid rgba(239, 68, 68, 0.2); */
            font-style: normal;
            font-weight: 500;
        }

        /* 修复文本显示 */
        .text-muted {
            color: var(--text-muted);
             font-size: 0.85rem;
        }

        /* 隐藏行 */
        .hidden-row {
            display: none !important;
        }

        /* Responsive Adjustments */
        @media (max-width: 992px) {
           .section-header {
               flex-wrap: wrap; /* Allow controls to wrap */
               gap: 10px;
           }
            .section-controls {
                 width: 100%; /* Make controls take full width on wrap */
                 justify-content: flex-end; /* Align controls to right */
             }
             /* Adjust date selector wrap */
            .date-selector {
                 flex-wrap: wrap;
             }
            
            /* 调整表格容器高度 */
            .data-table-container {
                max-height: 400px; /* 适中高度 */
            }
        }

        @media (max-width: 768px) {
            .container { padding: 0 12px; margin-top: 16px;}
            .header h1 { font-size: 1.5rem; }

            .section-header {
                padding: 10px 12px;
            }
            .title-container h2 { font-size: 1.05rem; }
            .date-display { font-size: 0.75rem; padding: 2px 4px;}
            .toggle-button { font-size: 0.75rem; padding: 4px 8px;}

             .section-controls {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
                 margin-top: 10px; /* Space when wrapped */
             }

            .date-selector {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
                 padding: 8px;
                 border: none;
                 background: none;
            }
            .date-selector label { width: 100%; margin-bottom: 4px;}
            .date-range-type { width: 100%; }

            .date-input-container {
                 width: 100%;
                 gap: 6px;
                 justify-content: space-between;
            }
            .date-input-container span { font-size: 0.8rem; }
             .operation-date-picker,
            .date-range-picker {
                width: 100%; /* Full width */
                 padding: 7px 10px;
                 font-size: 0.85rem;
                 box-sizing: border-box;
            }
             #rangeDateContainer .date-range-picker {
                 width: calc(50% - 10px); /* Adjust for gap */
             }


            .operation-query-btn, .section-export-btn {
                width: 100%;
                padding: 9px 16px; /* Larger touch target */
                 font-size: 0.9rem;
            }

            .data-table th, .data-table td {
                font-size: 0.75rem; /* Smaller font */
                padding: 7px 5px; /* Tighter padding */
            }
            .header-search { width: 80px; font-size: 0.75rem;}

            .indent-1 { padding-left: 20px !important; }
            .indent-2 { padding-left: 35px !important; }
            .indent-1::before { left: 8px; }
            .indent-2::before { left: 23px; }
            .indent-1::after, .indent-2::after { display: none; }

            .product-id-badge {
                max-width: 70px;
                font-size: 0.7rem;
                 padding: 1px 4px;
            }
             .expand-icon { width: 16px; height: 16px; font-size: 0.6rem;}
             .expand-control { width: 30px;}
             
             /* 调整表格容器高度 - 移动设备 */
             .data-table-container {
                 max-height: 350px; /* 较小屏幕高度 */
             }
        }
        
        /* 添加表格滚动后阴影效果，提升用户体验 */
        .data-table-container {
            background: 
                /* 顶部阴影 - 当内容向下滚动时显示 */
                linear-gradient(to bottom, rgba(0,0,0,0.05) 0%, rgba(0,0,0,0) 5%),
                /* 底部阴影 - 当内容可向上滚动时显示 */
                linear-gradient(to top, rgba(0,0,0,0.05) 0%, rgba(0,0,0,0) 5%);
            background-attachment: local, local;
            background-repeat: no-repeat;
        }
    `;
    document.head.appendChild(styleElement);
}

// --- 行展开/折叠 ---

// 切换运营多样性表格行 (Operator/Shop)
function toggleOperationRow(iconElement) {
    if (!iconElement) return;

    const row = iconElement.closest('tr');
    if (!row) return;

    const level = parseInt(row.dataset.level, 10);
    const isExpanded = iconElement.classList.contains('expanded');

    // 找到所有直接子行
    let childRows = [];
    const tableBody = row.parentNode;

    if (level === 1) { // 运营层级 -> 店铺行
        const operatorId = row.dataset.operatorId;
        childRows = Array.from(tableBody.querySelectorAll(`tr[data-operator-id="${operatorId}"][data-level="2"]`));
    } else if (level === 2) { // 店铺层级 -> SPU行
        const shopId = row.dataset.shopId;
        childRows = Array.from(tableBody.querySelectorAll(`tr[data-shop-id="${shopId}"][data-level="3"]`));
    } else {
        return; // Level 3 has no children to expand/collapse
    }


    if (isExpanded) {
        // 收起
        iconElement.classList.remove('expanded');
        iconElement.textContent = '▶';

        // 隐藏所有子孙行
        const rowsToHide = Array.from(tableBody.querySelectorAll(`tr[data-operator-id="${row.dataset.operatorId}"]`))
                               .filter(r => parseInt(r.dataset.level) > level && r.dataset.shopId?.startsWith(row.dataset.shopId || '')); // More robust descendant finding


        rowsToHide.forEach(childRow => {
             // Also reset expand state of children when collapsing parent
            const childIcon = childRow.querySelector('.expand-icon');
            if (childIcon && childIcon.classList.contains('expanded')) {
                 childIcon.classList.remove('expanded');
                 childIcon.textContent = '▶';
            }
            childRow.classList.add('hidden-row');
        });

    } else {
        // 展开
        iconElement.classList.add('expanded');
        iconElement.textContent = '▼';

        // 只显示直接子行
        childRows.forEach(childRow => {
            childRow.classList.remove('hidden-row');
            // Ensure grandchildren (level 3) remain hidden unless their parent (level 2) is expanded
            if (level === 1) { // If expanding level 1, ensure level 3 are initially hidden
                const grandChildRows = tableBody.querySelectorAll(`tr[data-shop-id="${childRow.dataset.shopId}"][data-level="3"]`);
                grandChildRows.forEach(gc => gc.classList.add('hidden-row'));
                 // Reset expand icon for level 2 rows being shown
                const childIcon = childRow.querySelector('.expand-icon');
                if (childIcon) {
                    childIcon.classList.remove('expanded');
                    childIcon.textContent = '▶';
                }
            }
        });
    }
}


// 切换SPU分布表格行 (SPU)
function toggleSpuRow(iconElement) {
    if (!iconElement) return;
    const row = iconElement.closest('tr');
    if (!row) return;
    const spuId = row.dataset.spuId;
    const isExpanded = iconElement.classList.contains('expanded');
    const childRows = document.querySelectorAll(`tr[data-spu-id="${spuId}"][data-level="2"]`); // Find operator rows under this SPU

    if (isExpanded) {
        iconElement.classList.remove('expanded');
        iconElement.textContent = '▶';
        Array.from(childRows).forEach(childRow => childRow.classList.add('hidden-row'));
    } else {
        iconElement.classList.add('expanded');
        iconElement.textContent = '▼';
        Array.from(childRows).forEach(childRow => childRow.classList.remove('hidden-row'));
    }
}

// --- 区域可见性切换 ---

// 切换区域内容的显示/隐藏
function toggleSectionVisibility(contentId, buttonSelector) {
    const content = document.getElementById(contentId);
    const button = document.querySelector(buttonSelector);
    if (!content || !button) return;
    const isHidden = content.classList.contains('hidden');
    if (isHidden) {
        content.classList.remove('hidden');
        button.textContent = '隐藏';
        // content.style.maxHeight = content.scrollHeight + "px"; // Animate open
    } else {
        content.classList.add('hidden');
        button.textContent = '显示';
        // content.style.maxHeight = "0"; // Animate close
    }
}

// 为每个区域绑定切换函数
function toggleOperatorDiversityVisibility() {
    toggleSectionVisibility('operatorDiversityContainerContent', '.data-section#operatorDiversityContainer .toggle-button');
}
function toggleSpuListingVisibility() {
    toggleSectionVisibility('spuListingContainerContent', '.data-section#spuListingContainer .toggle-button');
}

// 切换固定数据表格1显示/隐藏
function toggleFixedTable1Visibility() {
    toggleSectionVisibility('fixedTable1ContainerContent', '.data-section#fixedTable1Container .toggle-button');
}

// 切换固定数据表格2显示/隐藏
function toggleFixedTable2Visibility() {
    toggleSectionVisibility('fixedTable2ContainerContent', '.data-section#fixedTable2Container .toggle-button');
}

// 导出固定数据表格1
function exportFixedTable1Data() {
    alert('固定数据表1导出功能待实现');
}

// 导出固定数据表格2
function exportFixedTable2Data() {
    alert('固定数据表2导出功能待实现');
}

// --- Global Function Exposure ---
window.toggleOperationRow = toggleOperationRow;
window.toggleSpuRow = toggleSpuRow;
window.toggleOperatorDiversityVisibility = toggleOperatorDiversityVisibility;
window.toggleSpuListingVisibility = toggleSpuListingVisibility;
window.exportOperatorDiversityData = exportOperatorDiversityData;
window.exportSpuListingData = exportSpuListingData;
window.handleQueryButtonClick = handleQueryButtonClick; // Expose query handler
window.toggleFixedTable1Visibility = toggleFixedTable1Visibility;
window.toggleFixedTable2Visibility = toggleFixedTable2Visibility;
window.exportFixedTable1Data = exportFixedTable1Data;
window.exportFixedTable2Data = exportFixedTable2Data;

// --- Initial Load ---
document.addEventListener('DOMContentLoaded', async () => {
    if (!checkLoginStatus()) return;
    showLoadingIndicator();
    try {
        // 并行加载数据
        const [shopOperatorRawData, initialOperationResponse, fixedDataResponse] = await Promise.all([
            loadShopOperatorData(),
            loadDailyOperationData(), // 默认加载昨日数据
            loadFixedData() // 加载固定数据
        ]);

        // 创建店铺-运营映射表
        const shopOperatorMap = {};
        Object.entries(shopOperatorRawData).forEach(([shopName, data]) => {
            shopOperatorMap[shopName] = data.operator || '未知运营';
        });

        // 初始化 AppState
        window.AppState.init(shopOperatorMap, initialOperationResponse.data, initialOperationResponse.date || '昨日');

        // 处理并存储固定数据
        if (fixedDataResponse && fixedDataResponse.success) {
            window.AppState.fixedTable1Data = processFixedTable1Data(fixedDataResponse.data, shopOperatorMap);
            window.AppState.fixedTable2Data = processFixedTable2Data(fixedDataResponse.data, shopOperatorMap);
        }

        // 构建UI框架
        buildUI();

        // 填充表格数据
        updateOperatorDiversityTable(initialOperationResponse);
        updateSpuListingTable(initialOperationResponse);
        updateFixedTable1(fixedDataResponse);
        updateFixedTable2(fixedDataResponse);

        // 初始设置搜索监听
        setupTableSearch('operatorDiversityTable');
        setupTableSearch('spuListingTable');
        setupTableSearch('fixedTable1');
        setupTableSearch('fixedTable2');

    } catch (error) {
        console.error("初始化页面失败:", error);
        const container = document.querySelector('.container');
        if (container) {
            container.innerHTML = `<div class="error-message">页面加载失败，请刷新重试或联系管理员。错误：${error.message}</div>`;
        }
    } finally {
        hideLoadingIndicator();
    }
});

// 加载前日运营数据 (用于比较计算新上数据 - 如果需要的话)
async function loadPreviousDayOperationData() {
    try {
        // 获取前日日期 (昨天的前一天)
        const prevDate = new Date();
        prevDate.setDate(prevDate.getDate() - 2); // 昨天是-1，前天是-2
        const formattedDate = prevDate.toISOString().split('T')[0]; // 格式为 YYYY-MM-DD

        const url = `/api/daily-operation-data?date=${formattedDate}`;
        const response = await fetch(url);

        if (!response.ok) {
            // Don't throw, just return unsuccessful
            console.warn(`加载前日(${formattedDate})数据失败: ${response.status}`);
            return { success: false, message: `HTTP error! status: ${response.status}` };
        }
        return await response.json();
    } catch (error) {
        console.error('加载前日运营数据失败:', error);
        return { success: false, message: error.message };
    }
}

// Helper to re-setup search listeners inside update functions
function reattachSearchListeners(tableId) {
    requestAnimationFrame(() => {
        setupTableSearch(tableId);
    });
}

// Modify updateOperatorDiversityTable and updateSpuListingTable
// Add reattachSearchListeners call after setting innerHTML

function updateOperatorDiversityTable(response, queryDesc = null) {
    const container = document.getElementById('operatorDiversityContainerContent');
    const dateDisplay = document.getElementById('operatorDiversityDate');
    const displayDate = response?.date || queryDesc || window.AppState.currentOperationDate || '查询结果';

    if(dateDisplay) dateDisplay.textContent = displayDate;

    if (!response || !response.success) {
        console.error("加载运营数据失败:", response?.message || "未知错误");
        if (container) container.innerHTML = `<div class="empty-state error-state">加载运营数据失败: ${response?.message || '请稍后重试'}</div>`;
        window.AppState.rawOperationData = null;
        window.AppState.operatorDiversityData = [];
        window.AppState.currentOperationDate = '加载失败';
        return;
    }

    window.AppState.rawOperationData = response.data;
    window.AppState.currentOperationDate = displayDate;
    window.AppState.operatorDiversityData = processOperatorDiversityData(response.data, window.AppState.shopOperatorMap);
    const filteredData = applyOperatorDiversityFilters(window.AppState.operatorDiversityData, window.AppState.operatorDiversityFilters);

    if (container) {
        if (filteredData && filteredData.length > 0) {
            container.innerHTML = generateOperatorDiversityTable(filteredData);
        } else {
            container.innerHTML = '<div class="empty-state">无数据显示 (可能已被筛选)</div>';
        }
        reattachSearchListeners('operatorDiversityTable'); // Re-attach listeners
    }
}

function updateSpuListingTable(response, queryDesc = null) {
    const container = document.getElementById('spuListingContainerContent');
    const dateDisplay = document.getElementById('spuListingDate');
    const displayDate = response?.date || queryDesc || window.AppState.currentOperationDate || '查询结果';

    if(dateDisplay) dateDisplay.textContent = displayDate;

    // SPU table relies on the raw data loaded by the query
    if (!window.AppState.rawOperationData) {
         if (container) {
             container.innerHTML = '<div class="empty-state">请先查询运营数据</div>';
         }
         window.AppState.spuListingData = [];
         return;
    }

    if (!response || !response.success) {
        // Data loading failed, but raw data might exist from previous success
        console.warn("更新SPU表时遇到错误响应，将尝试使用现有数据:", response?.message);
        // We don't clear SPU data here, try to render with existing if possible
    }


    // Process raw data (ensure rawData is up-to-date from AppState)
    window.AppState.spuListingData = processSpuListingData(window.AppState.rawOperationData, window.AppState.shopOperatorMap);

    // Apply filters if any
    const filteredData = applySpuListingFilters(window.AppState.spuListingData, window.AppState.spuListingFilters);

    // Render table
    if (container) {
        if (filteredData && filteredData.length > 0) {
            container.innerHTML = generateSpuListingTable(filteredData); // Use filtered data
        } else {
            container.innerHTML = '<div class="empty-state">无数据显示 (可能已被筛选)</div>';
        }
        reattachSearchListeners('spuListingTable'); // Re-attach listeners
    }
}

// --- 主入口函数 ---
// 这个函数将在点击"SPU管理"菜单时被调用
function generateProductManagement() {
    if (!checkLoginStatus()) return;
    
    showLoadingIndicator();
    
    // 异步初始化
    (async function() {
        try {
            // 并行加载数据
            const [shopOperatorRawData, initialOperationResponse, fixedDataResponse] = await Promise.all([
                loadShopOperatorData(),
                loadDailyOperationData(), // 默认加载昨日数据
                loadFixedData() // 加载固定数据
            ]);
    
            // 创建店铺-运营映射表
            const shopOperatorMap = {};
            Object.entries(shopOperatorRawData).forEach(([shopName, data]) => {
                shopOperatorMap[shopName] = data.operator || '未知运营';
            });
    
            // 初始化 AppState
            window.AppState.init(shopOperatorMap, initialOperationResponse.data, initialOperationResponse.date || '昨日');
    
            // 处理并存储固定数据
            if (fixedDataResponse && fixedDataResponse.success) {
                window.AppState.fixedTable1Data = processFixedTable1Data(fixedDataResponse.data, shopOperatorMap);
                window.AppState.fixedTable2Data = processFixedTable2Data(fixedDataResponse.data, shopOperatorMap);
            }
    
            // 构建UI框架
            buildUI();
    
            // 填充表格数据
            updateOperatorDiversityTable(initialOperationResponse);
            updateSpuListingTable(initialOperationResponse);
            updateFixedTable1(fixedDataResponse);
            updateFixedTable2(fixedDataResponse);
    
            // 初始设置搜索监听
            setupTableSearch('operatorDiversityTable');
            setupTableSearch('spuListingTable');
            setupTableSearch('fixedTable1');
            setupTableSearch('fixedTable2');
    
        } catch (error) {
            console.error("SPU管理页面初始化失败:", error);
            const container = document.querySelector('.container');
            if (container) {
                container.innerHTML = `<div class="error-message">SPU管理页面加载失败，请刷新重试或联系管理员。错误：${error.message}</div>`;
            }
        } finally {
            hideLoadingIndicator();
        }
    })();
}

// 加载固定数据文件
async function loadFixedData() {
    try {
        const response = await fetch('/static/SKU/固定加载.json');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return { success: true, data: await response.json(), date: '固定数据' };
    } catch (error) {
        console.error('加载固定数据失败:', error);
        return { success: false, message: error.message };
    }
}

// 处理固定数据表格1
function processFixedTable1Data(rawData, shopOperatorMap) {
    // 使用与运营多样性表格相同的处理方式
    return processOperatorDiversityData(rawData, shopOperatorMap);
}

// 处理固定数据表格2
function processFixedTable2Data(rawData, shopOperatorMap) {
    // 使用与SPU表格相同的处理方式
    return processSpuListingData(rawData, shopOperatorMap);
}

// 生成固定数据表格1
function generateFixedTable1(fixedTable1Data) {
    if (!fixedTable1Data || fixedTable1Data.length === 0) {
        return '<div class="empty-state">暂无固定数据表1数据</div>';
    }

    let tableHtml = `
        <div class="data-table-container" style="max-height: ${fixedTable1Data.length > 10 ? '450px' : '350px'}">
            <table class="data-table fixed-table-1" id="fixedTable1">
                 <thead>
                    <tr>
                        <th style="width: 40px;"></th> <!-- Expand icon column -->
                        <th>运营 / 店铺 / SPU <input type="text" placeholder="搜索名称..." class="header-search" data-column="name"></th>
                        <th style="width: 90px; text-align: right;">店铺数</th>
                        <th style="width: 90px; text-align: right;">SPU数</th>
                        <th style="width: 90px; text-align: right;">链接数</th>
                        <th>商品ID列表 <input type="text" placeholder="搜索ID..." class="header-search" data-column="id"></th>
                    </tr>
                </thead>
                <tbody>
    `;

    // 使用与原表格相同的生成逻辑
    fixedTable1Data.forEach((operator, opIndex) => {
        const operatorId = `fixed1-op-${opIndex}`;
        // Operator Row (Level 1)
        tableHtml += `
            <tr class="operator-row level-1" data-level="1" data-operator-id="${operatorId}" data-operator-name="${operator.operatorName}">
                <td class="expand-control"><span class="expand-icon" onclick="toggleOperationRow(this)">▶</span></td>
                <td class="operator-name">${operator.operatorName}</td>
                <td class="data-cell number-cell">${operator.totalShops}</td>
                <td class="data-cell number-cell">${operator.totalSpus}</td>
                <td class="data-cell number-cell">${operator.totalLinks}</td>
                <td class="data-cell"></td>
            </tr>
        `;

        Object.entries(operator.shops).forEach(([shopName, shopData], shopIndex) => {
            const shopId = `${operatorId}-shop-${shopIndex}`;
            // Shop Row (Level 2) - Initially Hidden
            tableHtml += `
                <tr class="shop-row level-2 hidden-row" data-level="2" data-operator-id="${operatorId}" data-shop-id="${shopId}" data-shop-name="${shopName}">
                    <td class="expand-control"><span class="expand-icon" onclick="toggleOperationRow(this)">▶</span></td>
                    <td class="shop-name indent-1">${shopName}</td>
                    <td class="data-cell"></td>
                    <td class="data-cell number-cell">${shopData.totalSpus}</td>
                    <td class="data-cell number-cell">${shopData.totalLinks}</td>
                    <td class="data-cell"></td>
                </tr>
            `;

            Object.entries(shopData.spus).forEach(([spuName, spuData], spuIndex) => {
                const spuId = `${shopId}-spu-${spuIndex}`;
                // SPU Row (Level 3) - Initially Hidden
                tableHtml += `
                    <tr class="spu-row level-3 hidden-row" data-level="3" data-operator-id="${operatorId}" data-shop-id="${shopId}" data-spu-id="${spuId}" data-spu-name="${spuName}">
                        <td class="expand-control"></td>
                        <td class="spu-name indent-2">${spuName}</td>
                        <td class="data-cell"></td>
                        <td class="data-cell"></td>
                        <td class="data-cell number-cell">${spuData.商品数量}</td>
                        <td class="data-cell product-id-cell">
                            <div class="product-id-list-inline">
                                ${spuData.商品ID列表 && spuData.商品ID列表.length > 0
                                    ? spuData.商品ID列表.map(id => `<span class="product-id-badge" title="${id}">${id}</span>`).join('')
                                    : '<span class="text-muted">无ID</span>'
                                }
                            </div>
                        </td>
                    </tr>
                `;
            });
        });
    });

    tableHtml += `
                </tbody>
            </table>
        </div>
    `;

    return tableHtml;
}

// 生成固定数据表格2
function generateFixedTable2(fixedTable2Data) {
    if (!fixedTable2Data || fixedTable2Data.length === 0) {
        return '<div class="empty-state">暂无固定数据表2数据</div>';
    }

    let tableHtml = `
        <div class="data-table-container" style="max-height: ${fixedTable2Data.length > 10 ? '450px' : '350px'}">
            <table class="data-table fixed-table-2" id="fixedTable2">
                 <thead>
                        <tr>
                            <th style="width: 40px;"></th>
                            <th>SPU <input type="text" placeholder="搜索SPU..." class="header-search" data-column="spu"></th>
                            <th>运营 <input type="text" placeholder="搜索运营..." class="header-search" data-column="operator"></th>
                            <th style="width: 90px; text-align: right;">总链接数</th>
                            <th>商品ID列表 <input type="text" placeholder="搜索ID..." class="header-search" data-column="id"></th>
                        </tr>
                    </thead>
                <tbody>
    `;

    // 使用与原表格相同的生成逻辑
    fixedTable2Data.forEach((spu, spuIndex) => {
        const spuId = `fixed2-spu-${spuIndex}`;
        // SPU Row (Level 1)
        tableHtml += `
            <tr class="spu-row level-1" data-level="1" data-spu-id="${spuId}" data-spu-name="${spu.spuName}">
                <td class="expand-control"><span class="expand-icon" onclick="toggleSpuRow(this)">▶</span></td>
                <td class="spu-name">${spu.spuName}</td>
                <td class="data-cell number-cell">${spu.totalOperators}</td>
                <td class="data-cell number-cell">${spu.totalLinks}</td>
                <td class="data-cell"></td>
            </tr>
        `;

        Object.entries(spu.operators).forEach(([operatorName, opData], opIndex) => {
            const operatorId = `${spuId}-op-${opIndex}`;
            // Operator Row (Level 2) - Initially Hidden
            tableHtml += `
                <tr class="operator-row level-2 hidden-row" data-level="2" data-spu-id="${spuId}" data-operator-id="${operatorId}" data-operator-name="${operatorName}">
                    <td class="expand-control"></td>
                    <td class="indent-1"></td>
                    <td class="operator-name">${operatorName}</td>
                    <td class="data-cell number-cell">${opData.links}</td>
                    <td class="data-cell product-id-cell">
                         <div class="product-id-list-inline">
                            ${opData.productIds && opData.productIds.length > 0
                                ? opData.productIds.map(id => `<span class="product-id-badge" title="${id}">${id}</span>`).join('')
                                : '<span class="text-muted">无ID</span>'
                            }
                        </div>
                    </td>
                </tr>
            `;
        });
    });

    tableHtml += `
                </tbody>
            </table>
        </div>
    `;

    return tableHtml;
}

// 更新固定数据表格1
function updateFixedTable1(data) {
    const container = document.getElementById('fixedTable1ContainerContent');
    const dateDisplay = document.getElementById('fixedTable1Date');
    
    if(dateDisplay) dateDisplay.textContent = '固定数据';
    
    if (!data || !data.success) {
        console.error("加载固定数据表1失败:", data?.message || "未知错误");
        if (container) container.innerHTML = `<div class="empty-state error-state">加载固定数据表1失败: ${data?.message || '请稍后重试'}</div>`;
        return;
    }
    
    // 如果data.data是原始数据，需要处理；如果已经是处理过的数据，直接使用
    let processedData;
    if (Array.isArray(data.data) && data.data.length > 0 && data.data[0].operatorName) {
        // 已经是处理过的数据
        processedData = data.data;
    } else {
        // 原始数据，需要处理
        processedData = processFixedTable1Data(data.data, window.AppState.shopOperatorMap);
        // 存储处理后的数据到AppState
        window.AppState.fixedTable1Data = processedData;
    }
    
    // 应用过滤器
    const filteredData = applyFixedTable1Filters(processedData, window.AppState.fixedTable1Filters);
    
    if (container) {
        if (filteredData && filteredData.length > 0) {
            container.innerHTML = generateFixedTable1(filteredData);
        } else {
            container.innerHTML = '<div class="empty-state">固定数据表1无数据显示 (可能已被筛选)</div>';
        }
        reattachSearchListeners('fixedTable1');
    }
}

// 更新固定数据表格2
function updateFixedTable2(data) {
    const container = document.getElementById('fixedTable2ContainerContent');
    const dateDisplay = document.getElementById('fixedTable2Date');
    
    if(dateDisplay) dateDisplay.textContent = '固定数据';
    
    if (!data || !data.success) {
        console.error("加载固定数据表2失败:", data?.message || "未知错误");
        if (container) container.innerHTML = `<div class="empty-state error-state">加载固定数据表2失败: ${data?.message || '请稍后重试'}</div>`;
        return;
    }
    
    // 如果data.data是原始数据，需要处理；如果已经是处理过的数据，直接使用
    let processedData;
    if (Array.isArray(data.data) && data.data.length > 0 && data.data[0].spuName) {
        // 已经是处理过的数据
        processedData = data.data;
    } else {
        // 原始数据，需要处理
        processedData = processFixedTable2Data(data.data, window.AppState.shopOperatorMap);
        // 存储处理后的数据到AppState
        window.AppState.fixedTable2Data = processedData;
    }
    
    // 应用过滤器
    const filteredData = applyFixedTable2Filters(processedData, window.AppState.fixedTable2Filters);
    
    if (container) {
        if (filteredData && filteredData.length > 0) {
            container.innerHTML = generateFixedTable2(filteredData);
        } else {
            container.innerHTML = '<div class="empty-state">固定数据表2无数据显示 (可能已被筛选)</div>';
        }
        reattachSearchListeners('fixedTable2');
    }
}
