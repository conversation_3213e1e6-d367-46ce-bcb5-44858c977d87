/**
 * Simple ECharts Fallback Library
 * This is a very minimal version with just enough to prevent errors
 */
(function(global) {
    // Create a simple echarts object
    var echarts = {
        init: function(dom) {
            if (!dom) {
                console.error('ECharts init: dom not found');
                return {
                    setOption: function() {},
                    resize: function() {}
                };
            }
            
            // Add a message to the DOM element to show that we're using the fallback
            dom.innerHTML = '<div style="padding: 20px; text-align: center; background: #f8f9fa; border-radius: 5px; height: 100%;">' +
                '<p style="margin-bottom: 10px;">图表加载失败，请检查网络连接</p>' +
                '<p>Chart failed to load. Please check your network connection.</p>' +
                '</div>';
            
            return {
                setOption: function(option) {
                    console.log('setOption called with:', option);
                },
                resize: function() {
                    console.log('resize called');
                }
            };
        },
        graphic: {
            LinearGradient: function(x, y, x2, y2, colorStops) {
                return {
                    type: 'linear',
                    x: x,
                    y: y,
                    x2: x2,
                    y2: y2,
                    colorStops: colorStops
                };
            }
        },
        // Minimal version of some commonly used components
        version: 'fallback-1.0.0'
    };

    // Make it available globally
    global.echarts = echarts;
})(typeof window !== 'undefined' ? window : this); 