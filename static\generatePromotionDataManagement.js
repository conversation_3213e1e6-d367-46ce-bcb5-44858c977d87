/**
 * 推广数据管理
 * 用于显示和管理推广数据汇总和详细信息
 */

// 初始化变量
if (typeof currentPage === 'undefined') {
    var currentPage = 1;
}
if (typeof totalRecords === 'undefined') {
    var totalRecords = 0;
}
if (typeof pageSize === 'undefined') {
    var pageSize = 20; // 默认每页显示20条
}
if (typeof sortField === 'undefined') {
    var sortField = 'fetch_time';
}
if (typeof sortOrder === 'undefined') {
    var sortOrder = 'desc';
}
if (typeof keyword === 'undefined') {
    var keyword = '';
}
if (typeof shopFilter === 'undefined') {
    var shopFilter = '';
}
if (typeof dataType === 'undefined') {
    var dataType = 'summary'; // 'summary' 或 'detail'
}

// 统计数据
if (typeof statsData === 'undefined') {
    var statsData = {
        totalShops: 0,
        totalAds: 0,
        totalSpend: 0,
        totalGmv: 0,
        avgRoi: 0
    };
}

// 设置缓存过期时间（毫秒）
const CACHE_EXPIRY = 30 * 60 * 1000; // 30分钟

// 字段映射到显示字段
const summaryFieldMapping = {
    'shop_name': '店铺名称',
    'fetch_date': '数据日期',
    'fetch_time': '获取时间',
    'total_ad_num': '推广总数',
    'spend_amount': '消费金额',
    'gmv': 'GMV',
    'net_gmv': '净GMV',
    'order_num': '订单数',
    'net_order_num': '净订单数',
    'order_roi': '订单ROI',
    'net_order_roi': '净订单ROI',
    'click_rate': '点击率',
    'conversion_rate': '转化率'
};

const detailFieldMapping = {
    'shop_name': '店铺名称',
    'ad_id': '广告ID',
    'ad_name': '广告名称',
    'goods_name': '商品名称',
    'ad_status': '广告状态',
    'max_cost': '最大消费',
    'spend_amount': '消费金额',
    'spend_ratio': '剩余预算占比',
    'gmv': 'GMV',
    'net_gmv': '净GMV',
    'order_num': '订单数',
    'net_order_num': '净订单数',
    'order_roi': '订单ROI',
    'net_order_roi': '净订单ROI',
    'click_num': '点击数',
    'impression_num': '展示数',
    'click_rate': '点击率',
    'conversion_rate': '转化率',
    'fetch_time': '获取时间'
};

// 加载推广数据
function loadPromotionData() {
    // 显示加载状态
    const container = document.querySelector('.container');
    container.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-3">正在加载推广数据...</p>
        </div>
    `;

    // 尝试从缓存加载数据
    const cachedData = loadFromCache();
    if (cachedData) {
        // 使用缓存数据进行初始渲染
        const { page, pageSize: cachedPageSize, data, total, statistics } = cachedData;
        // 设置全局变量
        currentPage = page || 1;
        pageSize = cachedPageSize || 20;
        totalRecords = total || 0;
        statsData = statistics || statsData;
        
        console.log("已从缓存加载数据，共加载 " + totalRecords + " 条记录");
        renderPromotionManagement(data);
        
        // 在后台继续请求最新数据
        fetchPromotionData();
    } else {
        // 直接请求数据
        fetchPromotionData();
    }
}

// 保存数据到本地缓存
function saveToCache(responseData) {
    try {
        const cacheData = {
            timestamp: Date.now(),
            page: responseData.page,
            pageSize: responseData.pageSize,
            total: responseData.total,
            data: responseData.data,
            statistics: responseData.statistics
        };
        localStorage.setItem('promotionDataCache', JSON.stringify(cacheData));
        console.log("数据已缓存到本地，共 " + responseData.total + " 条记录");
        return true;
    } catch (error) {
        console.error("缓存数据失败:", error);
        return false;
    }
}

// 从本地缓存加载数据
function loadFromCache() {
    try {
        const cacheData = localStorage.getItem('promotionDataCache');
        if (!cacheData) return null;
        
        const parsedCache = JSON.parse(cacheData);
        const now = Date.now();
        
        // 检查缓存是否过期
        if (now - parsedCache.timestamp > CACHE_EXPIRY) {
            console.log("缓存已过期，需要重新加载数据");
            localStorage.removeItem('promotionDataCache');
            return null;
        }
        
        return parsedCache;
    } catch (error) {
        console.error("读取缓存数据失败:", error);
        return null;
    }
}

// 从API获取推广数据
function fetchPromotionData() {
    // 显示加载状态
    const tableBody = document.getElementById('promotionTableBody');
    if (tableBody) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="21" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载数据...</p>
                </td>
            </tr>
        `;
    }
    
    // 构建当前筛选参数
    const hideZeroGmv = document.getElementById('hideZeroGmv');
    const hidePausedAds = document.getElementById('hidePausedAds');
    const currentFilterParams = {
        dataType: dataType,
        keyword: keyword,
        shopFilter: shopFilter,
        hideZeroGmv: hideZeroGmv ? hideZeroGmv.checked : false,
        hidePausedAds: hidePausedAds ? hidePausedAds.checked : false
    };
    
    console.log("筛选参数:", currentFilterParams);
    console.log("排序字段:", sortField, "排序方向:", sortOrder);
    
    // 构建查询URL - 所有字段都使用后端排序和分页
    let url = `/api/promotion-data?dataType=${dataType}`;
    url += `&page=${currentPage}&pageSize=${pageSize}&sortField=${sortField}&sortOrder=${sortOrder}`;
    
    // 添加筛选条件
    if (keyword) {
        url += `&keyword=${encodeURIComponent(keyword)}`;
    }
    if (shopFilter) {
        url += `&shop=${encodeURIComponent(shopFilter)}`;
    }
    
    // 添加GMV筛选
    if (currentFilterParams.hideZeroGmv) {
        url += `&hideZeroGmv=true`;
        console.log('添加GMV筛选参数: hideZeroGmv=true');
    }
    
    // 添加暂停广告筛选
    if (currentFilterParams.hidePausedAds) {
        url += `&hidePausedAds=true`;
        console.log('添加暂停广告筛选参数: hidePausedAds=true');
    }
    
    console.log("正在请求推广数据:", url);
    
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(responseData => {
            console.log("API返回数据:", responseData);
            
            if (responseData.success) {
                let allData = responseData.data || [];
                
                // 更新统计数据
                statsData = responseData.statistics || statsData;
                
                // 后端已经处理了排序和分页
                totalRecords = responseData.total || 0;
                
                // 保存到缓存
                saveToCache({
                    ...responseData,
                    page: currentPage,
                    pageSize: pageSize
                });
                
                // 渲染数据
                const existingTable = document.getElementById('promotionTableBody');
                if (existingTable) {
                    updateTableContent(allData);
                } else {
                    renderPromotionManagement(allData);
                }
            } else {
                throw new Error(responseData.message || '获取数据失败');
            }
        })
        .catch(error => {
            console.error("获取推广数据失败:", error);
            
            // 显示错误信息
            const container = document.querySelector('.container');
            container.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <h4 class="alert-heading">数据加载失败</h4>
                    <p>无法加载推广数据: ${error.message}</p>
                    <hr>
                    <p class="mb-0">
                        <button class="btn btn-outline-danger" onclick="fetchPromotionData()">重试</button>
                    </p>
                </div>
            `;
        });
}

// 格式化时间戳
function formatTimestamp(timestamp) {
    if (!timestamp) return '';
    
    try {
        const date = new Date(timestamp);
        if (isNaN(date.getTime())) {
            return timestamp; // 如果无法解析，返回原始值
        }
        
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    } catch (error) {
        console.error('格式化时间戳失败:', error);
        return timestamp;
    }
}

// 渲染推广数据管理界面
function renderPromotionManagement(data) {
    const container = document.querySelector('.container');
    
    // 获取当前复选框状态（如果存在的话）
    let currentHideZeroGmv = true; // 默认值
    let currentHidePausedAds = false; // 默认值
    
    const existingHideZeroGmv = document.getElementById('hideZeroGmv');
    const existingHidePausedAds = document.getElementById('hidePausedAds');
    
    if (existingHideZeroGmv) {
        currentHideZeroGmv = existingHideZeroGmv.checked;
    }
    if (existingHidePausedAds) {
        currentHidePausedAds = existingHidePausedAds.checked;
    }
    
    // 清空容器
    container.innerHTML = '';
    
    // 添加样式
    addPromotionManagementStyles();
    
    // 创建主要内容
    const content = `
        <div class="promotion-management">
            <div class="container-fluid">
                <div class="header">
                    <h1>推广数据管理</h1>
                    <p class="text-muted">管理和查看推广数据汇总与详细信息</p>
                </div>
                
                <!-- 统计卡片 -->
                <div class="stats-cards">
                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-value">${statsData.totalShops || 0}</div>
                            <div class="stat-label">店铺总数</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-value">${statsData.totalAds || 0}</div>
                            <div class="stat-label">推广总数</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-value">¥${(statsData.totalSpend || 0).toLocaleString()}</div>
                            <div class="stat-label">总消费</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-value">¥${(statsData.totalGmv || 0).toLocaleString()}</div>
                            <div class="stat-label">总GMV</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-value">${(statsData.avgRoi || 0).toFixed(2)}</div>
                            <div class="stat-label">平均ROI</div>
                        </div>
                    </div>
                </div>
                
                <!-- 筛选和控制面板 -->
                <div class="filter-panel">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label>数据类型:</label>
                            <div class="data-type-buttons">
                                <button type="button" class="btn data-type-btn ${dataType === 'summary' ? 'active' : ''}" data-type="summary">
                                    汇总数据
                                </button>
                                <button type="button" class="btn data-type-btn ${dataType === 'detail' ? 'active' : ''}" data-type="detail">
                                    详细数据
                                </button>
                            </div>
                        </div>
                        <div class="filter-group">
                            <label>店铺筛选:</label>
                            <select id="shopFilter" class="form-select">
                                <option value="">全部店铺</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>关键词:</label>
                            <input type="text" id="keywordFilter" class="form-control" placeholder="搜索店铺、商品名称或商品ID" value="${keyword}">
                        </div>
                        <div class="filter-group">
                            <label>每页显示:</label>
                            <select id="pageSizeFilter" class="form-select">
                                <option value="10" ${pageSize === 10 ? 'selected' : ''}>10条</option>
                                <option value="20" ${pageSize === 20 ? 'selected' : ''}>20条</option>
                                <option value="50" ${pageSize === 50 ? 'selected' : ''}>50条</option>
                                <option value="100" ${pageSize === 100 ? 'selected' : ''}>100条</option>
                            </select>
                        </div>
                    </div>
                    <div class="filter-row">
                        <div class="filter-group">
                            <label>筛选选项:</label>
                            <div class="filter-options">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="hideZeroGmv" ${currentHideZeroGmv ? 'checked' : ''}>
                                    <span class="checkmark"></span>
                                    隐藏GMV为0的数据
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="hidePausedAds" ${currentHidePausedAds ? 'checked' : ''}>
                                    <span class="checkmark"></span>
                                    隐藏暂停的广告
                                </label>
                            </div>
                        </div>
                        <div class="filter-group filter-actions">
                            <button id="applyFiltersBtn" class="btn btn-primary">
                                <i class="fas fa-search"></i> 应用筛选
                            </button>
                            <button id="resetFiltersBtn" class="btn btn-outline-secondary">
                                <i class="fas fa-undo"></i> 重置
                            </button>
                            <button id="exportDataBtn" class="btn btn-success">
                                <i class="fas fa-download"></i> 导出数据
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 数据表格 -->
                <div class="table-container">
                    <div class="table-header">
                        <h3>${dataType === 'summary' ? '汇总数据' : '详细数据'} (共 ${totalRecords} 条记录)</h3>
                    </div>
                    <div class="table-responsive">
                        <table class="promotion-table ${dataType === 'summary' ? 'summary-table' : 'detail-table'}">
                            <thead>
                                ${renderTableHeader()}
                            </thead>
                            <tbody id="promotionTableBody">
                                ${renderPromotionTable(data)}
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 分页 -->
                <div class="pagination-container">
                    ${renderPagination()}
                </div>
            </div>
        </div>
    `;
    
    container.innerHTML = content;
    
    // 绑定事件处理器
    bindEventHandlers();
    
    // 加载店铺列表
    loadShopList();
}

// 渲染表格头部
function renderTableHeader() {
    const fieldMapping = dataType === 'summary' ? summaryFieldMapping : detailFieldMapping;
    
    let headers = '';
    for (const [field, label] of Object.entries(fieldMapping)) {
        const sortClass = sortField === field ? (sortOrder === 'asc' ? 'sort-asc' : 'sort-desc') : '';
        headers += `
            <th class="sortable ${sortClass}" data-field="${field}">
                ${label}
                <span class="sort-indicator"></span>
            </th>
        `;
    }
    
    return `<tr>${headers}</tr>`;
}

// 渲染推广数据表格
function renderPromotionTable(data) {
    if (!data || data.length === 0) {
        const fieldMapping = dataType === 'summary' ? summaryFieldMapping : detailFieldMapping;
        const colCount = Object.keys(fieldMapping).length; // 移除操作列
        return `
            <tr>
                <td colspan="${colCount}" class="text-center py-4">
                    <div class="no-data">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无数据</p>
                    </div>
                </td>
            </tr>
        `;
    }
    
    return data.map(item => {
        const rowData = dataType === 'summary' ? renderSummaryRow(item) : renderDetailRow(item);
        return `
            <tr>
                ${rowData}
            </tr>
        `;
    }).join('');
}

// 渲染汇总数据行
function renderSummaryRow(item) {
    // 计算点击率和转化率
    const clickRate = item.impression_num > 0 ? ((item.click_num / item.impression_num) * 100).toFixed(2) + '%' : '0%';
    const conversionRate = item.click_num > 0 ? ((item.order_num / item.click_num) * 100).toFixed(2) + '%' : '0%';
    
    return `
        <td class="shop-name-column">${item.shop_name || ''}</td>
        <td class="date-column">${item.fetch_date || ''}</td>
        <td class="time-column">${formatTimestamp(item.fetch_time)}</td>
        <td class="number-column">${item.total_ad_num || 0}</td>
        <td class="cost-column">¥${(item.spend_amount || 0).toLocaleString()}</td>
        <td class="money-column">¥${(item.gmv || 0).toLocaleString()}</td>
        <td class="money-column">¥${(item.net_gmv || 0).toLocaleString()}</td>
        <td class="number-column">${item.order_num || 0}</td>
        <td class="number-column">${item.net_order_num || 0}</td>
        <td class="ratio-column">${(item.order_roi || 0).toFixed(2)}</td>
        <td class="ratio-column">${(item.net_order_roi || 0).toFixed(2)}</td>
        <td class="percentage-column">${clickRate}</td>
        <td class="percentage-column">${conversionRate}</td>
    `;
}

// 渲染详细数据行
function renderDetailRow(item) {
    // 计算点击率和转化率
    const clickRate = item.impression_num > 0 ? ((item.click_num / item.impression_num) * 100).toFixed(2) + '%' : '0%';
    const conversionRate = item.click_num > 0 ? ((item.order_num / item.click_num) * 100).toFixed(2) + '%' : '0%';
    
    // 直接使用数据库的剩余预算占比字段
    const spendRatioValue = parseFloat(item.spend_ratio) || 0;
    const spendRatio = spendRatioValue.toFixed(2) + '%';
    
    // 判断消费占比是否低于20%，决定颜色样式
    const spendRatioClass = spendRatioValue < 20 ? 'percentage-column low-spend-ratio' : 'percentage-column';
    
    return `
        <td>${item.shop_name || ''}</td>
        <td>${item.ad_id || ''}</td>
        <td class="text-column" data-tooltip="${item.ad_name || ''}">${item.ad_name || ''}</td>
        <td class="text-column" data-tooltip="${item.goods_name || ''}">${item.goods_name || ''}</td>
        <td><span class="badge ${getAdStatusBadgeClass(item.ad_status)}">${item.ad_status || ''}</span></td>
        <td class="cost-column">¥${(item.max_cost || 0).toLocaleString()}</td>
        <td class="cost-column">¥${(item.spend_amount || 0).toLocaleString()}</td>
        <td class="${spendRatioClass}">${spendRatio}</td>
        <td class="money-column">¥${(item.gmv || 0).toLocaleString()}</td>
        <td class="money-column">¥${(item.net_gmv || 0).toLocaleString()}</td>
        <td class="number-column">${item.order_num || 0}</td>
        <td class="number-column">${item.net_order_num || 0}</td>
        <td class="ratio-column">${(item.order_roi || 0).toFixed(2)}</td>
        <td class="ratio-column">${(item.net_order_roi || 0).toFixed(2)}</td>
        <td class="number-column">${(item.click_num || 0).toLocaleString()}</td>
        <td class="number-column">${(item.impression_num || 0).toLocaleString()}</td>
        <td class="percentage-column">${clickRate}</td>
        <td class="percentage-column">${conversionRate}</td>
        <td class="time-column">${formatTimestamp(item.fetch_time)}</td>
    `;
}

// 获取广告状态徽章样式
function getAdStatusBadgeClass(status) {
    switch (status) {
        case '投放中':
        case 'active':
            return 'bg-success';
        case '暂停':
        case 'paused':
            return 'bg-warning';
        case '已结束':
        case 'ended':
            return 'bg-secondary';
        default:
            return 'bg-info';
    }
}

// 渲染分页
function renderPagination() {
    const totalPages = Math.ceil(totalRecords / pageSize);
    
    if (totalPages <= 1) {
        return '';
    }
    
    let pagination = '<nav aria-label="推广数据分页"><ul class="pagination justify-content-center">';
    
    // 上一页
    pagination += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})" aria-label="上一页">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    if (startPage > 1) {
        pagination += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(1)">1</a></li>`;
        if (startPage > 2) {
            pagination += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        pagination += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `;
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            pagination += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        pagination += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${totalPages})">${totalPages}</a></li>`;
    }
    
    // 下一页
    pagination += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})" aria-label="下一页">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
    `;
    
    pagination += '</ul></nav>';
    
    return pagination;
}

// 加载店铺列表
function loadShopList() {
    fetch('/api/promotion-shops')
        .then(response => response.json())
        .then(result => {
            if (result.success && result.data) {
                const shopSelect = document.getElementById('shopFilter');
                if (shopSelect) {
                    // 清空现有选项（保留"全部店铺"）
                    shopSelect.innerHTML = '<option value="">全部店铺</option>';
                    
                    // 添加店铺选项
                    result.data.forEach(shop => {
                        const option = document.createElement('option');
                        option.value = shop;
                        option.textContent = shop;
                        option.selected = shop === shopFilter;
                        shopSelect.appendChild(option);
                    });
                }
            }
        })
        .catch(error => {
            console.error('加载店铺列表失败:', error);
        });
}

// 绑定事件处理器
function bindEventHandlers() {
    // 数据类型切换
    const dataTypeButtons = document.querySelectorAll('.data-type-btn');
    dataTypeButtons.forEach(button => {
        button.addEventListener('click', function() {
            // 移除所有按钮的active类
            dataTypeButtons.forEach(btn => btn.classList.remove('active'));
            // 为当前按钮添加active类
            this.classList.add('active');
            
            const newDataType = this.getAttribute('data-type');
            
            // 如果切换到详细数据，设置默认排序为剩余预算升序（少的在上面）
            if (newDataType === 'detail' && dataType !== 'detail') {
                sortField = 'spend_ratio';
                sortOrder = 'asc'; // 升序，剩余预算少的在上面
            } else if (newDataType === 'summary' && dataType !== 'summary') {
                // 切换到汇总数据时，恢复默认排序
                sortField = 'fetch_time';
                sortOrder = 'desc';
            }
            
            dataType = newDataType;
            currentPage = 1; // 重置到第一页
            fetchPromotionData();
        });
    });
    
    // 应用筛选
    const applyFiltersBtn = document.getElementById('applyFiltersBtn');
    if (applyFiltersBtn) {
        applyFiltersBtn.addEventListener('click', applyFilters);
    }
    
    // 重置筛选
    const resetFiltersBtn = document.getElementById('resetFiltersBtn');
    if (resetFiltersBtn) {
        resetFiltersBtn.addEventListener('click', resetFilters);
    }
    
    // 导出数据
    const exportDataBtn = document.getElementById('exportDataBtn');
    if (exportDataBtn) {
        exportDataBtn.addEventListener('click', exportData);
    }
    
    // 每页显示数量变化
    const pageSizeFilter = document.getElementById('pageSizeFilter');
    if (pageSizeFilter) {
        pageSizeFilter.addEventListener('change', function() {
            pageSize = parseInt(this.value);
            currentPage = 1; // 重置到第一页
            fetchPromotionData();
        });
    }
    
    // GMV筛选复选框
    const hideZeroGmv = document.getElementById('hideZeroGmv');
    if (hideZeroGmv && !hideZeroGmv.hasAttribute('data-bound')) {
        hideZeroGmv.addEventListener('change', function() {
            console.log('GMV筛选状态改变:', this.checked);
            currentPage = 1;
            fetchPromotionData();
        });
        hideZeroGmv.setAttribute('data-bound', 'true');
    }
    
    // 暂停广告筛选复选框
    const hidePausedAds = document.getElementById('hidePausedAds');
    if (hidePausedAds && !hidePausedAds.hasAttribute('data-bound')) {
        hidePausedAds.addEventListener('change', function() {
            console.log('暂停广告筛选状态改变:', this.checked);
            currentPage = 1;
            fetchPromotionData();
        });
        hidePausedAds.setAttribute('data-bound', 'true');
    }
    
    // 表格排序
    const sortableHeaders = document.querySelectorAll('.sortable');
    sortableHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const field = this.getAttribute('data-field');
            if (sortField === field) {
                sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
            } else {
                sortField = field;
                sortOrder = 'desc';
            }
            fetchPromotionData();
        });
    });
    
    // 回车键搜索
    const keywordFilter = document.getElementById('keywordFilter');
    if (keywordFilter) {
        keywordFilter.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                applyFilters();
            }
        });
    }
}

// 应用筛选
function applyFilters() {
    keyword = document.getElementById('keywordFilter').value.trim();
    shopFilter = document.getElementById('shopFilter').value;
    
    currentPage = 1; // 重置到第一页
    fetchPromotionData();
}

// 重置筛选
function resetFilters() {
    keyword = '';
    shopFilter = '';
    dataType = 'summary';
    currentPage = 1;
    pageSize = 20;
    sortField = 'fetch_time';
    sortOrder = 'desc';
    
    // 重置表单
    document.getElementById('keywordFilter').value = '';
    document.getElementById('shopFilter').value = '';
    document.getElementById('pageSizeFilter').value = '20';
    document.getElementById('hideZeroGmv').checked = true;
    document.getElementById('hidePausedAds').checked = false;
    
    // 重置数据类型按钮
    document.querySelectorAll('.data-type-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.getAttribute('data-type') === 'summary') {
            btn.classList.add('active');
        }
    });
    
    fetchPromotionData();
}

// 导出数据
function exportData() {
    const hideZeroGmv = document.getElementById('hideZeroGmv');
    const hidePausedAds = document.getElementById('hidePausedAds');
    const exportParams = {
        dataType: dataType,
        shop: shopFilter,
        keyword: keyword,
        hideZeroGmv: hideZeroGmv && hideZeroGmv.checked,
        hidePausedAds: hidePausedAds && hidePausedAds.checked,
        // 添加排序参数
        sortField: sortField,
        sortOrder: sortOrder
    };
    
    // 显示导出进度
    const exportBtn = document.getElementById('exportDataBtn');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 导出中...';
    exportBtn.disabled = true;
    
    fetch('/api/promotion-data/export', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(exportParams)
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(err => {
                throw new Error(err.message || '导出失败');
            });
        }
        return response.blob();
    })
    .then(blob => {
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        
        // 生成文件名
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
        const shopName = shopFilter ? `_${shopFilter}` : '';
        const keywordText = keyword ? `_${keyword}` : '';
        a.download = `推广数据_${dataType === 'summary' ? '汇总' : '详细'}${shopName}${keywordText}_${timestamp}.xlsx`;
        
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        // 显示成功消息
        showAlert('数据导出成功', 'success');
    })
    .catch(error => {
        console.error('导出失败:', error);
        showAlert('导出失败: ' + error.message, 'danger');
    })
    .finally(() => {
        // 恢复按钮状态
        exportBtn.innerHTML = originalText;
        exportBtn.disabled = false;
    });
}

// 切换页面
function changePage(page) {
    if (page < 1 || page > Math.ceil(totalRecords / pageSize)) {
        return;
    }
    
    currentPage = page;
    fetchPromotionData();
}

// 查看推广详情
function viewPromotionDetails(id, data) {
    // 创建详情模态框
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'promotionDetailModal';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">推广数据详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    ${renderPromotionDetailContent(data)}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // 显示模态框
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
    
    // 模态框关闭后移除DOM元素
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

// 渲染推广详情内容
function renderPromotionDetailContent(data) {
    const fieldMapping = dataType === 'summary' ? summaryFieldMapping : detailFieldMapping;
    
    let content = '<div class="row">';
    
    for (const [field, label] of Object.entries(fieldMapping)) {
        let value = data[field] || '';
        
        // 格式化特定字段
        if (field.includes('time')) {
            value = formatTimestamp(value);
        } else if (field.includes('amount') || field.includes('gmv')) {
            value = '¥' + (parseFloat(value) || 0).toLocaleString();
        } else if (field.includes('roi')) {
            value = (parseFloat(value) || 0).toFixed(2);
        } else if (field.includes('num')) {
            value = (parseInt(value) || 0).toLocaleString();
        } else if (field === 'spend_ratio') {
            // 直接使用数据库的剩余预算占比字段
            value = (parseFloat(value) || 0).toFixed(2) + '%';
        } else if (field.includes('rate')) {
            // 格式化其他比率字段
            value = value ? (parseFloat(value) * 100).toFixed(2) + '%' : '0%';
        }
        
        content += `
            <div class="col-md-6 mb-3">
                <label class="form-label fw-bold">${label}:</label>
                <div class="form-control-plaintext">${value}</div>
            </div>
        `;
    }
    
    content += '</div>';
    
    return content;
}

// 查看店铺趋势
function viewShopTrend(shopName) {
    // 这里可以跳转到店铺趋势分析页面或显示趋势图表
    console.log('查看店铺趋势:', shopName);
    // 可以调用其他模块的函数或跳转页面
    showAlert(`正在加载 ${shopName} 的趋势分析...`, 'info');
}

// 显示提示信息
function showAlert(message, type = 'success') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 5秒后自动关闭
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.classList.remove('show');
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    document.body.removeChild(alertDiv);
                }
            }, 300);
        }
    }, 5000);
}

// 添加推广数据管理样式
function addPromotionManagementStyles() {
    // 检查是否已经添加过样式
    if (document.getElementById('promotion-management-styles')) {
        return;
    }
    
    const style = document.createElement('style');
    style.id = 'promotion-management-styles';
    style.textContent = `
        /* 全局基础样式 */
        .promotion-management {
            padding: 0;
            background: #f8fafc;
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }
        
        /* 顶部装饰 */
        .promotion-management::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            z-index: 1000;
        }
        
        /* 主容器 */
        .promotion-management .container-fluid {
            padding: 2rem;
            max-width: 1600px;
            margin: 0 auto;
        }
        
        /* 头部区域 */
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem 0;
        }
        
        .header h1 {
            color: #1a202c;
            margin-bottom: 0.5rem;
            font-size: 2.5rem;
            font-weight: 800;
            letter-spacing: -0.025em;
        }
        
        .header p {
            color: #4a5568;
            font-size: 1.1rem;
            margin: 0;
            opacity: 0.8;
        }
        
        /* 统计卡片网格 */
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }
        
        /* 统计卡片 */
        .stat-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 2rem 1.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            display: flex;
            align-items: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            border-left: 4px solid #667eea;
        }
        
        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border-left-color: #764ba2;
        }
        
        .stat-content {
            flex: 1;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 0.25rem;
            line-height: 1.2;
        }
        
        .stat-label {
            color: #4a5568;
            font-size: 0.9rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        /* 筛选面板 */
        .filter-panel {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        
        .filter-row {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
            align-items: end;
        }
        
        .filter-row:last-child {
            margin-bottom: 0;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            min-width: 180px;
            flex: 1;
        }
        
        .filter-group label {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2d3748;
            font-size: 0.9rem;
        }
        
        .filter-group .form-control,
        .filter-group .form-select {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: white;
        }
        
        .filter-group .form-control:focus,
        .filter-group .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            outline: none;
        }
        
        /* 按钮样式 */
        .btn {
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
            box-shadow: 0 4px 14px 0 rgba(102, 126, 234, 0.39);
        }
        
        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        
        .btn-outline-secondary {
            background: transparent;
            border: 2px solid #cbd5e0;
            color: #4a5568;
        }
        
        .btn-outline-secondary:hover {
            background: #f7fafc;
            border-color: #a0aec0;
            color: #2d3748;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #48bb78;
            color: white;
            box-shadow: 0 4px 14px 0 rgba(72, 187, 120, 0.39);
        }
        
        .btn-success:hover {
            background: #38a169;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);
        }
        
        /* 表格容器 */
        .table-container {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            margin-bottom: 2rem;
        }
        
        .table-header {
            background: #667eea;
            color: white;
            padding: 1.5rem 2rem;
            position: relative;
        }
        
        .table-header h3 {
            margin: 0;
            font-size: 1.3rem;
            font-weight: 600;
        }
        
        /* 表格样式 */
        .promotion-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
            table-layout: auto;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.5;
        }
        
        /* 表格自适应优化 */
        .table-responsive {
            overflow-x: auto;
            overflow-y: visible;
            min-height: 200px;
        }
        
        .promotion-table {
            min-width: 100%;
            width: max-content;
        }
        
        .promotion-table th {
            background: #f7fafc;
            color: #2d3748;
            font-weight: 600;
            padding: 1rem 0.75rem;
            text-align: left;
            border-bottom: 2px solid #e2e8f0;
            position: relative;
            font-size: 0.8rem;
            line-height: 1.3;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        
        .promotion-table th.sortable {
            cursor: pointer;
            user-select: none;
            transition: all 0.3s ease;
        }
        
        .promotion-table th.sortable:hover {
            background: #edf2f7;
            color: #667eea;
        }
        
        .sort-indicator {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            opacity: 0.5;
            transition: all 0.3s ease;
        }
        
        .sort-indicator::after {
            content: '↕';
            font-size: 0.8rem;
        }
        
        .sort-asc .sort-indicator::after {
            content: '↑';
            opacity: 1;
            color: #667eea;
        }
        
        .sort-desc .sort-indicator::after {
            content: '↓';
            opacity: 1;
            color: #667eea;
        }
        
        .promotion-table td {
            padding: 0.875rem 0.75rem;
            border-bottom: 1px solid #e2e8f0;
            vertical-align: middle;
            transition: all 0.3s ease;
            font-size: 0.875rem;
            line-height: 1.4;
        }
        
        /* 特殊处理长文本列 */
        .promotion-table td.text-column {
            word-wrap: break-word;
            word-break: break-all;
            line-height: 1.4;
            max-width: 300px;
            position: relative;
        }
        
        .promotion-table td.text-column:hover {
            background: #f7fafc;
            z-index: 10;
            position: relative;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border-radius: 4px;
        }
        
        .promotion-table tr {
            transition: all 0.3s ease;
        }
        
        .promotion-table tr:hover {
            background: #f7fafc;
        }
        
        /* 无数据状态 */
        .no-data {
            text-align: center;
            padding: 4rem 2rem;
            color: #4a5568;
        }
        
        .no-data i {
            opacity: 0.5;
            margin-bottom: 1rem;
        }
        
        /* 分页样式 */
        .pagination-container {
            display: flex;
            justify-content: center;
            margin-top: 2rem;
        }
        
        .pagination .page-link {
            border: 1px solid #e2e8f0;
            color: #4a5568;
            background: white;
            margin: 0 2px;
            border-radius: 6px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .pagination .page-link:hover {
            background: #667eea;
            border-color: #667eea;
            color: white;
            transform: translateY(-2px);
        }
        
        .pagination .page-item.active .page-link {
            background: #667eea;
            border-color: #667eea;
            color: white;
            box-shadow: 0 4px 14px 0 rgba(102, 126, 234, 0.39);
        }
        
        .pagination .page-item.disabled .page-link {
            opacity: 0.5;
            background: #f7fafc;
            border-color: #e2e8f0;
        }
        
        /* 徽章样式 */
        .badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
            border-radius: 12px;
            font-weight: 600;
        }
        
        .badge.bg-success {
            background: #48bb78 !important;
            color: white;
        }
        
        .badge.bg-warning {
            background: #ed8936 !important;
            color: white;
        }
        
        .badge.bg-secondary {
            background: #a0aec0 !important;
            color: white;
        }
        
        .badge.bg-info {
            background: #4299e1 !important;
            color: white;
        }
        
        /* 工具提示 */
        .tooltip-text {
            position: relative;
            cursor: help;
        }
        
        .tooltip-text:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: #2d3748;
            color: white;
            padding: 0.75rem 1rem;
            border-radius: 6px;
            font-size: 0.8rem;
            white-space: nowrap;
            z-index: 1000;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .tooltip-text:hover::before {
            content: '';
            position: absolute;
            bottom: calc(100% - 6px);
            left: 50%;
            transform: translateX(-50%);
            border: 6px solid transparent;
            border-top-color: #2d3748;
            z-index: 1001;
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .stats-cards {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
            
            .promotion-table {
                font-size: 0.8rem;
            }
        }
        
        @media (max-width: 768px) {
            .promotion-management .container-fluid {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .stats-cards {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .stat-card {
                padding: 1.5rem 1rem;
            }
            
            .stat-value {
                font-size: 1.5rem;
            }
            
            .filter-row {
                flex-direction: column;
                gap: 1rem;
            }
            
            .filter-group {
                min-width: auto;
            }
            
            .filter-panel {
                padding: 1.5rem;
            }
            
            .table-header {
                padding: 1rem;
            }
            
            .promotion-table {
                font-size: 0.75rem;
            }
            
            .promotion-table th,
            .promotion-table td {
                padding: 0.5rem 0.25rem;
            }
            
            .btn {
                padding: 0.6rem 1.2rem;
                font-size: 0.8rem;
            }
        }
        
        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.5rem;
            }
            
            .stat-card {
                text-align: center;
                padding: 1rem;
            }
        }
        
        /* 加载动画 */
        .spinner-border {
            border-color: #667eea;
            border-right-color: transparent;
        }
        
        /* 自定义滚动条 */
        .table-responsive::-webkit-scrollbar {
            height: 8px;
        }
        
        .table-responsive::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }
        
        .table-responsive::-webkit-scrollbar-thumb {
            background: #667eea;
            border-radius: 4px;
        }
        
        .table-responsive::-webkit-scrollbar-thumb:hover {
            background: #5a67d8;
        }
        
        /* 语义化列样式 */
        .shop-name-column {
            font-weight: 600;
            color: #2d3748;
            font-size: 0.9rem;
        }
        
        .cost-column {
            font-weight: 600;
            color: #e53e3e;
            text-align: right;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Menlo', monospace;
            font-size: 0.875rem;
            letter-spacing: 0.025em;
        }
        
        .money-column {
            font-weight: 600;
            color: #38a169;
            text-align: right;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Menlo', monospace;
            font-size: 0.875rem;
            letter-spacing: 0.025em;
        }
        
        .number-column {
            text-align: right;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Menlo', monospace;
            color: #4a5568;
            font-size: 0.875rem;
            font-weight: 500;
            letter-spacing: 0.025em;
        }
        
        .ratio-column {
            text-align: right;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Menlo', monospace;
            color: #667eea;
            font-weight: 600;
            font-size: 0.875rem;
            letter-spacing: 0.025em;
        }
        
        .percentage-column {
            text-align: right;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Menlo', monospace;
            color: #805ad5;
            font-weight: 600;
            font-size: 0.875rem;
            letter-spacing: 0.025em;
        }
        
        /* 低消费占比样式（低于20%显示红色） */
        .low-spend-ratio {
            color: #e53e3e !important;
            font-weight: 700;
        }
        
        .date-column {
            color: #4a5568;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .time-column {
            color: #718096;
            font-size: 0.8rem;
            font-weight: 400;
        }
        
        /* 数据对比效果 */
        .cost-column:hover {
            background: rgba(229, 62, 62, 0.1);
            border-radius: 4px;
        }
        
        .money-column:hover {
            background: rgba(56, 161, 105, 0.1);
            border-radius: 4px;
        }
        
        .ratio-column:hover {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 4px;
        }
        
        .percentage-column:hover {
            background: rgba(128, 90, 213, 0.1);
            border-radius: 4px;
        }
        
        /* 表格行条纹效果 */
        .promotion-table tbody tr:nth-child(even) {
            background-color: rgba(247, 250, 252, 0.5);
        }
        
        .promotion-table tbody tr:hover {
            background: linear-gradient(90deg, 
                rgba(102, 126, 234, 0.05), 
                rgba(102, 126, 234, 0.02)) !important;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
        }
        
        /* 表头优化 */
        .promotion-table th {
            background: linear-gradient(135deg, #f8fafc, #edf2f7) !important;
            border-bottom: 2px solid #667eea !important;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        /* 数据趋势指示器 */
        .trend-up::after {
            content: ' ↗';
            color: #38a169;
            font-weight: bold;
        }
        
        .trend-down::after {
            content: ' ↘';
            color: #e53e3e;
            font-weight: bold;
        }
        
        /* 重要数据高亮 */
        .highlight-high {
            background: linear-gradient(90deg, rgba(56, 161, 105, 0.1), transparent);
            border-left: 3px solid #38a169;
        }
        
        .highlight-low {
            background: linear-gradient(90deg, rgba(229, 62, 62, 0.1), transparent);
            border-left: 3px solid #e53e3e;
        }
        
        /* 数据加载骨架屏 */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        /* 表格性能优化 */
        .promotion-table {
            will-change: scroll-position;
            contain: layout style paint;
        }
        
        /* 数据类型按钮组 */
        .data-type-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .data-type-btn {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            color: #4a5568;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .data-type-btn:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
            color: #2d3748;
        }
        
        .data-type-btn.active {
            background: #667eea;
            border-color: #667eea;
            color: white;
            box-shadow: 0 4px 14px 0 rgba(102, 126, 234, 0.39);
        }
        
        /* 筛选选项 */
        .filter-options {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            flex-wrap: wrap;
        }
        
        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 0.9rem;
            color: #4a5568;
            margin: 0;
            white-space: nowrap;
        }
        
        .checkbox-label input[type="checkbox"] {
            margin-right: 0.5rem;
            width: 16px;
            height: 16px;
            accent-color: #667eea;
        }
        
        /* 筛选操作按钮组 */
        .filter-actions {
            display: flex;
            gap: 0.75rem;
            align-items: end;
        }
        
        .filter-actions .btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            white-space: nowrap;
        }
        
        .filter-actions .btn i {
            font-size: 0.875rem;
        }
        
        /* 表格自适应优化 */
        .table-responsive {
            overflow-x: auto;
            overflow-y: visible;
            min-height: 200px;
        }
        
        .promotion-table {
            min-width: 100%;
            width: max-content;
        }
        
        /* 文本列自适应 */
        .promotion-table td.text-column {
            min-width: 150px;
            max-width: 300px;
            width: auto;
        }
        
        /* 数字列紧凑显示 */
        .promotion-table td.number-column,
        .promotion-table td.cost-column,
        .promotion-table td.money-column,
        .promotion-table td.ratio-column,
        .promotion-table td.percentage-column {
            white-space: nowrap;
            width: auto;
        }
        
        /* 店铺名称列 */
        .promotion-table td.shop-name-column {
            min-width: 100px;
            max-width: 150px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        /* 时间列 */
        .promotion-table td.time-column {
            white-space: nowrap;
            min-width: 140px;
        }
    `;
    
    document.head.appendChild(style);
}

// 主函数：生成推广数据管理界面
function generatePromotionDataManagement() {
    console.log('开始生成推广数据管理界面');
    
    // 重置全局变量
    currentPage = 1;
    totalRecords = 0;
    pageSize = 20;
    sortField = 'fetch_time';
    sortOrder = 'desc';
    keyword = '';
    dataType = 'summary';
    
    // 加载推广数据
    loadPromotionData();
}

// 导出主函数供外部调用
if (typeof window !== 'undefined') {
    window.generatePromotionDataManagement = generatePromotionDataManagement;
}

// 只更新表格内容，不重新渲染整个界面
function updateTableContent(data) {
    // 更新表格头部
    const tableHead = document.querySelector('.promotion-table thead');
    if (tableHead) {
        tableHead.innerHTML = renderTableHeader();
    }
    
    // 更新表格内容
    const tableBody = document.getElementById('promotionTableBody');
    if (tableBody) {
        tableBody.innerHTML = renderPromotionTable(data);
    }
    
    // 更新表格类名
    const table = document.querySelector('.promotion-table');
    if (table) {
        table.className = `promotion-table ${dataType === 'summary' ? 'summary-table' : 'detail-table'}`;
    }
    
    // 更新表格标题
    const tableHeader = document.querySelector('.table-header h3');
    if (tableHeader) {
        tableHeader.textContent = `${dataType === 'summary' ? '汇总数据' : '详细数据'} (共 ${totalRecords} 条记录)`;
    }
    
    // 更新分页
    const paginationContainer = document.querySelector('.pagination-container');
    if (paginationContainer) {
        paginationContainer.innerHTML = renderPagination();
    }
    
    // 重新绑定排序事件
    const sortableHeaders = document.querySelectorAll('.sortable');
    sortableHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const field = this.getAttribute('data-field');
            if (sortField === field) {
                sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
            } else {
                sortField = field;
                sortOrder = 'desc';
            }
            fetchPromotionData();
        });
    });
    
    // 更新数据类型按钮状态
    const dataTypeButtons = document.querySelectorAll('.data-type-btn');
    dataTypeButtons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.getAttribute('data-type') === dataType) {
            btn.classList.add('active');
        }
    });
    
    // 重新绑定筛选选项事件（如果还没有绑定的话）
    const hideZeroGmv = document.getElementById('hideZeroGmv');
    const hidePausedAds = document.getElementById('hidePausedAds');
    
    if (hideZeroGmv && !hideZeroGmv.hasAttribute('data-bound')) {
        hideZeroGmv.addEventListener('change', function() {
            console.log('GMV筛选状态改变:', this.checked);
            currentPage = 1;
            fetchPromotionData();
        });
        hideZeroGmv.setAttribute('data-bound', 'true');
    }
    
    if (hidePausedAds && !hidePausedAds.hasAttribute('data-bound')) {
        hidePausedAds.addEventListener('change', function() {
            console.log('暂停广告筛选状态改变:', this.checked);
            currentPage = 1;
            fetchPromotionData();
        });
        hidePausedAds.setAttribute('data-bound', 'true');
    }
} 