/* Common styles for the application */

/* Base styles */
:root {
    --primary-color: #4b6cb7;
    --primary-dark: #182848;
    --primary-light: #8e9edc;
    --secondary-color: #38b2ac;
    --success-color: #48bb78;
    --warning-color: #ed8936;
    --danger-color: #e53e3e;
    --info-color: #4299e1;
    --text-color: #2d3748;
    --text-light: #718096;
    --bg-color: #f7fafc;
    --bg-light: #ffffff;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --border-radius: 0.375rem;
    --transition: all 0.3s ease;
}

/* Typography */
body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    color: var(--text-color);
    line-height: 1.5;
    background-color: var(--bg-color);
}

h1, h2, h3, h4, h5, h6 {
    margin-bottom: 0.5rem;
    font-weight: 600;
    line-height: 1.2;
}

h1 {
    font-size: 1.875rem;
}

h2 {
    font-size: 1.5rem;
}

h3 {
    font-size: 1.25rem;
}

p {
    margin-bottom: 1rem;
}

/* Buttons */
.btn {
    display: inline-block;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
}

.btn:hover {
    opacity: 0.9;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.5);
}

.btn-primary {
    color: white;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary {
    color: white;
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-success {
    color: white;
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-danger {
    color: white;
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-warning {
    color: white;
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-info {
    color: white;
    background-color: var(--info-color);
    border-color: var(--info-color);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

.btn-outline:hover {
    background-color: var(--bg-color);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--text-color);
    background-color: var(--bg-light);
    background-clip: padding-box;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-light);
    outline: 0;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.25);
}

/* Cards */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: var(--bg-light);
    background-clip: border-box;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.card-header {
    padding: 0.75rem 1.25rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid var(--border-color);
}

.card-body {
    flex: 1 1 auto;
    padding: 1.25rem;
}

.card-footer {
    padding: 0.75rem 1.25rem;
    background-color: rgba(0, 0, 0, 0.03);
    border-top: 1px solid var(--border-color);
}

/* Tables */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--text-color);
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid var(--border-color);
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid var(--border-color);
    background-color: rgba(0, 0, 0, 0.03);
    font-weight: 600;
}

.table tbody + tbody {
    border-top: 2px solid var(--border-color);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Utilities */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.text-primary {
    color: var(--primary-color);
}

.text-secondary {
    color: var(--secondary-color);
}

.text-success {
    color: var(--success-color);
}

.text-danger {
    color: var(--danger-color);
}

.text-warning {
    color: var(--warning-color);
}

.text-info {
    color: var(--info-color);
}

.text-muted {
    color: var(--text-light);
}

.bg-primary {
    background-color: var(--primary-color);
}

.bg-secondary {
    background-color: var(--secondary-color);
}

.bg-success {
    background-color: var(--success-color);
}

.bg-danger {
    background-color: var(--danger-color);
}

.bg-warning {
    background-color: var(--warning-color);
}

.bg-info {
    background-color: var(--info-color);
}

.bg-light {
    background-color: var(--bg-light);
}

.d-flex {
    display: flex;
}

.flex-column {
    flex-direction: column;
}

.justify-content-between {
    justify-content: space-between;
}

.justify-content-center {
    justify-content: center;
}

.align-items-center {
    align-items: center;
}

.flex-wrap {
    flex-wrap: wrap;
}

.mb-1 {
    margin-bottom: 0.25rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 1rem;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

.mt-1 {
    margin-top: 0.25rem;
}

.mt-2 {
    margin-top: 0.5rem;
}

.mt-3 {
    margin-top: 1rem;
}

.mt-4 {
    margin-top: 1.5rem;
}

.ml-1 {
    margin-left: 0.25rem;
}

.ml-2 {
    margin-left: 0.5rem;
}

.ml-3 {
    margin-left: 1rem;
}

.mr-1 {
    margin-right: 0.25rem;
}

.mr-2 {
    margin-right: 0.5rem;
}

.mr-3 {
    margin-right: 1rem;
}

.p-1 {
    padding: 0.25rem;
}

.p-2 {
    padding: 0.5rem;
}

.p-3 {
    padding: 1rem;
}

.p-4 {
    padding: 1.5rem;
}

/* Loading indicator */
.loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: var(--text-light);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Error message */
.error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: var(--danger-color);
}

/* No data message */
.no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: var(--text-light);
}

/* Notification */
.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    padding: 0.75rem 1.25rem;
    border-radius: var(--border-radius);
    background-color: var(--bg-light);
    box-shadow: var(--shadow-md);
    z-index: 1000;
    transform: translateY(-20px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.notification.show {
    transform: translateY(0);
    opacity: 1;
}

.notification.info {
    border-left: 4px solid var(--info-color);
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.error {
    border-left: 4px solid var(--danger-color);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--bg-light);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    margin: 0;
    font-size: 1.25rem;
}

.modal-body {
    padding: 1rem;
}

.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    gap: 0.5rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
}

.close-btn:hover {
    color: var(--danger-color);
}

/* Tabs */
.tabs-container {
    width: 100%;
}

.tabs-header {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1rem;
}

.tab {
    padding: 0.75rem 1rem;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-light);
    transition: var(--transition);
}

.tab:hover {
    color: var(--primary-color);
}

.tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    display: none;
}

/* Search input */
.search-container {
    position: relative;
    width: 100%;
    max-width: 300px;
}

.search-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    padding-left: 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.search-input:focus {
    border-color: var(--primary-light);
    outline: none;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.25);
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

/* Date range picker */
.date-range-picker {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.date-input {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.date-input:focus {
    border-color: var(--primary-light);
    outline: none;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.25);
}

.apply-date-btn {
    padding: 0.5rem 0.75rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.apply-date-btn:hover {
    background-color: var(--primary-dark);
}

/* Pagination */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 1rem;
    gap: 1rem;
}

.pagination-btn {
    padding: 0.5rem 1rem;
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.pagination-btn:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    color: var(--text-light);
}
