// 避免重复执行代码
if (typeof window.salesStatsInitialized === 'undefined') {
    window.salesStatsInitialized = true;

    // 全局变量
    let salesChartInstance; // 销售图表实例
    let promotionChartInstance; // 推广费图表实例
    let brandComparisonChartInstance; // 品牌对比图表实例
    let shopTreemapChartInstance; // 店铺矩形树图实例
    let currentData = {};
    let selectedShops = [];
    let comparisonData = {}; // 存储同比环比数据
    let chartOptions = {
        days: 30,
        // dataType: 'sales', // 移除，现在同时显示
        chartType: 'pie', // 修改默认值为'pie'而不是'line'
        displayMode: 'separate', // 'stack' 将同时应用于两个图表
        shopFilter: '',
        operatorFilter: '',
        brandFilter: '' // 新增品牌筛选
    };

    // 颜色列表，确保足够多的店铺有不同颜色
    const colorPalette = [
        '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
        '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf',
        '#aec7e8', '#ffbb78', '#98df8a', '#ff9896', '#c5b0d5',
        '#c49c94', '#f7b6d2', '#c7c7c7', '#dbdb8d', '#9edae5'
    ];

    // 生成销售统计页面
    function generateSalesStats() {
        const container = document.querySelector('.container');
        if (!container) return;

        container.innerHTML = `
            <div class="sales-stats-container">
                <div class="header">
                    <h1>销售趋势分析</h1>
                </div>

                <div class="filter-panel">
                    <div class="filter-grid">
                        <div class="filter-group time-range-group">
                            <label class="filter-label">时间范围</label>
                            <div class="chart-actions">
                                <button class="chart-action time-range-btn" data-days="7">7天</button>
                                <button class="chart-action time-range-btn" data-days="15">15天</button>
                                <button class="chart-action time-range-btn active" data-days="30">30天</button>
                            </div>
                        </div>
                        <!-- 移除数据类型选择 -->
                        <div class="filter-group">
                            <label class="filter-label">图表类型</label>
                            <select id="chartTypeSelect" class="filter-control">
                                <option value="pie">饼图 (汇总)</option>
                                <option value="line">折线图</option>
                                <option value="bar">柱状图</option>
                                <option value="area">面积图</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">显示方式 (趋势图)</label>
                            <select id="displayModeSelect" class="filter-control">
                                <option value="separate">多条折线/柱</option>
                                <option value="stack">堆叠图表</option>
                            </select>
                        </div>
                        <div class="filter-group shop-filter-group">
                            <label class="filter-label">店铺筛选</label>
                            <input type="text" id="shopFilterInput" class="filter-control" placeholder="输入店铺名关键词过滤列表">
                        </div>
                    </div>
                    <div class="filter-group operator-filter-group" style="margin-top: 16px;">
                        <label class="filter-label">运营人员筛选</label>
                        <div id="operatorSelector" class="operator-selector">
                            <!-- 运营人员按钮将通过JS动态生成 -->
                        </div>
                    </div>
                    <div class="filter-group brand-filter-group" style="margin-top: 16px;">
                        <label class="filter-label">品牌筛选</label>
                        <div id="brandSelector" class="brand-selector">
                            <!-- 品牌按钮将通过JS动态生成 -->
                        </div>
                    </div>
                    <div class="filter-group shop-select-group" style="margin-top: 16px;">
                        <label class="filter-label">店铺选择 (已选 <span id="selectedShopCount">0</span> 家)</label>
                        <div id="shopSelectorContainer" class="shop-selector-container">
                            <div id="shopSelector" class="shop-selector-list">
                                <!-- 店铺选择器将通过JS动态生成 -->
                            </div>
                        </div>
                        <div class="shop-select-actions">
                            <button id="selectAllShopsBtn" class="shop-select-btn">全选</button>
                            <button id="deselectAllShopsBtn" class="shop-select-btn">全不选</button>
                        </div>
                    </div>
                </div>

                <div class="charts-container">
                    <!-- 销售额图表 -->
                    <div class="chart-wrapper">
                        <div class="chart-header">
                            <h3 class="chart-title">净销售额趋势</h3>
                            <div class="chart-actions">
                                <button id="refreshSalesBtn" class="chart-action">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"/>
                                    </svg>
                                    刷新数据
                                </button>
                                <button id="exportSalesBtn" class="chart-action">导出净销售数据</button>
                            </div>
                        </div>
                        <div id="salesChart" class="chart-container">
                            <!-- 此处将通过ECharts渲染销售额图表 -->
                        </div>
                    </div>
                    
                    <!-- 推广费图表 -->
                    <div class="chart-wrapper">
                        <div class="chart-header">
                            <h3 class="chart-title">推广费趋势</h3>
                             <div class="chart-actions">
                                <!-- 刷新按钮可以只放一个，或者两个都触发loadData -->
                                <button id="exportPromotionBtn" class="chart-action">导出推广数据</button>
                            </div>
                        </div>
                        <div id="promotionChart" class="chart-container">
                            <!-- 此处将通过ECharts渲染推广费图表 -->
                        </div>
                    </div>
                    
                    <!-- 品牌销售额对比图表 -->
                    <div class="chart-wrapper">
                        <div class="chart-header">
                            <h3 class="chart-title">品牌净销售额对比</h3>
                            <div class="chart-actions">
                                <button id="exportBrandComparisonBtn" class="chart-action">导出品牌对比数据</button>
                            </div>
                        </div>
                        <div id="brandComparisonChart" class="chart-container">
                            <!-- 此处将通过ECharts渲染品牌销售额对比图表 -->
                        </div>
                    </div>
                    
                    <!-- 店铺矩形树图 -->
                    <div class="chart-wrapper">
                        <div class="chart-header">
                            <h3 class="chart-title">店铺净销售额占比</h3>
                            <div class="chart-actions">
                                <button id="exportShopTreemapBtn" class="chart-action">导出店铺占比数据</button>
                            </div>
                        </div>
                        <div id="shopTreemapChart" class="chart-container">
                            <!-- 此处将通过ECharts渲染店铺矩形树图 -->
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        addSalesStatsStyles();

        // 初始化ECharts实例
        initializeECharts();
    }

    // 初始化 ECharts 实例
    function initializeECharts() {
        if (typeof echarts !== 'undefined') {
            const salesChartDom = document.getElementById('salesChart');
            const promotionChartDom = document.getElementById('promotionChart');
            const brandComparisonChartDom = document.getElementById('brandComparisonChart');
            const shopTreemapChartDom = document.getElementById('shopTreemapChart');
            
            if (salesChartDom && promotionChartDom && brandComparisonChartDom && shopTreemapChartDom) {
                salesChartInstance = echarts.init(salesChartDom);
                promotionChartInstance = echarts.init(promotionChartDom);
                brandComparisonChartInstance = echarts.init(brandComparisonChartDom);
                shopTreemapChartInstance = echarts.init(shopTreemapChartDom);
                
                const resizeHandler = () => {
                    salesChartInstance && salesChartInstance.resize();
                    promotionChartInstance && promotionChartInstance.resize();
                    brandComparisonChartInstance && brandComparisonChartInstance.resize();
                    shopTreemapChartInstance && shopTreemapChartInstance.resize();
                };
                window.addEventListener('resize', resizeHandler);
                
                bindEventListeners();
                loadData();
            } else {
                console.error("无法找到一个或多个图表容器");
            }
        } else {
            console.error("ECharts库未加载！尝试动态加载...");
            const script = document.createElement('script');
            script.src = 'echarts.min.js'; // 假设echarts.min.js在static目录下
            script.onload = () => {
                console.log('ECharts库已成功加载');
                initializeECharts(); // 重新尝试初始化
            };
            script.onerror = () => console.error('无法加载ECharts库');
            document.head.appendChild(script);
        }
    }

    // 添加销售统计相关样式
    function addSalesStatsStyles() {
        const styleId = 'sales-stats-styles';
        if (document.getElementById(styleId)) return;
        
        const styleElement = document.createElement('style');
        styleElement.id = styleId;
        styleElement.textContent = `
            .sales-stats-container {
                font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            }
            .filter-panel {
                background-color: #ffffff;
                border-radius: 8px;
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                padding: 16px;
                margin-bottom: 20px;
            }
            .filter-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
                gap: 16px;
            }
            .filter-group {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }
            .filter-label {
                font-size: 14px;
                font-weight: 500;
                margin-bottom: 4px;
                color: #5f6368;
            }
            .filter-control {
                width: 100%;
                padding: 8px;
                border: 1px solid #dadce0;
                border-radius: 4px;
                background-color: white;
                font-size: 14px;
            }
            .time-range-group .chart-actions button {
                flex: 1;
            }
            .shop-filter-group {
                grid-column: span 2; /* 调整布局 */
            }
             @media (max-width: 1200px) { /* 调整断点 */
                .shop-filter-group {
                    grid-column: span 1;
                }
            }
            @media (max-width: 992px) { /* 调整断点 */
                 .filter-grid {
                    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
                 }
                 .shop-filter-group {
                     grid-column: span 1;
                 }
            }
            .operator-filter-group {
                grid-column: 1 / -1;
            }
            .operator-selector {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
            }
            .operator-btn {
                padding: 6px 12px;
                border: 1px solid #dadce0;
                border-radius: 4px;
                background-color: #fff;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.3s;
            }
            .operator-btn.active {
                background-color: #1a73e8;
                color: white;
                border-color: #1a73e8;
                font-weight: 500;
            }
            .operator-btn:hover:not(.active) {
                background-color: #f1f3f4;
            }
            .shop-select-group {
                grid-column: 1 / -1;
            }
            .shop-selector-container {
                max-height: 150px;
                overflow-y: auto;
                border: 1px solid #dadce0;
                border-radius: 4px;
                padding: 10px;
                background-color: #fff;
            }
            .shop-selector-list {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
                gap: 8px;
            }
            .shop-selector-item {
                display: flex;
                align-items: center;
                gap: 5px;
            }
            .shop-checkbox {
                cursor: pointer;
            }
            .shop-label {
                font-size: 14px;
                cursor: pointer;
                flex: 1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .shop-select-actions {
                margin-top: 10px;
                display: flex;
                gap: 10px;
            }
            .shop-select-btn {
                padding: 4px 10px;
                font-size: 12px;
                border: 1px solid #dadce0;
                background-color: #f8f9fa;
                border-radius: 4px;
                cursor: pointer;
            }
            .shop-select-btn:hover {
                background-color: #e9ecef;
            }
            .chart-actions {
                display: flex;
                gap: 8px;
            }
            .chart-action {
                padding: 4px 8px;
                border: 1px solid #dadce0;
                border-radius: 4px;
                background-color: transparent;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.3s;
                display: flex;
                align-items: center;
                gap: 4px;
            }
            .chart-action.active {
                background-color: #1a73e8;
                color: white;
                border-color: #1a73e8;
            }
            .chart-action:hover:not(.active) {
                background-color: rgba(0, 0, 0, 0.05);
            }
            .charts-container {
                display: grid;
                /* 改为单列布局，每个图表占一行 */
                grid-template-columns: 1fr; 
                gap: 16px;
                margin-bottom: 20px;
            }
            .chart-wrapper {
                background-color: #ffffff;
                border-radius: 8px;
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                padding: 16px;
                overflow: hidden;
                 display: flex; /* 使内容垂直排列 */
                 flex-direction: column;
            }
            .chart-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;
                flex-shrink: 0; /* 防止头部缩小 */
            }
            .chart-title {
                font-size: 18px;
                font-weight: 500;
                margin: 0;
                color: #202124;
            }
            .chart-container {
                /* height: 500px; */ /* 改为自适应高度 */
                flex-grow: 1; /* 占据剩余空间 */
                min-height: 550px; /* 增加最小高度 */

                position: relative;
            }
            .loading-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(255, 255, 255, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10;
                 border-radius: 8px; /* 匹配父元素圆角 */
            }
            .spinner {
                width: 40px;
                height: 40px;
                border: 4px solid rgba(0, 0, 0, 0.1);
                border-radius: 50%;
                border-top-color: #1a73e8;
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                to {
                    transform: rotate(360deg);
                }
            }
            @media screen and (max-width: 992px) {
                 /* 单列显示图表 - 这个媒体查询现在对于 grid-template-columns 是多余的，但可以保留 */
                 .charts-container {
                     /* grid-template-columns: 1fr; */ /* 不再需要显式设置 */
                 }
            }
            @media screen and (max-width: 768px) {
                .filter-grid {
                    grid-template-columns: 1fr;
                }
            }
            
            /* 同比环比表格样式 */
            .comparison-table-wrapper {
                margin-top: 20px;
            }
            .table-container {
                overflow-x: auto;
                max-width: 100%;
            }
            .comparison-table {
                width: 100%;
                border-collapse: collapse;
                font-size: 14px;
                text-align: left;
            }
            .comparison-table th {
                background-color: #f8f9fa;
                padding: 10px;
                border-bottom: 2px solid #dee2e6;
                white-space: nowrap;
                text-align: center;
                font-weight: 500;
                color: #495057;
            }
            .comparison-table thead th {
                position: sticky;
                top: 0;
                z-index: 1;
                background-color: #f8f9fa;
            }
            .comparison-table tbody tr:hover {
                background-color: rgba(0, 0, 0, 0.02);
            }
            .comparison-table td {
                padding: 10px;
                border-bottom: 1px solid #dee2e6;
                white-space: nowrap;
            }
            .comparison-table .shop-name {
                font-weight: 500;
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .comparison-table .number-cell {
                text-align: right;
                font-family: 'Consolas', monospace;
            }
            .comparison-table .percent-cell {
                text-align: right;
                font-family: 'Consolas', monospace;
                font-weight: 500;
            }
            .comparison-table .positive {
                color: #28a745;
            }
            .comparison-table .negative {
                color: #dc3545;
            }
            .comparison-table .neutral {
                color: #6c757d;
            }
            .empty-data-message, .empty-message {
                padding: 20px;
                text-align: center;
                color: #6c757d;
                font-style: italic;
            }
            .brand-filter-group {
                grid-column: 1 / -1;
            }
            .brand-selector {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
            }
            .brand-btn {
                padding: 6px 12px;
                border: 1px solid #dadce0;
                border-radius: 4px;
                background-color: #fff;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.3s;
            }
            .brand-btn.active {
                background-color: #1a73e8;
                color: white;
                border-color: #1a73e8;
                font-weight: 500;
            }
            .brand-btn:hover:not(.active) {
                background-color: #f1f3f4;
            }
            .shop-select-group {
                grid-column: 1 / -1;
            }
        `;
        document.head.appendChild(styleElement);
    }

    // 绑定事件监听器
    function bindEventListeners() {
        // 时间范围选择
        document.querySelectorAll('.time-range-btn').forEach(button => {
            button.addEventListener('click', function() {
                document.querySelectorAll('.time-range-btn').forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                chartOptions.days = parseInt(this.getAttribute('data-days'));
                loadData(); // 重新加载数据
            });
        });
        
        // 移除数据类型选择的监听器
        
        // 图表类型选择
        const chartTypeSelect = document.getElementById('chartTypeSelect');
        if (chartTypeSelect) {
            chartTypeSelect.addEventListener('change', function() {
                chartOptions.chartType = this.value;
                 // 饼图不支持堆叠模式，如果选了饼图，隐藏堆叠选项
                 const displayModeGroup = document.getElementById('displayModeSelect').closest('.filter-group');
                 if (displayModeGroup) {
                     displayModeGroup.style.display = (this.value === 'pie') ? 'none' : '';
                 }
                renderChart(); // 重新渲染图表
            });
            // 初始化时检查是否是饼图并更新显示状态
            if (chartOptions.chartType === 'pie') {
                const displayModeGroup = document.getElementById('displayModeSelect').closest('.filter-group');
                if (displayModeGroup) displayModeGroup.style.display = 'none';
            }
        }
        
        // 显示方式选择 (堆叠/分开)
        const displayModeSelect = document.getElementById('displayModeSelect');
        if (displayModeSelect) {
            displayModeSelect.addEventListener('change', function() {
                chartOptions.displayMode = this.value;
                renderChart(); // 重新渲染图表
            });
        }
        
        // 店铺筛选输入
        const shopFilterInput = document.getElementById('shopFilterInput');
        if (shopFilterInput) {
             shopFilterInput.addEventListener('input', debounce(function() {
                const filterValue = this.value.toLowerCase();
                document.querySelectorAll('#shopSelector .shop-selector-item').forEach(item => {
                    const label = item.querySelector('.shop-label');
                    if (label) {
                        const shopName = label.textContent.toLowerCase();
                        item.style.display = shopName.includes(filterValue) ? '' : 'none';
                    }
                });
                 // 筛选时也重新计算饼图（如果当前是饼图模式）
                 if (chartOptions.chartType === 'pie') {
                    //  renderChart(); // 可能需要，取决于是否希望饼图实时反映筛选结果
                 }
            }, 300));
        }

        // 刷新按钮 (现在只有一个主刷新按钮，触发loadData)
        const refreshBtn = document.getElementById('refreshSalesBtn'); // 使用销售区的刷新按钮
        if (refreshBtn) {
            refreshBtn.addEventListener('click', loadData);
        }
        
        // 导出按钮 (分开导出)
        const exportSalesBtn = document.getElementById('exportSalesBtn');
        if (exportSalesBtn) {
            exportSalesBtn.addEventListener('click', () => exportData('sales'));
        }
        const exportPromotionBtn = document.getElementById('exportPromotionBtn');
        if (exportPromotionBtn) {
             exportPromotionBtn.addEventListener('click', () => exportData('promotion'));
        }
        const exportBrandComparisonBtn = document.getElementById('exportBrandComparisonBtn');
        if (exportBrandComparisonBtn) {
            exportBrandComparisonBtn.addEventListener('click', exportBrandComparisonData);
        }
        const exportShopTreemapBtn = document.getElementById('exportShopTreemapBtn');
        if (exportShopTreemapBtn) {
            exportShopTreemapBtn.addEventListener('click', exportShopTreemapData);
        }

        // 全选/全不选按钮
        const selectAllBtn = document.getElementById('selectAllShopsBtn');
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', () => toggleAllShops(true));
        }
        const deselectAllBtn = document.getElementById('deselectAllShopsBtn');
        if (deselectAllBtn) {
            deselectAllBtn.addEventListener('click', () => toggleAllShops(false));
        }
    }

    // 切换所有可见店铺的选择状态
    function toggleAllShops(select) {
        const visibleCheckboxes = document.querySelectorAll('#shopSelector .shop-selector-item input[type="checkbox"]:not(:disabled)');
        let changed = false;
        visibleCheckboxes.forEach(checkbox => {
            if (checkbox.closest('.shop-selector-item').style.display !== 'none') {
                 if (checkbox.checked !== select) {
                     checkbox.checked = select;
                     // 手动触发 change 事件以更新 selectedShops
                     checkbox.dispatchEvent(new Event('change', { bubbles: true })); // 确保事件冒泡
                     changed = true;
                 }
            }
        });
         // 只有在实际更改了选择时才重新渲染
         // if (changed) {
             //  updateSelectedShopCount(); // change 事件应该已经触发了这个
             //  renderChart(); // change 事件应该已经触发了这个
         // }
    }
    
    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this, args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }

    // 更新图表标题 (不再需要根据dataType更新)
    /*
    function updateChartTitle() {
        // ...
    }
    */

    // 加载数据
    async function loadData() {
        try {
            showLoading(); // 在图表上显示加载
            let url = `/api/shop-trend-data?days=${chartOptions.days}`;
            
            if (chartOptions.operatorFilter && chartOptions.operatorFilter !== 'all') {
                url += `&operator=${encodeURIComponent(chartOptions.operatorFilter)}`;
            }
            
            if (chartOptions.brandFilter && chartOptions.brandFilter !== 'all') {
                url += `&brand=${encodeURIComponent(chartOptions.brandFilter)}`;
            }
            
            const response = await fetch(url);
            const result = await response.json();
            
            if (result.success) {
                currentData = result.data;
                
                // 检查返回的数据结构是否包含 salesData 和 promotionData
                if (!currentData.salesData || !currentData.promotionData) {
                     console.error("API返回的数据缺少 salesData 或 promotionData");
                     showError("获取数据格式错误，请检查API");
                     return; // 提前退出，防止后续渲染错误
                }
                
                // 处理退款数据，计算净销售额
                // 检查API是否返回了退款数据
                if (currentData.refundData) {
                    // 如果有退款数据，则用销售额减去退款额得到净销售额
                    for (const shop in currentData.salesData) {
                        const shopSales = currentData.salesData[shop];
                        const shopRefunds = currentData.refundData[shop] || Array(shopSales.length).fill(0);
                        
                        // 确保退款数组长度与销售数组相同
                        if (shopRefunds.length !== shopSales.length) {
                            console.warn(`${shop}的退款数据长度(${shopRefunds.length})与销售数据长度(${shopSales.length})不一致，使用0填充`);
                            while (shopRefunds.length < shopSales.length) shopRefunds.push(0);
                        }
                        
                        // 计算净销售额 = 销售额 - 退款额
                        currentData.salesData[shop] = shopSales.map((sale, index) => 
                            Math.max(0, (sale || 0) - (shopRefunds[index] || 0))
                        );
                    }
                }
                // 如果API没有退款数据，那么我们假定销售额就是净销售额，不做额外处理

                renderOperatorSelector(currentData.operators || []);
                renderBrandSelector(currentData.brands || []); // 渲染品牌选择器

                const previousSelectedShops = new Set(selectedShops);
                renderShopSelector(); 
                // 保留有效的旧选择，或者在运营筛选/品牌筛选变化时重置
                 if (chartOptions.operatorFilter === this.dataset?.operator && 
                     chartOptions.brandFilter === this.dataset?.brand) { // 只有在非筛选变化时保留
                     selectedShops = [...previousSelectedShops].filter(shop => currentData.shops.includes(shop));
                 } else { // 如果是筛选导致的数据加载，清空选择
                     selectedShops = [];
                 }

                if (selectedShops.length === 0 && currentData.shops.length > 0) {
                    selectedShops = currentData.shops.slice(0, Math.min(10, currentData.shops.length));
                }
                
                // 计算同比环比数据
                calculateComparisonData();
                
                updateShopCheckboxStates();
                updateSelectedShopCount();
                renderChart(); // 渲染销售和推广图表
                renderBrandComparisonChart(); // 渲染品牌对比图表
                renderShopTreemapChart(); // 渲染店铺矩形树图
                renderComparisonTable(); // 渲染同比环比表格
            } else {
                showError(result.message || '获取数据失败');
            }
        } catch (error) {
            console.error('加载数据失败:', error);
            showError('加载数据失败，请稍后重试');
        } finally {
            hideLoading(); // 隐藏图表的加载
        }
    }

    // 渲染运营人员选择器
    function renderOperatorSelector(operators) {
        const container = document.getElementById('operatorSelector');
        if (!container) return;
        container.innerHTML = ''; 

        const allBtn = document.createElement('button');
        allBtn.textContent = '全部运营';
        allBtn.className = 'operator-btn';
        allBtn.dataset.operator = 'all';
        if (!chartOptions.operatorFilter || chartOptions.operatorFilter === 'all') {
            allBtn.classList.add('active');
        }
        allBtn.addEventListener('click', handleOperatorClick);
        container.appendChild(allBtn);

        operators.forEach(op => {
            const btn = document.createElement('button');
            btn.textContent = op;
            btn.className = 'operator-btn';
            btn.dataset.operator = op;
            if (chartOptions.operatorFilter === op) {
                btn.classList.add('active');
            }
            btn.addEventListener('click', handleOperatorClick);
            container.appendChild(btn);
        });
    }

    // 渲染品牌选择器
    function renderBrandSelector(brands) {
        const container = document.getElementById('brandSelector');
        if (!container) return;
        container.innerHTML = ''; 

        const allBtn = document.createElement('button');
        allBtn.textContent = '全部品牌';
        allBtn.className = 'brand-btn';
        allBtn.dataset.brand = 'all';
        if (!chartOptions.brandFilter || chartOptions.brandFilter === 'all') {
            allBtn.classList.add('active');
        }
        allBtn.addEventListener('click', handleBrandClick);
        container.appendChild(allBtn);

        brands.forEach(brand => {
            const btn = document.createElement('button');
            btn.textContent = brand;
            btn.className = 'brand-btn';
            btn.dataset.brand = brand;
            if (chartOptions.brandFilter === brand) {
                btn.classList.add('active');
            }
            btn.addEventListener('click', handleBrandClick);
            container.appendChild(btn);
        });
    }

    // 处理运营人员按钮点击
    function handleOperatorClick() {
        const selectedOperator = this.dataset.operator;
        if (chartOptions.operatorFilter !== selectedOperator) {
            chartOptions.operatorFilter = selectedOperator;
            document.querySelectorAll('.operator-btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            // 标记是运营筛选导致的加载，以便 loadData 清空店铺选择
            loadData.call(this); // 传递 this 上下文，以便 loadData 知道是哪个按钮触发的
        }
    }

    // 处理品牌按钮点击
    function handleBrandClick() {
        const selectedBrand = this.dataset.brand;
        if (chartOptions.brandFilter !== selectedBrand) {
            chartOptions.brandFilter = selectedBrand;
            document.querySelectorAll('.brand-btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            // 标记是品牌筛选导致的加载
            loadData.call(this);
        }
    }

    // 渲染店铺选择器
    function renderShopSelector() {
        const container = document.getElementById('shopSelector');
        if (!container || !currentData || !currentData.shops) return;
        container.innerHTML = '';

        currentData.shops.forEach((shop, index) => {
            const id = `shop-checkbox-${index}`;
            const itemDiv = document.createElement('div');
            itemDiv.className = 'shop-selector-item';

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.id = id;
            checkbox.value = shop;
            checkbox.className = 'shop-checkbox';

            checkbox.addEventListener('change', function() {
                if (this.checked) {
                    if (!selectedShops.includes(shop)) selectedShops.push(shop);
                } else {
                    selectedShops = selectedShops.filter(s => s !== shop);
                }
                updateSelectedShopCount();
                renderChart(); // 选择变化时更新图表
                renderComparisonTable(); // 同时更新同比环比表格
            });

            const label = document.createElement('label');
            label.className = 'shop-label';
            label.setAttribute('for', id);
            label.textContent = shop;
            label.title = shop;

            itemDiv.appendChild(checkbox);
            itemDiv.appendChild(label);
            container.appendChild(itemDiv);
        });
        
        const filterInput = document.getElementById('shopFilterInput');
        if (filterInput && filterInput.value) {
             filterInput.dispatchEvent(new Event('input'));
        }
    }
    
    // 更新店铺选择器的选中状态
    function updateShopCheckboxStates() {
         document.querySelectorAll('#shopSelector .shop-checkbox').forEach(checkbox => {
             checkbox.checked = selectedShops.includes(checkbox.value);
         });
    }
    
    // 更新已选店铺计数
    function updateSelectedShopCount() {
        const countElement = document.getElementById('selectedShopCount');
        if (countElement) {
            countElement.textContent = selectedShops.length;
        }
    }

    // --- 重构 renderChart 函数 ---
    function renderChart() {
        if (!salesChartInstance || !promotionChartInstance || !currentData || !currentData.dates) {
            console.warn("图表实例或数据未准备好");
            return;
        }
        
         if (!currentData.salesData || !currentData.promotionData) {
             console.error("渲染图表失败：缺少 salesData 或 promotionData");
             showError("数据不完整，无法渲染图表");
             return;
         }

        // 清除之前的选项，特别是对于饼图切换回其他类型时
        salesChartInstance.clear();
        promotionChartInstance.clear();
        
        if (chartOptions.chartType === 'pie') {
            renderPieCharts();
        } else {
            renderTrendCharts();
        }
    }

    // 渲染趋势图 (折线/柱状/面积)
    function renderTrendCharts() {
        const isStack = chartOptions.displayMode === 'stack';
        const chartType = chartOptions.chartType === 'area' ? 'line' : chartOptions.chartType; // area 内部用 line 实现

        // --- 销售额图表 ---
        const salesSeries = selectedShops.map((shop, index) => {
            // 检查是否有退款数据
            if (currentData.refundData && currentData.refundData[shop]) {
                // 计算净销售额 = 销售额 - 退款额
                const netSales = (currentData.salesData[shop] || []).map((sale, i) => {
                    const refund = currentData.refundData[shop][i] || 0;
                    return Math.max(0, (sale || 0) - refund);
                });
                return createSeriesConfig(shop, index, netSales, chartType, isStack);
            } else {
                // 没有退款数据，直接使用销售额
                return createSeriesConfig(shop, index, currentData.salesData[shop] || [], chartType, isStack);
            }
        });
        
        const salesOption = createTrendChartOption('净销售额 (元)', currentData.dates, salesSeries, selectedShops);
        salesChartInstance.setOption(salesOption, true);

        // --- 推广费图表 ---
        const promotionSeries = selectedShops.map((shop, index) => 
            createSeriesConfig(shop, index, currentData.promotionData[shop] || [], chartType, isStack)
        );
        const promotionOption = createTrendChartOption('推广费 (元)', currentData.dates, promotionSeries, selectedShops);
        promotionChartInstance.setOption(promotionOption, true);
    }

    // 创建趋势图系列的配置
    function createSeriesConfig(shopName, index, data, chartType, isStack) {
        let seriesConfig = {
            name: shopName,
            type: chartType,
            data: data,
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
                color: colorPalette[index % colorPalette.length]
            },
            emphasis: { focus: 'series' }
        };

        if (chartOptions.chartType === 'area') { // 注意这里用原始选项判断
            seriesConfig.areaStyle = {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: `${colorPalette[index % colorPalette.length]}99` },
                    { offset: 1, color: `${colorPalette[index % colorPalette.length]}11` }
                ]),
                 opacity: isStack ? 0.8 : 0.5 // 堆叠时透明度稍高
            };
        }
        
        if (isStack && (chartType === 'line' || chartType === 'bar')) {
            seriesConfig.stack = 'total';
            if (chartOptions.chartType === 'area') { // 确保面积图堆叠有areaStyle
                if(!seriesConfig.areaStyle) seriesConfig.areaStyle = {}; // 初始化，以防万一
                 seriesConfig.areaStyle.opacity = 0.8;
            }
             if (chartType === 'bar') {
                 seriesConfig.label = {
                     show: true, position: 'inside', formatter: '{c}', 
                     color: '#fff', fontSize: 10,
                     formatter: function(params) { // 只显示大于0的标签
                         return params.value > 0 ? formatNumber(params.value) : '';
                     }
                 };
             }
        }
        return seriesConfig;
    }

    // 创建趋势图的基础配置
    function createTrendChartOption(yAxisName, dates, series, legendData) {
        return {
            tooltip: {
                trigger: 'axis',
                axisPointer: { type: 'cross', label: { backgroundColor: '#6a7985' } },
                formatter: function(params) { // 优化 tooltip 显示
                     let result = `<div style="font-weight:bold;margin-bottom:5px;">${params[0].axisValue}</div>`;
                     params.sort((a, b) => b.value - a.value); // 按值排序
                     params.forEach(param => {
                         const value = param.value;
                         const formattedValue = formatCurrency(value); // 统一用货币格式
                         const color = param.color;
                         result += `<div style="display:flex;align-items:center;margin:3px 0;">
                             <span style="display:inline-block;margin-right:5px;width:10px;height:10px;border-radius:50%;background-color:${color};"></span>
                             <span style="flex:1;padding-right:10px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;" title="${param.seriesName}">${param.seriesName}:</span>
                             <span style="font-weight:bold;">${formattedValue}</span>
                         </div>`;
                     });
                     return result;
                 }
            },
            legend: {
                data: legendData,
                top: 10, type: 'scroll', orient: 'horizontal', padding: [5, 80],
                selector: [{ type: 'all', title: '全选' }, { type: 'inverse', title: '反选' }]
            },
            grid: { left: '3%', right: '4%', bottom: '10%', containLabel: true }, // 稍微增大底部边距给 dataZoom 留空间
            toolbox: {
                feature: {
                    saveAsImage: { title: '保存为图片', name: `${yAxisName.split(' ')[0]}趋势图-${new Date().toLocaleDateString()}` },
                    dataZoom: { title: { zoom: '区域缩放', back: '区域缩放还原' } },
                    restore: { title: '还原' }
                },
                right: 20
            },
            dataZoom: [
                { type: 'inside', start: 0, end: 100 },
                { type: 'slider', start: 0, end: 100, bottom: 5 } // 使用 slider 类型 dataZoom
            ],
            xAxis: {
                type: 'category',
                boundaryGap: chartOptions.chartType === 'bar', // 柱状图需要间隙
                data: dates,
                axisLine: { lineStyle: { color: '#999' } },
                axisLabel: { color: '#666', formatter: value => value } // 可以考虑日期格式化
            },
            yAxis: {
                type: 'value',
                name: yAxisName,
                axisLabel: { formatter: value => formatCurrency(value, true) }, // 使用简化格式
                splitLine: { lineStyle: { type: 'dashed', color: '#eee' } }
            },
            series: series
        };
    }

    // 渲染饼图
    function renderPieCharts() {
        if (selectedShops.length === 0) {
             showError("请至少选择一个店铺以查看饼图汇总数据", salesChartInstance);
             showError("请至少选择一个店铺以查看饼图汇总数据", promotionChartInstance);
             return;
         }

        // 计算总销售额和总推广费
        const salesTotals = {};
        const promotionTotals = {};
        selectedShops.forEach(shop => {
            // 检查是否有退款数据
            if (currentData.refundData && currentData.refundData[shop]) {
                // 计算净销售额 = 销售额 - 退款额
                const netSales = (currentData.salesData[shop] || []).map((sale, i) => {
                    const refund = currentData.refundData[shop][i] || 0;
                    return Math.max(0, (sale || 0) - refund);
                });
                salesTotals[shop] = netSales.reduce((sum, val) => sum + (val || 0), 0);
            } else {
                // 没有退款数据，直接使用销售额
                salesTotals[shop] = (currentData.salesData[shop] || []).reduce((sum, val) => sum + (val || 0), 0);
            }
            
            promotionTotals[shop] = (currentData.promotionData[shop] || []).reduce((sum, val) => sum + (val || 0), 0);
        });

        // 准备饼图数据
        const salesPieData = selectedShops
            .map(shop => ({ value: salesTotals[shop], name: shop }))
            .filter(item => item.value > 0); // 过滤掉0值

        const promotionPieData = selectedShops
            .map(shop => ({ value: promotionTotals[shop], name: shop }))
            .filter(item => item.value > 0); // 过滤掉0值

        // --- 销售额饼图 ---
         if (salesPieData.length > 0) {
             const salesPieOption = createPieChartOption('净销售额总占比', salesPieData);
             salesChartInstance.setOption(salesPieOption, true);
         } else {
             showError("所选店铺在当前时间范围内无净销售额数据", salesChartInstance);
         }

        // --- 推广费饼图 ---
         if (promotionPieData.length > 0) {
             const promotionPieOption = createPieChartOption('推广费总占比', promotionPieData);
             promotionChartInstance.setOption(promotionPieOption, true);
         } else {
              showError("所选店铺在当前时间范围内无推广费数据", promotionChartInstance);
         }
    }

    // 创建饼图的基础配置
    function createPieChartOption(titleText, data) {
         const totalValue = data.reduce((sum, item) => sum + item.value, 0);
        return {
            title: {
                text: titleText,
                subtext: `总计: ${formatCurrency(totalValue)}`,
                left: 'center'
            },
            tooltip: {
                trigger: 'item',
                 formatter: function(params) {
                     const percent = totalValue > 0 ? ((params.value / totalValue) * 100).toFixed(1) : 0;
                     return `${params.name}<br/>${formatCurrency(params.value)} (${percent}%)`;
                 }
            },
            legend: {
                orient: 'vertical',
                left: 'left',
                top: 'middle', // 调整图例位置
                data: data.map(item => item.name),
                type: 'scroll' // 支持滚动
            },
            toolbox: {
                 feature: {
                     saveAsImage: { title: '保存为图片', name: `${titleText}-${new Date().toLocaleDateString()}` }
                 },
                 right: 20
             },
            series: [
                {
                    name: titleText,
                    type: 'pie',
                    radius: '65%', // 调整饼图大小
                    center: ['60%', '55%'], // 调整饼图位置，给图例留空间
                    data: data.sort((a, b) => b.value - a.value), // 按值排序
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                     label: {
                         formatter: '{b}: {d}%', // 显示名称和百分比
                         fontSize: 12
                     },
                     labelLine: {
                         length: 8,
                         length2: 5
                     }
                }
            ]
        };
    }

    // 导出数据 (根据 dataType 导出)
    function exportData(dataType) { // dataType: 'sales' or 'promotion'
        if (!currentData || !currentData.dates || selectedShops.length === 0) {
            showError('没有选中的店铺数据可导出');
            return;
        }
        
        const dataTypeName = dataType === 'sales' ? '净销售额' : '推广费';

        try {
            let csvContent = "data:text/csv;charset=utf-8,\uFEFF"; 
            
            let headers = ["日期"];
            selectedShops.forEach(shop => headers.push(`${shop} - ${dataTypeName}`));
            csvContent += headers.join(',') + "\n";
            
            currentData.dates.forEach((date, index) => {
                let row = [date];
                selectedShops.forEach(shop => {
                    if (dataType === 'sales' && currentData.refundData && currentData.refundData[shop]) {
                        // 计算净销售额
                        const sale = currentData.salesData[shop]?.[index] || 0;
                        const refund = currentData.refundData[shop][index] || 0;
                        const netSale = Math.max(0, sale - refund);
                        row.push(netSale);
                    } else if (dataType === 'sales') {
                        // 没有退款数据，直接使用销售数据
                        const shopData = currentData.salesData[shop] || [];
                        row.push(shopData[index] !== undefined ? shopData[index] : 0);
                    } else {
                        // 推广费数据
                        const shopData = currentData.promotionData[shop] || [];
                        row.push(shopData[index] !== undefined ? shopData[index] : 0);
                    }
                });
                csvContent += row.join(',') + "\n";
            });
            
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `${dataTypeName}趋势数据-${selectedShops.length}店-${new Date().toLocaleDateString()}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } catch (error) {
            console.error(`导出${dataTypeName}数据失败:`, error);
            showError(`导出${dataTypeName}数据失败`);
        }
    }

    // 显示加载中 (在图表容器上)
    function showLoading() {
        const containers = [
            document.getElementById('salesChart'), 
            document.getElementById('promotionChart'),
            document.getElementById('brandComparisonChart'),
            document.getElementById('shopTreemapChart')
        ];
        containers.forEach(container => {
            if (!container || container.querySelector('.loading-overlay')) return;
            const loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'loading-overlay';
            const spinner = document.createElement('div');
            spinner.className = 'spinner';
            loadingOverlay.appendChild(spinner);
            container.appendChild(loadingOverlay);
        });
    }

    // 隐藏加载中 (从图表容器移除)
    function hideLoading() {
        document.querySelectorAll('.loading-overlay').forEach(overlay => overlay.remove());
    }

    // 显示错误信息 (可以在指定图表上显示或全局 alert)
    function showError(message, chartInstance = null) {
        console.error("错误:", message);
        if (chartInstance && chartInstance.getDom()) {
             // 尝试在图表上显示错误信息
             chartInstance.clear(); // 清除之前的图表
             chartInstance.showLoading('default', {
                 text: message,
                 color: '#d62728',
                 textColor: '#333',
                 maskColor: 'rgba(255, 255, 255, 0.8)',
                 zlevel: 0
             });
         } else {
             // 全局提示
             // 查找或创建一个提示区域
             let toastContainer = document.getElementById('global-toast-container');
             if (!toastContainer) {
                 toastContainer = document.createElement('div');
                 toastContainer.id = 'global-toast-container';
                 toastContainer.style.position = 'fixed';
                 toastContainer.style.top = '20px';
                 toastContainer.style.right = '20px';
                 toastContainer.style.zIndex = '9999';
                 document.body.appendChild(toastContainer);
             }
             const toast = document.createElement('div');
             toast.style.backgroundColor = '#f8d7da';
             toast.style.color = '#721c24';
             toast.style.padding = '10px 15px';
             toast.style.marginBottom = '10px';
             toast.style.borderRadius = '4px';
             toast.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
             toast.textContent = message;
             toastContainer.appendChild(toast);
             setTimeout(() => toast.remove(), 5000); // 5秒后自动消失
         }
    }

    // 格式化货币 (添加可选的简化参数)
    function formatCurrency(value, simplify = false) {
        if (value === undefined || value === null || isNaN(value)) return simplify ? '¥0' : '¥0.00';
        
        value = parseFloat(value);

        if (simplify) {
             if (value >= 100000000) { // 亿
                 return '¥' + (value / 100000000).toFixed(1) + '亿';
             } else if (value >= 10000) { // 万
                 return '¥' + (value / 10000).toFixed(1) + '万';
             } else {
                 return '¥' + value.toFixed(0); // 千以下不带小数
             }
        } else {
             return '¥' + value.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
        }
    }

    // 格式化数字 (用于标签等)
    function formatNumber(value) {
        if (value === undefined || value === null || isNaN(value)) return '0';
        value = Math.round(value); // 四舍五入到整数
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    // 计算同比环比数据
    function calculateComparisonData() {
        // 如果API已经返回了同比环比数据，则直接使用
        if (currentData.comparisonData) {
            comparisonData = {
                sales: {},     // 净销售额同比环比
                promotion: {}, // 推广费同比环比
                roi: {}        // ROI同比环比 (净销售额/推广费)
            };

            // 转换API返回的数据格式以适应现有代码
            for (const shop in currentData.comparisonData) {
                const shopData = currentData.comparisonData[shop];
                
                comparisonData.sales[shop] = {
                    current: shopData.sales.current,
                    previous: shopData.sales.yoyRef, // 使用同比参考值作为previous
                    yoy: shopData.sales.yoy,
                    mom: shopData.sales.mom
                };
                
                comparisonData.promotion[shop] = {
                    current: shopData.promotion.current,
                    previous: shopData.promotion.yoyRef,
                    yoy: shopData.promotion.yoy,
                    mom: shopData.promotion.mom
                };
                
                comparisonData.roi[shop] = {
                    current: shopData.roi.current,
                    previous: shopData.roi.yoyRef,
                    yoy: shopData.roi.yoy,
                    mom: shopData.roi.mom
                };
            }
            return;
        }
        
        // 以下是原有的计算逻辑，当API没有返回同比环比数据时使用
        comparisonData = {
            sales: {},     // 净销售额同比环比
            promotion: {}, // 推广费同比环比
            roi: {}        // ROI同比环比 (净销售额/推广费)
        };
        
        if (!currentData || !currentData.dates || !currentData.salesData || !currentData.promotionData) {
            console.warn("无法计算同比环比数据：数据不完整");
            return;
        }
        
        // 根据所选时间区间，确定计算方式
        const days = chartOptions.days;
        // 只有当有足够的数据时才计算同比数据
        const hasYearOnYearData = currentData.dates.length >= days * 2;
        // 只有当有足够的数据时才计算环比数据
        const hasMoMData = currentData.dates.length >= days * 2;

        // 获取每个店铺的当前周期和上一周期数据
        for (const shop of currentData.shops) {
            const salesData = currentData.salesData[shop] || [];
            const promotionData = currentData.promotionData[shop] || [];
            const refundData = currentData.refundData?.[shop] || Array(salesData.length).fill(0); // 获取退款数据，如果没有则使用0
            
            if (salesData.length < days || promotionData.length < days) {
                continue; // 跳过数据不足的店铺
            }
            
            // 初始化该店铺的比较数据
            comparisonData.sales[shop] = { current: 0, previous: 0, yoy: 0, mom: 0 };
            comparisonData.promotion[shop] = { current: 0, previous: 0, yoy: 0, mom: 0 };
            comparisonData.roi[shop] = { current: 0, previous: 0, yoy: 0, mom: 0 };
            
            // 计算净销售额 = 销售额 - 退款额
            const netSalesData = salesData.map((sale, index) => 
                Math.max(0, (sale || 0) - (refundData[index] || 0))
            );
            
            // 当前周期的总和
            const currentSalesTotal = netSalesData.slice(0, days).reduce((sum, val) => sum + (val || 0), 0);
            const currentPromotionTotal = promotionData.slice(0, days).reduce((sum, val) => sum + (val || 0), 0);
            
            // 存储当前周期数据
            comparisonData.sales[shop].current = currentSalesTotal;
            comparisonData.promotion[shop].current = currentPromotionTotal;
            comparisonData.roi[shop].current = currentPromotionTotal > 0 
                ? (currentSalesTotal / currentPromotionTotal)
                : 0;
                
            // 计算同比数据 (去年同期)
            if (hasYearOnYearData) {
                // 这里假设API返回的数据已经包含了去年同期的数据
                // 实际项目中需要根据API的返回情况调整
                // 这里简化处理，假设当前日期后面的数据是去年同期
                const yoySalesTotal = netSalesData.slice(days, days * 2).reduce((sum, val) => sum + (val || 0), 0);
                const yoyPromotionTotal = promotionData.slice(days, days * 2).reduce((sum, val) => sum + (val || 0), 0);
                
                comparisonData.sales[shop].previous = yoySalesTotal;
                comparisonData.promotion[shop].previous = yoyPromotionTotal;
                comparisonData.roi[shop].previous = yoyPromotionTotal > 0
                    ? (yoySalesTotal / yoyPromotionTotal)
                    : 0;
                    
                // 计算同比增长率
                comparisonData.sales[shop].yoy = yoySalesTotal > 0
                    ? ((currentSalesTotal - yoySalesTotal) / yoySalesTotal * 100)
                    : (currentSalesTotal > 0 ? 100 : 0);
                    
                comparisonData.promotion[shop].yoy = yoyPromotionTotal > 0
                    ? ((currentPromotionTotal - yoyPromotionTotal) / yoyPromotionTotal * 100)
                    : (currentPromotionTotal > 0 ? 100 : 0);
                    
                comparisonData.roi[shop].yoy = comparisonData.roi[shop].previous > 0
                    ? ((comparisonData.roi[shop].current - comparisonData.roi[shop].previous) / comparisonData.roi[shop].previous * 100)
                    : (comparisonData.roi[shop].current > 0 ? 100 : 0);
            }
            
            // 计算环比数据 (上一个周期)
            if (hasMoMData) {
                // 这里同样简化处理，使用滑动窗口方式计算上一个相同时间长度的周期
                const previousSalesTotal = netSalesData.slice(days, days * 2).reduce((sum, val) => sum + (val || 0), 0);
                const previousPromotionTotal = promotionData.slice(days, days * 2).reduce((sum, val) => sum + (val || 0), 0);
                
                // 计算环比增长率
                comparisonData.sales[shop].mom = previousSalesTotal > 0
                    ? ((currentSalesTotal - previousSalesTotal) / previousSalesTotal * 100)
                    : (currentSalesTotal > 0 ? 100 : 0);
                    
                comparisonData.promotion[shop].mom = previousPromotionTotal > 0
                    ? ((currentPromotionTotal - previousPromotionTotal) / previousPromotionTotal * 100)
                    : (currentPromotionTotal > 0 ? 100 : 0);
                
                // 修改这部分为ROI的环比计算
                const previousROI = previousPromotionTotal > 0 
                    ? (previousSalesTotal / previousPromotionTotal)
                    : 0;
                    
                comparisonData.roi[shop].mom = previousROI > 0
                    ? ((comparisonData.roi[shop].current - previousROI) / previousROI * 100)
                    : (comparisonData.roi[shop].current > 0 ? 100 : 0);
            }
        }
    }
    
    // 渲染同比环比表格
    function renderComparisonTable() {
        // 查找表格容器，如果不存在则创建
        let tableContainer = document.querySelector('.comparison-table-wrapper');
        
        // 如果没有选中的店铺或者没有对比数据，则不显示表格
        if (selectedShops.length === 0 || Object.keys(comparisonData.sales).length === 0) {
            if (tableContainer) {
                tableContainer.innerHTML = '<div class="empty-data-message">选择店铺后显示同比环比数据</div>';
            }
            return;
        }
        
        // 如果尚未添加表格容器，则添加到图表容器后面
        if (!tableContainer) {
            const chartsContainer = document.querySelector('.charts-container');
            if (!chartsContainer) return;
            
            tableContainer = document.createElement('div');
            tableContainer.className = 'comparison-table-wrapper chart-wrapper';
            chartsContainer.appendChild(tableContainer);
        }
        
        // 创建表格内容
        const tableHTML = `
            <div class="chart-header">
                <h3 class="chart-title">店铺同比环比数据</h3>
                <div class="chart-actions">
                    <button id="exportComparisonBtn" class="chart-action">导出对比数据</button>
                </div>
            </div>
            <div class="table-container">
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th rowspan="2">店铺名称</th>
                            <th colspan="3">净销售额</th>
                            <th colspan="3">推广费</th>
                            <th colspan="3">ROI (销售/推广)</th>
                        </tr>
                        <tr>
                            <th>当前值 (元)</th>
                            <th>同比<br><small>昨日/前日</small></th>
                            <th>环比<br><small>昨日/上周昨日</small></th>
                            <th>当前值 (元)</th>
                            <th>同比<br><small>昨日/前日</small></th>
                            <th>环比<br><small>昨日/上周昨日</small></th>
                            <th>当前值</th>
                            <th>同比<br><small>昨日/前日</small></th>
                            <th>环比<br><small>昨日/上周昨日</small></th>
                        </tr>
                    </thead>
                    <tbody>
                        ${generateTableRows()}
                    </tbody>
                </table>
            </div>
        `;
        
        tableContainer.innerHTML = tableHTML;
        
        // 添加导出按钮事件监听
        const exportBtn = document.getElementById('exportComparisonBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', exportComparisonData);
        }
    }
    
    // 生成表格行
    function generateTableRows() {
        let rowsHTML = '';
        
        // 只为选中的店铺生成行
        for (const shop of selectedShops) {
            // 检查该店铺是否有对比数据
            if (!comparisonData.sales[shop]) {
                continue;
            }
            
            const salesData = comparisonData.sales[shop];
            const promotionData = comparisonData.promotion[shop];
            const roiData = comparisonData.roi[shop];
            
            rowsHTML += `
                <tr>
                    <td class="shop-name">${shop}</td>
                    <td class="number-cell">${formatCurrency(salesData.current)}</td>
                    <td class="percent-cell ${getPercentClass(salesData.yoy)}">${formatPercent(salesData.yoy)}</td>
                    <td class="percent-cell ${getPercentClass(salesData.mom)}">${formatPercent(salesData.mom)}</td>
                    <td class="number-cell">${formatCurrency(promotionData.current)}</td>
                    <td class="percent-cell ${getPercentClass(promotionData.yoy)}">${formatPercent(promotionData.yoy)}</td>
                    <td class="percent-cell ${getPercentClass(promotionData.mom)}">${formatPercent(promotionData.mom)}</td>
                    <td class="number-cell">${roiData.current.toFixed(2)}</td>
                    <td class="percent-cell ${getPercentClass(roiData.yoy)}">${formatPercent(roiData.yoy)}</td>
                    <td class="percent-cell ${getPercentClass(roiData.mom)}">${formatPercent(roiData.mom)}</td>
                </tr>
            `;
        }
        
        if (rowsHTML === '') {
            rowsHTML = `<tr><td colspan="10" class="empty-message">暂无同比环比数据</td></tr>`;
        }
        
        return rowsHTML;
    }
    
    // 获取百分比颜色类
    function getPercentClass(percent) {
        if (percent > 0) return 'positive';
        if (percent < 0) return 'negative';
        return 'neutral';
    }
    
    // 格式化百分比
    function formatPercent(value) {
        if (value === undefined || value === null || isNaN(value)) return '0%';
        
        // 添加正负号
        const sign = value > 0 ? '+' : '';
        return `${sign}${value.toFixed(2)}%`;
    }
    
    // 导出同比环比数据
    function exportComparisonData() {
        if (!comparisonData || selectedShops.length === 0) {
            showError('没有同比环比数据可导出');
            return;
        }
        
        try {
            let csvContent = "data:text/csv;charset=utf-8,\uFEFF";
            
            // 添加表头
            let headers = ["店铺名称", 
                "净销售额 (元)", "净销售额同比 (%)", "净销售额环比 (%)",
                "推广费 (元)", "推广费同比 (%)", "推广费环比 (%)",
                "ROI", "ROI同比 (%)", "ROI环比 (%)"];
            csvContent += headers.join(',') + "\n";
            
            // 添加数据行
            for (const shop of selectedShops) {
                if (!comparisonData.sales[shop]) continue;
                
                const salesData = comparisonData.sales[shop];
                const promotionData = comparisonData.promotion[shop];
                const roiData = comparisonData.roi[shop];
                
                let row = [
                    `"${shop}"`,
                    salesData.current,
                    salesData.yoy,
                    salesData.mom,
                    promotionData.current,
                    promotionData.yoy,
                    promotionData.mom,
                    roiData.current,
                    roiData.yoy,
                    roiData.mom
                ];
                
                csvContent += row.join(',') + "\n";
            }
            
            // 触发下载
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `店铺同比环比数据-${new Date().toLocaleDateString()}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } catch (error) {
            console.error('导出同比环比数据失败:', error);
            showError('导出同比环比数据失败');
        }
    }

    // 渲染品牌净销售额对比图表
    function renderBrandComparisonChart() {
        if (!brandComparisonChartInstance || !currentData || !currentData.brands) {
            console.warn("品牌对比图表实例或数据未准备好");
            return;
        }
        
        // 清除之前的图表
        brandComparisonChartInstance.clear();
        
        // 计算每个品牌下所有店铺的总销售额
        const brandSalesData = {};
        
        // 初始化每个品牌的销售额为0
        (currentData.brands || []).forEach(brand => {
            brandSalesData[brand] = 0;
        });
        
        // 统计每个店铺的销售额并按品牌归类
        for (const shop in currentData.salesData) {
            // 获取店铺对应的品牌
            const shopBrand = getShopBrand(shop);
            if (!shopBrand) continue; // 如果没有找到对应品牌，跳过
            
            // 计算该店铺在当前时间段内的总销售额
            const shopSales = (currentData.salesData[shop] || []).reduce((sum, val) => sum + (val || 0), 0);
            
            // 将店铺销售额加到对应品牌
            if (brandSalesData[shopBrand] !== undefined) {
                brandSalesData[shopBrand] += shopSales;
            } else {
                brandSalesData[shopBrand] = shopSales;
            }
        }
        
        // 转换数据为ECharts所需格式
        const seriesData = Object.entries(brandSalesData)
            .filter(([brand, sales]) => sales > 0) // 过滤掉销售额为0的品牌
            .sort((a, b) => b[1] - a[1]) // 按销售额从高到低排序
            .map(([brand, sales]) => ({
                name: brand,
                value: sales
            }));
            
        // 如果没有有效数据，显示提示信息
        if (seriesData.length === 0) {
            showError("当前没有品牌销售额数据可显示", brandComparisonChartInstance);
            return;
        }
        
        // 创建品牌对比图表配置
        const option = {
            title: {
                text: `品牌净销售额对比 (${chartOptions.days}天)`,
                left: 'center'
            },
            tooltip: {
                trigger: 'item',
                formatter: function(params) {
                    const totalValue = seriesData.reduce((sum, item) => sum + item.value, 0);
                    const percent = totalValue > 0 ? ((params.value / totalValue) * 100).toFixed(1) : 0;
                    return `${params.name}<br/>${formatCurrency(params.value)} (${percent}%)`;
                }
            },
            legend: {
                type: 'scroll',
                orient: 'vertical',
                right: 10,
                top: 'center',
                data: seriesData.map(item => item.name)
            },
            series: [
                {
                    name: '品牌销售额',
                    type: 'pie',
                    radius: '55%',
                    center: ['40%', '50%'],
                    data: seriesData,
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    label: {
                        formatter: '{b}: {d}%',
                        fontSize: 12
                    }
                }
            ],
            toolbox: {
                feature: {
                    saveAsImage: { title: '保存为图片', name: `品牌销售额对比-${new Date().toLocaleDateString()}` }
                },
                right: 20
            }
        };
        
        // 设置图表配置
        brandComparisonChartInstance.setOption(option);
    }
    
    // 获取店铺对应的品牌
    function getShopBrand(shopName) {
        // 通过API返回的品牌-店铺映射关系获取
        if (currentData.shopBrands && currentData.shopBrands[shopName]) {
            return currentData.shopBrands[shopName];
        }
        
        // 如果API未返回映射关系，尝试从currentData.brandShops中查找
        if (currentData.brandShops) {
            for (const brand in currentData.brandShops) {
                if (currentData.brandShops[brand].includes(shopName)) {
                    return brand;
                }
            }
        }
        
        // 未找到对应品牌
        return null;
    }
    
    // 导出品牌对比数据
    function exportBrandComparisonData() {
        if (!currentData || !currentData.brands) {
            showError('没有品牌数据可导出');
            return;
        }
        
        try {
            // 计算每个品牌的销售额
            const brandSalesData = {};
            
            // 初始化每个品牌的销售额为0
            (currentData.brands || []).forEach(brand => {
                brandSalesData[brand] = 0;
            });
            
            // 统计每个店铺的销售额并按品牌归类
            for (const shop in currentData.salesData) {
                const shopBrand = getShopBrand(shop);
                if (!shopBrand) continue;
                
                const shopSales = (currentData.salesData[shop] || []).reduce((sum, val) => sum + (val || 0), 0);
                
                if (brandSalesData[shopBrand] !== undefined) {
                    brandSalesData[shopBrand] += shopSales;
                } else {
                    brandSalesData[shopBrand] = shopSales;
                }
            }
            
            // 计算总销售额
            const totalSales = Object.values(brandSalesData).reduce((sum, val) => sum + val, 0);
            
            // 转换为CSV内容
            let csvContent = "data:text/csv;charset=utf-8,\uFEFF";
            csvContent += "品牌,净销售额 (元),占比 (%)\n";
            
            // 按销售额排序
            const sortedBrands = Object.entries(brandSalesData)
                .sort((a, b) => b[1] - a[1])
                .map(([brand, sales]) => {
                    const percent = totalSales > 0 ? (sales / totalSales * 100).toFixed(2) : 0;
                    return { brand, sales, percent };
                });
            
            // 添加每个品牌的数据
            sortedBrands.forEach(item => {
                csvContent += `"${item.brand}",${item.sales},${item.percent}\n`;
            });
            
            // 添加总计行
            csvContent += `"总计",${totalSales},100\n`;
            
            // 触发下载
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `品牌销售额对比-${chartOptions.days}天-${new Date().toLocaleDateString()}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } catch (error) {
            console.error('导出品牌对比数据失败:', error);
            showError('导出品牌对比数据失败');
        }
    }

    // 渲染店铺矩形树图
    function renderShopTreemapChart() {
        if (!shopTreemapChartInstance || !currentData || !currentData.shops) {
            console.warn("店铺矩形树图实例或数据未准备好");
            return;
        }
        
        // 清除之前的图表
        shopTreemapChartInstance.clear();
        
        // 计算所有店铺的总销售额
        const shopSalesData = {};
        
        // 统计每个店铺在选择时间段内的总销售额
        for (const shop of currentData.shops) {
            // 计算该店铺在当前时间段内的总销售额
            let shopSales = 0;
            
            if (currentData.salesData[shop]) {
                shopSales = currentData.salesData[shop].reduce((sum, val) => sum + (val || 0), 0);
            }
            
            // 只保留有销售额的店铺
            if (shopSales > 0) {
                shopSalesData[shop] = shopSales;
            }
        }
        
        // 按销售额排序所有店铺
        const sortedShops = Object.entries(shopSalesData)
            .sort((a, b) => b[1] - a[1]) // 从高到低排序
            .map(([shop, sales]) => ({
                name: shop,
                value: sales,
                // 获取店铺对应的品牌，用于分组
                brand: getShopBrand(shop) || '未分类',
            }));
            
        // 如果没有有效数据，显示提示信息
        if (sortedShops.length === 0) {
            showError("当前没有店铺销售额数据可显示", shopTreemapChartInstance);
            return;
        }
        
        // 计算总销售额，用于显示百分比
        const totalSales = sortedShops.reduce((sum, item) => sum + item.value, 0);
        
        // 获取所有品牌列表并分配颜色
        const uniqueBrands = [...new Set(sortedShops.map(shop => shop.brand))];
        const brandColorMap = {};
        uniqueBrands.forEach((brand, index) => {
            brandColorMap[brand] = colorPalette[index % colorPalette.length];
        });
        
        // 根据品牌分组数据
        const brandGroups = [];
        uniqueBrands.forEach(brand => {
            const brandShops = sortedShops.filter(shop => shop.brand === brand);
            const brandTotal = brandShops.reduce((sum, shop) => sum + shop.value, 0);
            
            // 只添加有销售额的品牌
            if (brandTotal > 0) {
                brandGroups.push({
                    name: brand,
                    value: brandTotal,
                    children: brandShops.map(shop => ({
                        name: shop.name,
                        value: shop.value,
                        itemStyle: {
                            color: brandColorMap[brand]
                        }
                    })),
                    itemStyle: {
                        color: brandColorMap[brand]
                    }
                });
            }
        });
        
        // 按品牌总销售额排序
        brandGroups.sort((a, b) => b.value - a.value);
        
        // 创建矩形树图配置
        const option = {
            title: {
                text: `店铺净销售额占比 (${chartOptions.days}天)`,
                subtext: `总计: ${formatCurrency(totalSales)}`,
                left: 'center'
            },
            tooltip: {
                formatter: function(params) {
                    // 根据不同层级显示不同信息
                    if (params.treePathInfo.length > 1) {
                        // 店铺层级
                        const shopPercent = ((params.value / totalSales) * 100).toFixed(2);
                        return `${params.name}<br/>净销售额: ${formatCurrency(params.value)}<br/>占比: ${shopPercent}%<br/>品牌: ${params.treePathInfo[0].name}`;
                    } else {
                        // 品牌层级
                        const brandPercent = ((params.value / totalSales) * 100).toFixed(2);
                        return `品牌: ${params.name}<br/>净销售额: ${formatCurrency(params.value)}<br/>占比: ${brandPercent}%`;
                    }
                }
            },
            series: [{
                name: '店铺销售额',
                type: 'treemap',
                data: brandGroups,
                width: '95%',
                height: '90%',
                roam: false, // 禁用拖拽和缩放
                top: '10%',
                label: {
                    show: true,
                    formatter: function(params) {
                        // 如果是品牌层级
                        if (params.treePathInfo.length === 1) {
                            const percent = ((params.value / totalSales) * 100).toFixed(1);
                            return `{b|${params.name}}\n{c|${percent}%}`;
                        }
                        
                        // 如果是店铺层级
                        const percent = ((params.value / totalSales) * 100).toFixed(1);
                        if (percent < 1) {
                            return params.name; // 占比太小时只显示名称
                        } else {
                            return `{a|${params.name}}\n{c|${percent}%}`;
                        }
                    },
                    rich: {
                        a: {
                            fontSize: 12,
                            lineHeight: 16,
                            color: '#fff'
                        },
                        b: {
                            fontSize: 14,
                            fontWeight: 'bold',
                            lineHeight: 20,
                            color: '#fff'
                        },
                        c: {
                            fontSize: 12,
                            lineHeight: 16,
                            color: '#fff'
                        }
                    }
                },
                levels: [
                    { // 顶层配置
                        itemStyle: {
                            borderColor: '#fff',
                            borderWidth: 1,
                            gapWidth: 2
                        }
                    },
                    { // 店铺层配置
                        colorSaturation: [0.35, 0.7],
                        itemStyle: {
                            borderColorSaturation: 0.6,
                            gapWidth: 1,
                            borderWidth: 1
                        }
                    }
                ],
                breadcrumb: {
                    show: false // 隐藏面包屑导航
                }
            }],
            toolbox: {
                feature: {
                    saveAsImage: { title: '保存为图片', name: `店铺销售占比-${new Date().toLocaleDateString()}` }
                },
                right: 20
            }
        };
        
        // 设置图表配置
        shopTreemapChartInstance.setOption(option);
    }
    
    // 导出店铺矩形树图数据
    function exportShopTreemapData() {
        if (!currentData || !currentData.shops) {
            showError('没有店铺数据可导出');
            return;
        }
        
        try {
            // 计算所有店铺的总销售额
            const shopSalesData = {};
            
            // 统计每个店铺在选择时间段内的总销售额
            for (const shop of currentData.shops) {
                let shopSales = 0;
                
                if (currentData.salesData[shop]) {
                    shopSales = currentData.salesData[shop].reduce((sum, val) => sum + (val || 0), 0);
                }
                
                // 只保留有销售额的店铺
                if (shopSales > 0) {
                    shopSalesData[shop] = shopSales;
                }
            }
            
            // 计算总销售额
            const totalSales = Object.values(shopSalesData).reduce((sum, val) => sum + val, 0);
            
            // 转换为CSV内容
            let csvContent = "data:text/csv;charset=utf-8,\uFEFF";
            csvContent += "店铺名称,品牌,净销售额 (元),占比 (%)\n";
            
            // 按销售额排序
            const sortedShops = Object.entries(shopSalesData)
                .sort((a, b) => b[1] - a[1])
                .map(([shop, sales]) => {
                    const percent = totalSales > 0 ? (sales / totalSales * 100).toFixed(2) : 0;
                    const brand = getShopBrand(shop) || '未分类';
                    return { shop, brand, sales, percent };
                });
            
            // 添加每个店铺的数据
            sortedShops.forEach(item => {
                csvContent += `"${item.shop}","${item.brand}",${item.sales},${item.percent}\n`;
            });
            
            // 添加总计行
            csvContent += `"总计","",${totalSales},100\n`;
            
            // 触发下载
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `店铺净销售额占比-${chartOptions.days}天-${new Date().toLocaleDateString()}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } catch (error) {
            console.error('导出店铺占比数据失败:', error);
            showError('导出店铺占比数据失败');
        }
    }

    // 将主函数暴露到全局
    window.generateSalesStats = generateSalesStats;
}
