import sqlite3
import pandas as pd
from datetime import datetime, timedelta

def query_summary_data(shop_name=None, date_range=None, db_path=r'C:\Users\<USER>\Desktop\网站项目\static\db\promotion_data.db'):
    """查询汇总数据"""
    conn = sqlite3.connect(db_path)
    
    query = "SELECT * FROM summary_data"
    params = []
    
    conditions = []
    if shop_name:
        conditions.append("shop_name = ?")
        params.append(shop_name)
    
    if date_range:
        if len(date_range) == 2:
            conditions.append("fetch_date BETWEEN ? AND ?")
            params.extend(date_range)
    
    if conditions:
        query += " WHERE " + " AND ".join(conditions)
    
    query += " ORDER BY fetch_time DESC"
    
    df = pd.read_sql_query(query, conn, params=params)
    conn.close()
    
    return df

def query_ad_detail_data(shop_name=None, ad_id=None, date_range=None, db_path=r'C:\Users\<USER>\Desktop\网站项目\static\db\promotion_data.db'):
    """查询详细广告数据"""
    conn = sqlite3.connect(db_path)
    
    query = "SELECT * FROM ad_detail_data"
    params = []
    
    conditions = []
    if shop_name:
        conditions.append("shop_name = ?")
        params.append(shop_name)
    
    if ad_id:
        conditions.append("ad_id = ?")
        params.append(str(ad_id))
    
    if date_range:
        if len(date_range) == 2:
            conditions.append("fetch_date BETWEEN ? AND ?")
            params.extend(date_range)
    
    if conditions:
        query += " WHERE " + " AND ".join(conditions)
    
    query += " ORDER BY fetch_time DESC"
    
    df = pd.read_sql_query(query, conn, params=params)
    conn.close()
    
    return df

def get_shop_list(db_path=r'C:\Users\<USER>\Desktop\网站项目\static\db\promotion_data.db'):
    """获取数据库中所有店铺列表"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute("SELECT DISTINCT shop_name FROM summary_data ORDER BY shop_name")
    shops = [row[0] for row in cursor.fetchall()]
    
    conn.close()
    return shops

def get_date_range(shop_name=None, db_path=r'C:\Users\<USER>\Desktop\网站项目\static\db\promotion_data.db'):
    """获取数据日期范围"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    query = "SELECT MIN(fetch_date), MAX(fetch_date) FROM summary_data"
    params = []
    
    if shop_name:
        query += " WHERE shop_name = ?"
        params.append(shop_name)
    
    cursor.execute(query, params)
    result = cursor.fetchone()
    
    conn.close()
    return result

def get_top_performing_ads(shop_name=None, metric='gmv', limit=10, db_path=r'C:\Users\<USER>\Desktop\网站项目\static\db\promotion_data.db'):
    """获取表现最好的广告"""
    conn = sqlite3.connect(db_path)
    
    # 确保metric是有效的列名
    valid_metrics = ['gmv', 'net_gmv', 'spend_amount', 'order_num', 'net_order_num', 'click_num', 'impression_num']
    if metric not in valid_metrics:
        metric = 'gmv'
    
    query = f"""
        SELECT ad_name, goods_name, ad_status, {metric}, order_roi, net_order_roi, fetch_date
        FROM ad_detail_data
    """
    params = []
    
    if shop_name:
        query += " WHERE shop_name = ?"
        params.append(shop_name)
    
    query += f" ORDER BY {metric} DESC LIMIT ?"
    params.append(limit)
    
    df = pd.read_sql_query(query, conn, params=params)
    conn.close()
    
    return df

def get_shop_performance_trend(shop_name, days=30, db_path=r'C:\Users\<USER>\Desktop\网站项目\static\db\promotion_data.db'):
    """获取店铺表现趋势"""
    conn = sqlite3.connect(db_path)
    
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
    
    query = """
        SELECT fetch_date, spend_amount, gmv, net_gmv, order_num, net_order_num, 
               order_roi, net_order_roi, total_ad_num
        FROM summary_data
        WHERE shop_name = ? AND fetch_date BETWEEN ? AND ?
        ORDER BY fetch_date
    """
    
    df = pd.read_sql_query(query, conn, params=[shop_name, start_date, end_date])
    conn.close()
    
    return df

def export_to_excel(shop_name=None, date_range=None, output_file=None, db_path=r'C:\Users\<USER>\Desktop\网站项目\static\db\promotion_data.db'):
    """导出数据到Excel文件"""
    if not output_file:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        shop_suffix = f"_{shop_name}" if shop_name else ""
        output_file = f"promotion_data{shop_suffix}_{timestamp}.xlsx"
    
    # 获取汇总数据和详细数据
    summary_df = query_summary_data(shop_name, date_range, db_path)
    detail_df = query_ad_detail_data(shop_name, None, date_range, db_path)
    
    # 写入Excel文件
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        summary_df.to_excel(writer, sheet_name='汇总数据', index=False)
        detail_df.to_excel(writer, sheet_name='详细数据', index=False)
        
        if shop_name:
            # 添加趋势数据
            trend_df = get_shop_performance_trend(shop_name, db_path=db_path)
            if not trend_df.empty:
                trend_df.to_excel(writer, sheet_name='趋势数据', index=False)
            
            # 添加表现最好的广告
            top_ads_df = get_top_performing_ads(shop_name, db_path=db_path)
            if not top_ads_df.empty:
                top_ads_df.to_excel(writer, sheet_name='热门广告', index=False)
    
    print(f"数据已导出到: {output_file}")
    return output_file

def export_all_shops_to_excel(date_range=None, db_path=r'C:\Users\<USER>\Desktop\网站项目\static\db\promotion_data.db'):
    """导出所有店铺的数据到单个Excel文件"""
    shops = get_shop_list(db_path)
    if not shops:
        print("数据库中没有店铺数据")
        return None
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"all_shops_promotion_data_{timestamp}.xlsx"
    
    print(f"正在导出 {len(shops)} 个店铺的数据...")
    
    # 获取所有数据
    all_summary_df = query_summary_data(date_range=date_range, db_path=db_path)
    all_detail_df = query_ad_detail_data(date_range=date_range, db_path=db_path)
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 导出汇总数据
        all_summary_df.to_excel(writer, sheet_name='所有店铺汇总', index=False)
        all_detail_df.to_excel(writer, sheet_name='所有店铺详细', index=False)
        
        # 为每个店铺创建单独的工作表
        for shop in shops:
            print(f"正在处理店铺: {shop}")
            
            # 店铺汇总数据
            shop_summary = query_summary_data(shop_name=shop, date_range=date_range, db_path=db_path)
            if not shop_summary.empty:
                safe_shop_name = shop.replace('/', '_').replace('\\', '_')[:20]  # 工作表名称限制
                shop_summary.to_excel(writer, sheet_name=f'{safe_shop_name}_汇总', index=False)
            
            # 店铺详细数据
            shop_detail = query_ad_detail_data(shop_name=shop, date_range=date_range, db_path=db_path)
            if not shop_detail.empty:
                safe_shop_name = shop.replace('/', '_').replace('\\', '_')[:20]
                shop_detail.to_excel(writer, sheet_name=f'{safe_shop_name}_详细', index=False)
        
        # 添加店铺对比数据
        if not all_summary_df.empty:
            comparison_df = all_summary_df.groupby('shop_name').agg({
                'total_ad_num': 'last',
                'spend_amount': 'sum',
                'gmv': 'sum',
                'net_gmv': 'sum',
                'order_num': 'sum',
                'net_order_num': 'sum',
                'click_num': 'sum',
                'impression_num': 'sum'
            }).reset_index()
            
            # 计算平均ROI
            comparison_df['avg_order_roi'] = all_summary_df.groupby('shop_name')['order_roi'].apply(
                lambda x: pd.to_numeric(x, errors='coerce').mean()
            ).values
            
            comparison_df.to_excel(writer, sheet_name='店铺对比', index=False)
    
    print(f"所有店铺数据已导出到: {output_file}")
    return output_file

def export_shops_separately(shops_list, date_range=None, db_path=r'C:\Users\<USER>\Desktop\网站项目\static\db\promotion_data.db'):
    """为每个店铺单独导出Excel文件"""
    exported_files = []
    
    for shop in shops_list:
        print(f"正在导出店铺 '{shop}' 的数据...")
        try:
            output_file = export_to_excel(shop_name=shop, date_range=date_range, db_path=db_path)
            exported_files.append(output_file)
        except Exception as e:
            print(f"导出店铺 '{shop}' 失败: {e}")
    
    print(f"\n共导出 {len(exported_files)} 个文件:")
    for file in exported_files:
        print(f"- {file}")
    
    return exported_files

def main():
    """数据库查询和导出工具"""
    print("=== 推广数据库查询工具 ===\n")
    
    # 获取所有店铺
    shops = get_shop_list()
    if not shops:
        print("数据库中没有数据，请先运行 test_single_shop.py 获取数据")
        return
    
    print(f"数据库中的店铺 ({len(shops)} 个):")
    for i, shop in enumerate(shops, 1):
        print(f"{i}. {shop}")
    
    # 获取日期范围
    date_range = get_date_range()
    print(f"\n数据日期范围: {date_range[0]} 到 {date_range[1]}")
    
    # 显示功能菜单
    print("\n=== 功能选择 ===")
    print("1. 查看单个店铺数据分析")
    print("2. 导出单个店铺数据到Excel")
    print("3. 导出所有店铺数据到单个Excel文件")
    print("4. 为每个店铺单独导出Excel文件")
    print("5. 选择多个店铺导出")
    print("6. 查看所有店铺对比数据")
    
    choice = input("\n请选择功能 (1-6): ").strip()
    
    if choice == "1":
        # 查看单个店铺数据分析
        print("\n选择要分析的店铺:")
        for i, shop in enumerate(shops, 1):
            print(f"{i}. {shop}")
        
        try:
            shop_index = int(input("请输入店铺编号: ")) - 1
            if 0 <= shop_index < len(shops):
                shop_name = shops[shop_index]
                
                print(f"\n=== 店铺 '{shop_name}' 数据分析 ===")
                
                # 汇总数据
                summary_df = query_summary_data(shop_name=shop_name)
                if not summary_df.empty:
                    print(f"\n最新汇总数据:")
                    latest = summary_df.iloc[0]
                    print(f"- 总广告数量: {latest['total_ad_num']}")
                    print(f"- 消费金额: {latest['spend_amount']:.2f} 元")
                    print(f"- GMV: {latest['gmv']:.2f} 元")
                    print(f"- 订单数: {latest['order_num']}")
                    print(f"- 订单ROI: {latest['order_roi']}")
                
                # 表现最好的广告
                top_ads = get_top_performing_ads(shop_name=shop_name, limit=5)
                if not top_ads.empty:
                    print(f"\n表现最好的5个广告 (按GMV排序):")
                    print(top_ads[['ad_name', 'gmv', 'order_roi']].to_string(index=False))
                
                # 趋势数据
                trend_df = get_shop_performance_trend(shop_name, days=7)
                if not trend_df.empty:
                    print(f"\n近7天趋势:")
                    print(trend_df[['fetch_date', 'spend_amount', 'gmv', 'order_num']].to_string(index=False))
            else:
                print("无效的店铺编号")
        except ValueError:
            print("请输入有效的数字")
    
    elif choice == "2":
        # 导出单个店铺数据
        print("\n选择要导出的店铺:")
        for i, shop in enumerate(shops, 1):
            print(f"{i}. {shop}")
        
        try:
            shop_index = int(input("请输入店铺编号: ")) - 1
            if 0 <= shop_index < len(shops):
                shop_name = shops[shop_index]
                print(f"\n正在导出店铺 '{shop_name}' 的数据...")
                export_file = export_to_excel(shop_name=shop_name)
            else:
                print("无效的店铺编号")
        except ValueError:
            print("请输入有效的数字")
    
    elif choice == "3":
        # 导出所有店铺数据到单个文件
        print(f"\n正在导出所有 {len(shops)} 个店铺的数据到单个Excel文件...")
        export_file = export_all_shops_to_excel()
    
    elif choice == "4":
        # 为每个店铺单独导出文件
        print(f"\n正在为每个店铺单独导出Excel文件...")
        confirm = input(f"将为 {len(shops)} 个店铺各生成一个Excel文件，确认继续？(y/n): ")
        if confirm.lower() == 'y':
            exported_files = export_shops_separately(shops)
        else:
            print("已取消导出")
    
    elif choice == "5":
        # 选择多个店铺导出
        print("\n选择要导出的店铺（输入编号，用逗号分隔，如: 1,3,5）:")
        for i, shop in enumerate(shops, 1):
            print(f"{i}. {shop}")
        
        try:
            indices_input = input("请输入店铺编号: ")
            indices = [int(x.strip()) - 1 for x in indices_input.split(',')]
            selected_shops = [shops[i] for i in indices if 0 <= i < len(shops)]
            
            if selected_shops:
                print(f"\n选择的店铺: {', '.join(selected_shops)}")
                print("导出选项:")
                print("1. 导出到单个Excel文件")
                print("2. 为每个店铺单独导出文件")
                
                export_choice = input("请选择导出方式 (1/2): ")
                
                if export_choice == "1":
                    # 创建临时数据库查询结果
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    output_file = f"selected_shops_data_{timestamp}.xlsx"
                    
                    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                        for shop in selected_shops:
                            summary_df = query_summary_data(shop_name=shop)
                            detail_df = query_ad_detail_data(shop_name=shop)
                            
                            if not summary_df.empty:
                                safe_name = shop.replace('/', '_').replace('\\', '_')[:20]
                                summary_df.to_excel(writer, sheet_name=f'{safe_name}_汇总', index=False)
                            
                            if not detail_df.empty:
                                safe_name = shop.replace('/', '_').replace('\\', '_')[:20]
                                detail_df.to_excel(writer, sheet_name=f'{safe_name}_详细', index=False)
                    
                    print(f"选择的店铺数据已导出到: {output_file}")
                
                elif export_choice == "2":
                    exported_files = export_shops_separately(selected_shops)
                else:
                    print("无效选择")
            else:
                print("没有选择有效的店铺")
        except ValueError:
            print("请输入有效的数字格式")
    
    elif choice == "6":
        # 查看所有店铺对比数据
        print("\n=== 所有店铺对比数据 ===")
        
        all_summary = query_summary_data()
        if not all_summary.empty:
            comparison_df = all_summary.groupby('shop_name').agg({
                'total_ad_num': 'last',
                'spend_amount': 'sum',
                'gmv': 'sum',
                'net_gmv': 'sum',
                'order_num': 'sum',
                'net_order_num': 'sum'
            }).reset_index()
            
            # 按GMV排序
            comparison_df = comparison_df.sort_values('gmv', ascending=False)
            
            print("\n店铺表现对比 (按GMV排序):")
            print(comparison_df.to_string(index=False))
            
            # 询问是否导出
            export_choice = input("\n是否导出对比数据到Excel？(y/n): ")
            if export_choice.lower() == 'y':
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_file = f"shops_comparison_{timestamp}.xlsx"
                comparison_df.to_excel(output_file, index=False)
                print(f"对比数据已导出到: {output_file}")
        else:
            print("没有找到汇总数据")
    
    else:
        print("无效的选择")

if __name__ == "__main__":
    main() 