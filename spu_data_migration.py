#!/usr/bin/env python
# -*- coding: utf-8 -*-
import sqlite3
import pandas as pd
import os
import glob
import logging
from datetime import datetime
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_spu_database():
    """创建 SPU 数据库和表结构"""
    db_path = 'static/db/spu_data.db'
    
    # 确保目录存在
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建 SPU 销售数据表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS spu_sales_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            product_code TEXT NOT NULL,
            style_code TEXT NOT NULL,
            national_code TEXT,
            supplier TEXT,
            supplier_style_code TEXT,
            color_spec TEXT,
            product_name TEXT,
            product_short_name TEXT,
            product_category TEXT,
            virtual_category TEXT,
            cost_price REAL,
            brand TEXT,
            other_price1 REAL,
            other_price2 REAL,
            other_price3 REAL,
            other_price4 REAL,
            other_price5 REAL,
            developer TEXT,
            other_attr2 TEXT,
            other_attr3 TEXT,
            other_attr4 TEXT,
            other_attr5 TEXT,
            basic_price REAL,
            market_price REAL,
            sales_quantity REAL,
            actual_quantity REAL,
            actual_amount REAL,
            sales_amount REAL,
            sales_cost REAL,
            actual_cost REAL,
            sales_profit REAL,
            sales_margin_rate TEXT,
            return_quantity REAL,
            actual_return_quantity REAL,
            return_amount REAL,
            actual_return_amount REAL,
            return_cost REAL,
            actual_return_cost REAL,
            return_profit REAL,
            net_sales_quantity REAL,
            net_sales_amount REAL,
            net_sales_cost REAL,
            net_sales_profit REAL,
            basic_amount REAL,
            paid_amount REAL,
            discount_amount REAL,
            shipping_income REAL,
            shipping_expense REAL,
            net_margin_rate TEXT,
            overseas_shipping_expense REAL,
            overseas_total_income REAL,
            overseas_total_expense REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 创建索引以提高查询性能
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_date ON spu_sales_data(date)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_product_code ON spu_sales_data(product_code)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_style_code ON spu_sales_data(style_code)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_date_style ON spu_sales_data(date, style_code)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_date_product ON spu_sales_data(date, product_code)')
    
    # 创建聚合数据表以提高查询效率
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS spu_daily_summary (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            style_code TEXT NOT NULL,
            product_count INTEGER,
            total_sales_quantity REAL,
            total_sales_amount REAL,
            total_sales_cost REAL,
            total_sales_profit REAL,
            avg_margin_rate REAL,
            total_net_sales_quantity REAL,
            total_net_sales_amount REAL,
            total_net_sales_cost REAL,
            total_net_sales_profit REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(date, style_code)
        )
    ''')
    
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_summary_date ON spu_daily_summary(date)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_summary_style ON spu_daily_summary(style_code)')
    
    conn.commit()
    conn.close()
    
    logger.info(f"数据库创建成功: {db_path}")
    return db_path

def clean_data(df):
    """清洗数据"""
    # 转换日期格式
    df['日期'] = pd.to_datetime(df['日期']).dt.strftime('%Y-%m-%d')
    
    # 清洗数值字段
    numeric_columns = [
        '成本价', '其它价格1', '其它价格2', '其它价格3', '其它价格4', '其它价格5',
        '基本售价', '市场吊牌价', '销售数量', '实发数量', '实发金额', '销售金额',
        '销售成本', '实发成本', '销售毛利', '退货数量', '实退数量', '退货金额',
        '实退金额', '退货成本', '实退成本', '退货毛利', '净销量', '净销售额',
        '净销售成本', '净销售毛利', '基本金额', '已付金额', '优惠金额', '运费收入',
        '运费支出', '境外运费支出', '境外收入总计', '境外支出总计'
    ]
    
    for col in numeric_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
    
    # 清洗文本字段
    text_columns = [
        '商品编码', '款式编码', '国标码', '供应商', '供应商款号', '颜色规格',
        '商品名称', '商品简称', '产品分类', '虚拟分类', '品牌', '开发员',
        '其它属性2', '其它属性3', '其它属性4', '其它属性5', '销售毛利率', '净毛利率'
    ]
    
    for col in text_columns:
        if col in df.columns:
            df[col] = df[col].astype(str).replace('nan', '').replace('NaN', '')
    
    return df

def import_excel_to_db(file_path, db_path):
    """将单个 Excel 文件导入数据库"""
    try:
        logger.info(f"正在处理文件: {file_path}")
        
        # 读取 Excel 文件
        df = pd.read_excel(file_path)
        
        # 清洗数据
        df = clean_data(df)
        
        # 字段映射
        column_mapping = {
            '日期': 'date',
            '商品编码': 'product_code',
            '款式编码': 'style_code',
            '国标码': 'national_code',
            '供应商': 'supplier',
            '供应商款号': 'supplier_style_code',
            '颜色规格': 'color_spec',
            '商品名称': 'product_name',
            '商品简称': 'product_short_name',
            '产品分类': 'product_category',
            '虚拟分类': 'virtual_category',
            '成本价': 'cost_price',
            '品牌': 'brand',
            '其它价格1': 'other_price1',
            '其它价格2': 'other_price2',
            '其它价格3': 'other_price3',
            '其它价格4': 'other_price4',
            '其它价格5': 'other_price5',
            '开发员': 'developer',
            '其它属性2': 'other_attr2',
            '其它属性3': 'other_attr3',
            '其它属性4': 'other_attr4',
            '其它属性5': 'other_attr5',
            '基本售价': 'basic_price',
            '市场吊牌价': 'market_price',
            '销售数量': 'sales_quantity',
            '实发数量': 'actual_quantity',
            '实发金额': 'actual_amount',
            '销售金额': 'sales_amount',
            '销售成本': 'sales_cost',
            '实发成本': 'actual_cost',
            '销售毛利': 'sales_profit',
            '销售毛利率': 'sales_margin_rate',
            '退货数量': 'return_quantity',
            '实退数量': 'actual_return_quantity',
            '退货金额': 'return_amount',
            '实退金额': 'actual_return_amount',
            '退货成本': 'return_cost',
            '实退成本': 'actual_return_cost',
            '退货毛利': 'return_profit',
            '净销量': 'net_sales_quantity',
            '净销售额': 'net_sales_amount',
            '净销售成本': 'net_sales_cost',
            '净销售毛利': 'net_sales_profit',
            '基本金额': 'basic_amount',
            '已付金额': 'paid_amount',
            '优惠金额': 'discount_amount',
            '运费收入': 'shipping_income',
            '运费支出': 'shipping_expense',
            '净毛利率': 'net_margin_rate',
            '境外运费支出': 'overseas_shipping_expense',
            '境外收入总计': 'overseas_total_income',
            '境外支出总计': 'overseas_total_expense'
        }
        
        # 重命名列
        df = df.rename(columns=column_mapping)
        
        # 只保留数据库中存在的列
        db_columns = [
            'date', 'product_code', 'style_code', 'national_code', 'supplier',
            'supplier_style_code', 'color_spec', 'product_name', 'product_short_name',
            'product_category', 'virtual_category', 'cost_price', 'brand',
            'other_price1', 'other_price2', 'other_price3', 'other_price4', 'other_price5',
            'developer', 'other_attr2', 'other_attr3', 'other_attr4', 'other_attr5',
            'basic_price', 'market_price', 'sales_quantity', 'actual_quantity',
            'actual_amount', 'sales_amount', 'sales_cost', 'actual_cost',
            'sales_profit', 'sales_margin_rate', 'return_quantity', 'actual_return_quantity',
            'return_amount', 'actual_return_amount', 'return_cost', 'actual_return_cost',
            'return_profit', 'net_sales_quantity', 'net_sales_amount', 'net_sales_cost',
            'net_sales_profit', 'basic_amount', 'paid_amount', 'discount_amount',
            'shipping_income', 'shipping_expense', 'net_margin_rate',
            'overseas_shipping_expense', 'overseas_total_income', 'overseas_total_expense'
        ]
        
        # 过滤列
        available_columns = [col for col in db_columns if col in df.columns]
        df = df[available_columns]
        
        logger.info(f"文件 {file_path} 包含 {len(df)} 行数据")
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 构建 INSERT 语句
        columns = df.columns.tolist()
        placeholders = ','.join(['?' for _ in columns])
        insert_sql = f"INSERT INTO spu_sales_data ({','.join(columns)}) VALUES ({placeholders})"
        
        # 批量插入数据，每次最多500行
        batch_size = 500
        total_rows = len(df)
        
        for i in range(0, total_rows, batch_size):
            batch = df.iloc[i:i+batch_size]
            data = batch.values.tolist()
            
            try:
                cursor.executemany(insert_sql, data)
                conn.commit()
                logger.info(f"已处理 {min(i+batch_size, total_rows)}/{total_rows} 行")
            except Exception as e:
                logger.error(f"插入数据时出错: {str(e)}")
                conn.rollback()
                break
        
        conn.close()
        
        logger.info(f"文件 {file_path} 导入成功")
        return True
        
    except Exception as e:
        logger.error(f"导入文件 {file_path} 时发生错误: {str(e)}")
        return False

def create_summary_data(db_path):
    """创建汇总数据"""
    logger.info("开始创建汇总数据...")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 清空汇总表
    cursor.execute('DELETE FROM spu_daily_summary')
    
    # 创建汇总数据
    cursor.execute('''
        INSERT OR REPLACE INTO spu_daily_summary (
            date, style_code, product_count,
            total_sales_quantity, total_sales_amount, total_sales_cost, total_sales_profit,
            avg_margin_rate,
            total_net_sales_quantity, total_net_sales_amount, 
            total_net_sales_cost, total_net_sales_profit
        )
        SELECT 
            date,
            style_code,
            COUNT(DISTINCT product_code) as product_count,
            SUM(sales_quantity) as total_sales_quantity,
            SUM(sales_amount) as total_sales_amount,
            SUM(sales_cost) as total_sales_cost,
            SUM(sales_profit) as total_sales_profit,
            AVG(CASE WHEN sales_margin_rate LIKE '%' THEN 
                CAST(REPLACE(sales_margin_rate, '%', '') AS REAL) 
                ELSE 0 END) as avg_margin_rate,
            SUM(net_sales_quantity) as total_net_sales_quantity,
            SUM(net_sales_amount) as total_net_sales_amount,
            SUM(net_sales_cost) as total_net_sales_cost,
            SUM(net_sales_profit) as total_net_sales_profit
        FROM spu_sales_data 
        GROUP BY date, style_code
    ''')
    
    conn.commit()
    conn.close()
    
    logger.info("汇总数据创建完成")

def migrate_all_data():
    """迁移所有数据"""
    logger.info("开始 SPU 数据迁移...")
    
    # 创建数据库
    db_path = create_spu_database()
    
    # 获取所有 Excel 文件
    excel_dir = 'static/spu_day_date'
    excel_files = glob.glob(os.path.join(excel_dir, '*.xlsx'))
    
    # 过滤掉临时文件
    excel_files = [f for f in excel_files if not os.path.basename(f).startswith('~$')]
    
    logger.info(f"找到 {len(excel_files)} 个 Excel 文件")
    
    # 删除现有数据
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    cursor.execute('DELETE FROM spu_sales_data')
    cursor.execute('DELETE FROM spu_daily_summary')
    conn.commit()
    conn.close()
    
    # 导入每个文件
    success_count = 0
    for file_path in excel_files:
        if import_excel_to_db(file_path, db_path):
            success_count += 1
    
    logger.info(f"成功导入 {success_count}/{len(excel_files)} 个文件")
    
    # 创建汇总数据
    create_summary_data(db_path)
    
    # 显示统计信息
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute('SELECT COUNT(*) FROM spu_sales_data')
    total_records = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(DISTINCT style_code) FROM spu_sales_data')
    unique_styles = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(DISTINCT product_code) FROM spu_sales_data')
    unique_products = cursor.fetchone()[0]
    
    cursor.execute('SELECT MIN(date), MAX(date) FROM spu_sales_data')
    date_range = cursor.fetchone()
    
    conn.close()
    
    logger.info("=" * 50)
    logger.info("迁移完成统计:")
    logger.info(f"总记录数: {total_records:,}")
    logger.info(f"款式编码数量: {unique_styles:,}")
    logger.info(f"商品编码数量: {unique_products:,}")
    logger.info(f"日期范围: {date_range[0]} 到 {date_range[1]}")
    logger.info("=" * 50)

if __name__ == '__main__':
    migrate_all_data() 