import os
import csv
import json
import pandas as pd
from datetime import datetime

def read_csv_file(file_path, encoding='gbk'):
    """读取CSV文件，处理中文编码
    
    Args:
        file_path: CSV文件路径
        encoding: 文件编码，默认为GBK
        
    Returns:
        list: 读取的数据列表
    """
    data = []
    encodings = ['gbk', 'utf-8', 'gb18030', 'gb2312', 'cp936']
    
    for enc in encodings:
        try:
            with open(file_path, 'r', encoding=enc) as f:
                reader = csv.reader(f)
                headers = next(reader)  # 跳过表头
                for row in reader:
                    if row:  # 确保行不为空
                        data.append(row)
            return data
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"使用 {enc} 编码读取CSV文件时发生错误: {str(e)}")
            continue
            
    print(f"读取CSV文件失败: {file_path}")
    return []

def read_json_file(file_path):
    """读取JSON文件
    
    Args:
        file_path: JSON文件路径
        
    Returns:
        dict: 读取的JSON数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"读取JSON文件失败: {e}")
        return None

def write_json_file(file_path, data):
    """写入JSON文件
    
    Args:
        file_path: JSON文件路径
        data: 要写入的数据
        
    Returns:
        bool: 是否写入成功
    """
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"写入JSON文件失败: {e}")
        return False

def read_excel_file(file_path):
    """读取Excel文件
    
    Args:
        file_path: Excel文件路径
        
    Returns:
        DataFrame: 读取的Excel数据
    """
    try:
        return pd.read_excel(file_path)
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return None

def ensure_directory(directory):
    """确保目录存在，如果不存在则创建
    
    Args:
        directory: 目录路径
    """
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"创建目录: {directory}")

def get_latest_file(directory, pattern):
    """获取目录下最新的文件
    
    Args:
        directory: 目录路径
        pattern: 文件名模式（例如：'*.csv'）
        
    Returns:
        str: 最新文件的路径，如果没有找到则返回None
    """
    try:
        files = [f for f in os.listdir(directory) if f.endswith(pattern)]
        if not files:
            return None
            
        files.sort(reverse=True)
        return os.path.join(directory, files[0])
    except Exception as e:
        print(f"获取最新文件失败: {e}")
        return None 