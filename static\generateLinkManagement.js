function generateLinkManagement() {
    const container = document.querySelector('.container');

    // Create header section with enhanced styling
    const header = document.createElement('div');
    header.className = 'header';
    header.innerHTML = `
        <h1>链接管理</h1>
        <p class="header-subtitle">管理和优化您的所有推广链接</p>
    `;
    container.appendChild(header);

    // Add custom styles for the link management page
    addCustomStyles();

    // Add summary cards section
    const summarySection = document.createElement('div');
    summarySection.className = 'summary-section';

    // 初始化摘要卡片，数据稍后从API获取
    summarySection.innerHTML = `
        <div class="summary-card hot-selling">
            <div class="summary-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 2v8"></path>
                    <path d="m4.93 10.93 1.41 1.41"></path>
                    <path d="M2 18h2"></path>
                    <path d="M20 18h2"></path>
                    <path d="m19.07 10.93-1.41 1.41"></path>
                    <path d="M22 22H2"></path>
                    <path d="m16 6-4 4-4-4"></path>
                    <path d="M16 18a4 4 0 0 0 0-8H8a4 4 0 0 0 0 8"></path>
                </svg>
            </div>
            <div class="summary-content">
                <div class="summary-title">爆款热销</div>
                <div class="summary-value"><div class="shimmer-loading"></div></div>
                <div class="summary-change up">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="18 15 12 9 6 15"></polyline>
                    </svg>
                    <span>--.--%</span>
                </div>
            </div>
            <div class="summary-meta">
                <div class="summary-highlight">近3天销量增长</div>
                <div class="top-item">
                    <span class="item-label">TOP1:</span>
                    <span class="item-value">加载中...</span>
                </div>
            </div>
        </div>

        <div class="summary-card fast-growing">
            <div class="summary-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                    <polyline points="17 6 23 6 23 12"></polyline>
                </svg>
            </div>
            <div class="summary-content">
                <div class="summary-title">快速增长</div>
                <div class="summary-value"><div class="shimmer-loading"></div></div>
                <div class="summary-change up">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="18 15 12 9 6 15"></polyline>
                    </svg>
                    <span>--.--%</span>
                </div>
            </div>
            <div class="summary-meta">
                <div class="summary-highlight">销量环比增长</div>
                <div class="top-item">
                    <span class="item-label">TOP1:</span>
                    <span class="item-value">加载中...</span>
                </div>
            </div>
        </div>

        <div class="summary-card profit-king">
            <div class="summary-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="8" r="7"></circle>
                    <polyline points="8.21 13.89 7 23 12 20 17 23 15.79 13.88"></polyline>
                </svg>
            </div>
            <div class="summary-content">
                <div class="summary-title">利润王牌</div>
                <div class="summary-value"><div class="shimmer-loading"></div></div>
                <div class="summary-change up">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="18 15 12 9 6 15"></polyline>
                    </svg>
                    <span>--.--%</span>
                </div>
            </div>
            <div class="summary-meta">
                <div class="summary-highlight">最高利润率产品</div>
                <div class="top-item">
                    <span class="item-label">TOP1:</span>
                    <span class="item-value">加载中...</span>
                </div>
            </div>
        </div>

        <div class="summary-card loss-black-hole">
            <div class="summary-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                    <line x1="12" y1="9" x2="12" y2="13"></line>
                    <line x1="12" y1="17" x2="12.01" y2="17"></line>
                </svg>
            </div>
            <div class="summary-content">
                <div class="summary-title">亏损黑洞</div>
                <div class="summary-value"><div class="shimmer-loading"></div></div>
                <div class="summary-change down">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                    <span>--.--%</span>
                </div>
            </div>
            <div class="summary-meta">
                <div class="summary-highlight">亏损需关注</div>
                <div class="top-item">
                    <span class="item-label">TOP1:</span>
                    <span class="item-value">加载中...</span>
                </div>
            </div>
        </div>
    `;

    container.appendChild(summarySection);

    // Create enhanced control panel with date range picker - MODIFIED
    const controlPanel = document.createElement('div');
    controlPanel.className = 'control-panel';
    controlPanel.innerHTML = `
        <div class="control-panel-left">
            <div class="search-box">
                <input type="text" id="linkSearchInput" placeholder="搜索链接名称或URL...">
                <button id="searchButton">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="11" cy="11" r="8"></circle>
                        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                    </svg>
                </button>
            </div>
            <div class="date-range-picker">
                <input type="date" id="startDate" class="date-input">
                <span class="date-separator">至</span>
                <input type="date" id="endDate" class="date-input">
                <button id="applyDateFilter" class="date-filter-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="22 7 12 17 7 12"></polyline>
                        <path d="M22 12v5a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                    </svg>
                    应用
                </button>
            </div>
        </div>
        <div class="control-panel-right">
            <button id="exportBtn" class="secondary-btn">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="7 10 12 15 17 10"></polyline>
                    <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
                导出数据
            </button>
        </div>
    `;
    container.appendChild(controlPanel);

    // Create link count display with improved styling and add date range indication
    const linkCount = document.createElement('div');
    linkCount.className = 'link-count';
    linkCount.innerHTML = `
        <span class="count-number">--</span> 个链接
        <span class="date-range-display" id="dateRangeDisplay">时间范围: 加载中...</span>
        <span class="last-updated">最后更新: ${new Date().toLocaleDateString()}</span>
    `;
    container.appendChild(linkCount);

    // Create links container with a wrapping card for better visual hierarchy
    const tableCard = document.createElement('div');
    tableCard.className = 'table-card';
    container.appendChild(tableCard);

    const linksContainer = document.createElement('div');
    linksContainer.id = 'linksContainer';
    linksContainer.className = 'links-container';
    tableCard.appendChild(linksContainer);

    // Generate modern table view
    generateTableView();

    // Add enhanced pagination
    const pagination = document.createElement('div');
    pagination.className = 'pagination';
    pagination.innerHTML = `
        <button class="page-btn" disabled>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
        </button>
        <button class="page-btn active">1</button>
        <button class="page-btn">2</button>
        <button class="page-btn">3</button>
        <button class="page-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
        </button>
    `;
    container.appendChild(pagination);

    // Add event listeners
    addEventListeners();

    // Initialize date pickers with default values
    initializeDatePickers();

    // Load required libraries for charts
    loadChartLibrary();
    
    // 初始加载摘要数据
    loadSummaryData();
}

function addCustomStyles() {
    // Add new styles for modern UI if not already present
    if (!document.getElementById('link-management-styles')) {
        const style = document.createElement('style');
        style.id = 'link-management-styles';
        style.textContent = `
            .header {
                margin-bottom: 30px;
                border-bottom: 1px solid #eee;
                padding-bottom: 20px;
            }

            .header h1 {
                font-size: 30px;
                font-weight: 700;
                margin-bottom: 8px;
                background: linear-gradient(90deg, #4b6cb7, #182848);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                display: inline-block;
            }

            .header-subtitle {
                color: #666;
                font-size: 16px;
                margin-top: 0;
            }

            .control-panel {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 15px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.05);
                margin-bottom: 15px;
                flex-wrap: wrap;
                gap: 12px;
            }

            .control-panel-left, .control-panel-right {
                display: flex;
                align-items: center;
                gap: 12px;
                flex-wrap: wrap;
            }

            .search-box {
                display: flex;
                border: 1px solid #ddd;
                border-radius: 6px;
                overflow: hidden;
                transition: all 0.3s ease;
                width: 250px;
            }

            .search-box:focus-within {
                border-color: #4b6cb7;
                box-shadow: 0 0 0 3px rgba(75, 108, 183, 0.1);
            }

            .search-box input {
                border: none;
                padding: 12px 15px;
                font-size: 14px;
                flex: 1;
                outline: none;
            }

            .search-box button {
                background: transparent;
                border: none;
                padding: 0 15px;
                cursor: pointer;
                color: #666;
                transition: color 0.3s ease;
            }

            .search-box button:hover {
                color: #4b6cb7;
            }

            .date-range-picker {
                display: flex;
                align-items: center;
                gap: 8px;
                max-width: 100%;
            }

            .date-input {
                border: 1px solid #ddd;
                border-radius: 6px;
                padding: 8px 10px;
                font-size: 13px;
                width: 130px;
                outline: none;
                transition: border-color 0.3s ease;
            }

            .date-input:focus {
                border-color: #4b6cb7;
                box-shadow: 0 0 0 2px rgba(75, 108, 183, 0.1);
            }

            .date-separator {
                color: #666;
                font-size: 13px;
            }

            .date-filter-btn {
                display: flex;
                align-items: center;
                gap: 5px;
                padding: 8px 12px;
                border: none;
                border-radius: 6px;
                background: #f0f4ff;
                color: #4b6cb7;
                font-size: 13px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .date-filter-btn:hover {
                background: #e1e7ff;
            }

            .date-range-display {
                font-size: 13px;
                color: #666;
                margin-left: auto;
                margin-right: 15px;
            }

            .action-btn, .secondary-btn {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                border: none;
            }

            .action-btn {
                background: linear-gradient(90deg, #4b6cb7, #182848);
                color: white;
            }

            .action-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            }

            .secondary-btn {
                background: #f5f7fa;
                color: #333;
                border: 1px solid #ddd;
            }

            .secondary-btn:hover {
                background: #e9ecef;
            }

            .link-count {
                margin: 15px 0;
                color: #666;
                font-size: 14px;
                display: flex;
                justify-content: space-between;
                padding: 0 5px;
            }

            .count-number {
                font-weight: 600;
                color: #4b6cb7;
            }

            .last-updated {
                font-size: 13px;
                color: #999;
            }

            .table-card {
                background: white;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                overflow: hidden;
                margin-bottom: 20px;
            }

            .links-container {
                overflow-x: auto;
            }

            .shop-table {
                width: 100%;
                border-collapse: separate;
                border-spacing: 0;
            }

            .shop-table th {
                position: sticky;
                top: 0;
                background: #f8f9fa;
                color: #333;
                font-weight: 600;
                padding: 15px;
                text-align: left;
                font-size: 14px;
                border-bottom: 2px solid #eee;
                white-space: nowrap;
            }

            .shop-table td {
                padding: 15px;
                border-bottom: 1px solid #eee;
                vertical-align: middle;
            }

            .shop-table tr:hover {
                background-color: #f8f9fa;
            }

            .shop-table tr:last-child td {
                border-bottom: none;
            }

            .table-image {
                width: 60px;
                height: 60px;
                border-radius: 8px;
                object-fit: cover;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }

            .link-title {
                font-weight: 600;
                margin-bottom: 5px;
                color: #333;
                font-size: 14px;
                max-width: 300px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .link-url {
                font-size: 13px;
                color: #666;
                word-break: break-all;
                max-width: 300px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .status-badge {
                padding: 6px 12px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: 500;
                display: inline-block;
            }

            .status-active {
                background-color: #e1f3e8;
                color: #1c8a44;
            }

            .status-inactive {
                background-color: #f8e6e6;
                color: #d13b3b;
            }

            .trend-chart-container {
                height: 40px;
                width: 120px;
            }

            .data-cell {
                font-weight: 600;
                color: #333;
            }

            .up-trend {
                color: #1c8a44;
            }

            .down-trend {
                color: #d13b3b;
            }

            .table-actions {
                display: flex;
                gap: 8px;
            }

            .table-btn {
                padding: 6px 12px;
                border-radius: 6px;
                font-size: 13px;
                cursor: pointer;
                transition: all 0.3s ease;
                border: none;
                display: flex;
                align-items: center;
                gap: 5px;
            }

            .edit-btn {
                background: #e9ecef;
                color: #333;
            }

            .edit-btn:hover {
                background: #dee2e6;
            }

            .delete-btn {
                background: #fbeaea;
                color: #d13b3b;
            }

            .delete-btn:hover {
                background: #f8d7d7;
            }

            .pagination {
                display: flex;
                justify-content: center;
                margin-top: 30px;
                gap: 5px;
            }

            .page-btn {
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s ease;
                font-weight: 500;
            }

            .page-btn:hover:not([disabled]) {
                border-color: #4b6cb7;
            }

            .page-btn.active {
                background: linear-gradient(90deg, #4b6cb7, #182848);
                color: white;
                border: none;
            }

            .page-btn[disabled] {
                opacity: 0.5;
                cursor: not-allowed;
            }

            @media (max-width: 900px) {
                .control-panel {
                    flex-direction: column;
                    align-items: flex-start;
                }

                .control-panel-left {
                    width: 100%;
                    margin-bottom: 10px;
                }

                .search-box {
                    width: 100%;
                }

                .date-range-picker {
                    width: 100%;
                    flex-wrap: wrap;
                }

                .control-panel-right {
                    width: 100%;
                    justify-content: flex-end;
                }
            }

            @media (max-width: 600px) {
                .date-input {
                    width: calc(50% - 15px);
                }

                .date-range-picker {
                    justify-content: space-between;
                }
            }

            /* Summary Cards Styles */
            .summary-section {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 20px;
                margin-bottom: 30px;
            }

            .summary-card {
                background: white;
                border-radius: 12px;
                padding: 24px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.05);
                position: relative;
                overflow: hidden;
                transition: transform 0.3s ease, box-shadow 0.3s ease;
                cursor: pointer;
                min-height: 180px;
                display: flex;
                flex-direction: column;
            }

            .summary-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            }

            .summary-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 4px;
                height: 100%;
            }

            .summary-card.hot-selling::before {
                background-color: #ff6b6b;
            }

            .summary-card.fast-growing::before {
                background-color: #5cb85c;
            }

            .summary-card.profit-king::before {
                background-color: #ffbb33;
            }

            .summary-card.loss-black-hole::before {
                background-color: #343a40;
            }

            .summary-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 48px;
                height: 48px;
                border-radius: 12px;
                margin-bottom: 16px;
                flex-shrink: 0;
            }

            .hot-selling .summary-icon {
                background-color: rgba(255, 107, 107, 0.1);
                color: #ff6b6b;
            }

            .fast-growing .summary-icon {
                background-color: rgba(92, 184, 92, 0.1);
                color: #5cb85c;
            }

            .profit-king .summary-icon {
                background-color: rgba(255, 187, 51, 0.1);
                color: #ffbb33;
            }

            .loss-black-hole .summary-icon {
                background-color: rgba(52, 58, 64, 0.1);
                color: #343a40;
            }

            .summary-content {
                flex: 1;
                margin-bottom: 24px;
            }

            .summary-title {
                font-size: 16px;
                font-weight: 600;
                color: #495057;
                margin-bottom: 12px;
            }

            .summary-value {
                font-size: 28px;
                font-weight: 700;
                color: #212529;
                margin-bottom: 12px;
                display: flex;
                align-items: baseline;
            }

            .summary-unit {
                font-size: 16px;
                font-weight: 400;
                color: #6c757d;
                margin-left: 5px;
            }

            .summary-change {
                display: flex;
                align-items: center;
                font-size: 14px;
                font-weight: 500;
                gap: 5px;
            }

            .summary-change.up {
                color: #5cb85c;
            }

            .summary-change.down {
                color: #ff6b6b;
            }

            .summary-meta {
                margin-top: auto;
                border-top: 1px solid rgba(0,0,0,0.05);
                padding-top: 12px;
                font-size: 13px;
            }

            .summary-highlight {
                font-weight: 500;
                color: #6c757d;
                margin-bottom: 6px;
            }

            .top-item {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 100%;
            }

            .item-label {
                font-weight: 600;
                color: #343a40;
            }

            .item-value {
                color: #6c757d;
            }

            @media (max-width: 1200px) {
                .summary-section {
                    grid-template-columns: repeat(2, 1fr);
                }
            }

            @media (max-width: 768px) {
                .summary-section {
                    grid-template-columns: 1fr;
                }

                .summary-card {
                    min-height: 150px;
                }
            }

            .loading-indicator {
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 50px;
                font-size: 16px;
                color: #666;
                background: rgba(255, 255, 255, 0.8);
                border-radius: 5px;
                position: relative;
            }

            .loading-indicator:after {
                content: '';
                width: 20px;
                height: 20px;
                margin-left: 15px;
                border: 2px solid #4b6cb7;
                border-top: 2px solid transparent;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .error-message {
                padding: 30px;
                background: #fff8f8;
                border: 1px solid #ffcdd2;
                border-radius: 8px;
                color: #d32f2f;
                font-size: 14px;
                text-align: center;
                margin: 20px 0;
            }
        `;
        document.head.appendChild(style);
    }
}

function loadChartLibrary() {
    // Add Chart.js for line charts
    if (!document.getElementById('chartjs-script')) {
        const script = document.createElement('script');
        script.id = 'chartjs-script';
        script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
        script.onload = initializeCharts;
        document.head.appendChild(script);
    } else {
        initializeCharts();
    }
}

function initializeCharts() {
    const chartCanvases = document.querySelectorAll('.trend-chart');
    chartCanvases.forEach(canvas => {
        const ctx = canvas.getContext('2d');

        // 从canvas数据属性获取数据，或使用默认值
        let data = [0, 0, 0, 0, 0, 0, 0];
        try {
            if (canvas.dataset.values) {
                data = JSON.parse(canvas.dataset.values);
            }
        } catch (error) {
            console.error('解析图表数据出错:', error);
        }

        // 确保数据有7个数据点
        while (data.length < 7) {
            data.push(0);
        }
        
        // 只保留最近的7个数据点
        if (data.length > 7) {
            data = data.slice(data.length - 7);
        }

        // 确定图表颜色基于数据趋势
        let borderColor = '#4b6cb7';
        let backgroundColor = 'rgba(75, 108, 183, 0.1)';
        
        // 计算趋势：如果最后一个数据点高于第一个，则为上升趋势
        if (data[data.length - 1] > data[0]) {
            borderColor = '#5cb85c'; // 上升趋势为绿色
            backgroundColor = 'rgba(92, 184, 92, 0.1)';
        } else if (data[data.length - 1] < data[0]) {
            borderColor = '#d9534f'; // 下降趋势为红色
            backgroundColor = 'rgba(217, 83, 79, 0.1)';
        }

        // 创建图表
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['一', '二', '三', '四', '五', '六', '日'],
                datasets: [{
                    label: canvas.dataset.label || '销售额',
                    data: data,
                    borderColor: borderColor,
                    backgroundColor: backgroundColor,
                    tension: 0.4,
                    fill: true,
                    pointRadius: 2,
                    pointHoverRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: true,
                        callbacks: {
                            label: function(context) {
                                return `￥${context.parsed.y.toLocaleString()}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: false
                    },
                    y: {
                        display: false
                    }
                }
            }
        });
    });
}

function addEventListeners() {
    // Search functionality
    const searchInput = document.getElementById('linkSearchInput');
    const searchButton = document.getElementById('searchButton');

    searchButton.addEventListener('click', function() {
        filterLinks(searchInput.value);
    });

    searchInput.addEventListener('keyup', function(e) {
        if (e.key === 'Enter') {
            filterLinks(searchInput.value);
        }
    });

    // Date range filter functionality
    const startDateInput = document.getElementById('startDate');
    const endDateInput = document.getElementById('endDate');
    const applyDateFilterBtn = document.getElementById('applyDateFilter');

    // Add logic to handle date dependencies
    if (startDateInput && endDateInput) {
        startDateInput.addEventListener('change', function() {
            // Set end date min value to start date
            endDateInput.min = this.value;

            // If end date is before start date, update end date
            if (endDateInput.value && endDateInput.value < this.value) {
                endDateInput.value = this.value;
            }
        });

        endDateInput.addEventListener('change', function() {
            // Set start date max value to end date
            startDateInput.max = this.value;

            // If start date is after end date, update start date
            if (startDateInput.value && startDateInput.value > this.value) {
                startDateInput.value = this.value;
            }
        });
    }

    // Apply date filter
    if (applyDateFilterBtn) {
        applyDateFilterBtn.addEventListener('click', function() {
            const startDate = startDateInput.value;
            const endDate = endDateInput.value;

            if (startDate && endDate) {
                // 更新日期范围显示
                updateDateRangeDisplay(startDate, endDate);

                // 应用日期过滤器
                applyDateRangeFilter(startDate, endDate);
            }
        });
    }

    // Export button
    const exportBtn = document.getElementById('exportBtn');
    exportBtn.addEventListener('click', function() {
        exportLinkData();
    });

    // Add event listeners to the initial pagination
    initPaginationEvents();
}

// Initialize pagination events
function initPaginationEvents() {
    const pageButtons = document.querySelectorAll('.pagination .page-btn:not([disabled])');
    pageButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            pageButtons.forEach(btn => btn.classList.remove('active'));

            // Add active class to the clicked button
            this.classList.add('active');

            // Get the page number from the button (if available)
            const page = this.getAttribute('data-page') || this.textContent;
            if (page && !isNaN(page)) {
                // 获取当前搜索值和日期范围
                const searchValue = document.getElementById('linkSearchInput').value.trim();
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;
                
                // 使用当前页码重新加载表格
                generateTableView(searchValue, startDate, endDate, parseInt(page));
            }
        });
    });
}

function filterLinks(query) {
    // 获取当前日期范围值
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    // 使用搜索参数重新加载表格，重置为第1页
    document.getElementById('linkSearchInput').value = query;
    generateTableView(query, startDate, endDate, 1);
}

function applyDateRangeFilter(startDate, endDate) {
    // 获取当前搜索值
    const searchTerm = document.getElementById('linkSearchInput').value.trim();

    // 更新日期范围显示
    updateDateRangeDisplay(startDate, endDate);

    // 使用API获取指定日期范围的数据
    generateTableView(searchTerm, startDate, endDate, 1);

    // 更新摘要卡片数据
    loadSummaryData(startDate, endDate);
}

function exportLinkData() {
    // 获取当前搜索值和日期范围
    const searchTerm = document.getElementById('linkSearchInput').value.trim();
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    // 构建API URL
    let apiUrl = `/api/links?search=${encodeURIComponent(searchTerm)}`;
    if (startDate && endDate) {
        apiUrl += `&startDate=${startDate}&endDate=${endDate}`;
    }
    apiUrl += '&pageSize=1000'; // 导出时获取大量数据
    
    // 显示导出中的提示
    showToast('正在准备导出数据...');
    
    // 调用API获取数据
    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 将链接数据转换为CSV格式
                const csvContent = convertToCSV(data.links);
                
                // 创建下载链接
                const encodedUri = encodeURI(csvContent);
                const link = document.createElement("a");
                link.setAttribute("href", "data:text/csv;charset=utf-8,\uFEFF" + encodedUri);
                
                // 设置文件名
                const dateRange = startDate && endDate ? `${startDate}_${endDate}` : 'all';
                link.setAttribute("download", `links_export_${dateRange}.csv`);
                
                // 触发下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                // 显示成功提示
                showToast('导出成功！文件已开始下载');
            } else {
                showToast('导出失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('导出数据时出错:', error);
            showToast('导出失败，请稍后重试');
        });
}

// 将数据转换为CSV格式
function convertToCSV(links) {
    // 定义CSV头部
    const headers = [
        '商品ID', '商品名称', '店铺名称', '销售额', '销量', '利润', '利润率', 
        '退款率', '成本', '推广费', '保本ROI', '实际ROI', '运营人员'
    ];
    
    // 创建CSV内容
    let csvContent = headers.join(',') + '\n';
    
    // 添加每行数据
    links.forEach(link => {
        const row = [
            `"${link.id}"`,
            `"${link.productName.replace(/"/g, '""')}"`, // 转义引号
            `"${link.shop_name ? link.shop_name.replace(/"/g, '""') : '未知'}"`,
            link.sales,
            link.quantity,
            link.profit,
            `${link.profitRate}%`,
            `${link.refundRate}%`,
            link.cost,
            link.adCost,
            `${link.breakEvenROI}%`,
            `${link.actualROI}%`,
            `"${link.operator}"`
        ];
        csvContent += row.join(',') + '\n';
    });
    
    return csvContent;
}

function showToast(message) {
    // Create toast notification if not exists
    if (!document.getElementById('toast-container')) {
        const toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.style = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 9999;
        `;
        document.body.appendChild(toastContainer);
    }

    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.innerHTML = `
        <div class="toast-content">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
            <span>${message}</span>
        </div>
    `;
    toast.style = `
        background: white;
        border-radius: 8px;
        padding: 16px;
        margin-top: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideIn 0.3s, fadeOut 0.5s 2.5s forwards;
        max-width: 300px;
    `;

    // Add animation styles if they don't exist
    if (!document.getElementById('toast-animations')) {
        const style = document.createElement('style');
        style.id = 'toast-animations';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }
            .toast-content {
                display: flex;
                align-items: center;
                gap: 10px;
            }
            .toast-content svg {
                color: #1c8a44;
            }
        `;
        document.head.appendChild(style);
    }

    document.getElementById('toast-container').appendChild(toast);

    // Remove the toast after animation completes
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

function generateTableView(searchQuery = '', startDate = '', endDate = '', page = 1) {
    const linksContainer = document.getElementById('linksContainer');
    linksContainer.innerHTML = '';

    // Show loading indicator
    linksContainer.innerHTML = '<div class="loading-indicator">加载数据中...</div>';

    // Get search query
    if (searchQuery === undefined || searchQuery === null) {
        searchQuery = document.getElementById('linkSearchInput').value.trim();
    }

    // 构建API请求URL
    let apiUrl = `/api/links?search=${encodeURIComponent(searchQuery)}&page=${page}`;

    // 如果提供了日期范围，则添加到URL
    if (startDate && endDate) {
        apiUrl += `&startDate=${startDate}&endDate=${endDate}`;
    }

    // Call API to get links data
    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新链接计数显示
                document.querySelector('.link-count').innerHTML = `
                    <span class="count-number">${data.totalCount || 0}</span> 个链接
                    <span class="date-range-display" id="dateRangeDisplay">${getDateRangeText(data.dateRange?.startDate, data.dateRange?.endDate)}</span>
                    <span class="last-updated">最后更新: ${new Date().toLocaleDateString()}</span>
                `;

                // 清除加载指示器
                linksContainer.innerHTML = '';

                // 如果有聚合数据，显示数据总计信息
                if (data.aggregatedData) {
                    showAggregatedDataSummary(data.aggregatedData);
                }

                // 创建表格
                const table = document.createElement('table');
                table.className = 'shop-table';

                // 生成表头
                const thead = document.createElement('thead');
                thead.innerHTML = `
                    <tr>
                        <th width="20">ID</th>
                        <th width="250">链接信息</th>
                        <th width="120">销售趋势</th>
                        <th>利润</th>
                        <th>利润率</th>
                        <th>销售额</th>
                        <th>销量</th>
                        <th>退款率</th>
                        <th>商品成本</th>
                        <th>推广费</th>
                        <th>保本ROI</th>
                        <th>实际ROI</th>
                        <th>所属店铺</th>
                        <th width="100">操作</th>
                    </tr>
                `;
                table.appendChild(thead);

                // 生成表格内容
                const tbody = document.createElement('tbody');

                if (!data.links || data.links.length === 0) {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `<td colspan="14" style="text-align: center; padding: 30px;">没有找到匹配的链接数据</td>`;
                    tbody.appendChild(tr);
                } else {
                    data.links.forEach((link, index) => {
                        const tr = document.createElement('tr');
                        
                        // 截取商品ID，只显示后8位
                        const shortId = link.id.length > 8 ? link.id.slice(-8) : link.id;

                        // 商品图片默认值处理
                        const imageUrl = link.imageUrl || `https://picsum.photos/id/${20 + index}/60/60`;
                        
                        // 利润和利润率显示样式
                        const profitClass = link.profit > 0 ? 'up-trend' : 'down-trend';
                        const profitRateClass = link.profitRate > 0 ? 'up-trend' : 'down-trend';
                        
                        // ROI样式
                        const roiClass = link.actualROI > link.breakEvenROI ? 'up-trend' : 'down-trend';
                        
                        // 退款率样式
                        const refundClass = link.refundRate > 5 ? 'down-trend' : '';

                        tr.innerHTML = `
                            <td>${shortId}</td>
                            <td>
                                <div style="display: flex; align-items: center; gap: 15px;">
                                    <img src="${imageUrl}" class="table-image" onerror="this.src='https://picsum.photos/id/${20 + index}/60/60'">
                                    <div>
                                        <div class="link-title">${link.productName}</div>
                                        <div class="link-url">${link.url || '#'}</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="trend-chart-container">
                                    <canvas class="trend-chart" data-values='${JSON.stringify(link.salesTrend || [0,0,0,0,0,0,0])}' height="40"></canvas>
                                </div>
                            </td>
                            <td class="data-cell ${profitClass}">¥${link.profit.toLocaleString(undefined, {maximumFractionDigits: 2})}</td>
                            <td class="data-cell ${profitRateClass}">${link.profitRate.toFixed(1)}%</td>
                            <td class="data-cell">¥${link.sales.toLocaleString(undefined, {maximumFractionDigits: 2})}</td>
                            <td class="data-cell">${link.quantity}</td>
                            <td class="data-cell ${refundClass}">${link.refundRate.toFixed(1)}%</td>
                            <td class="data-cell">¥${link.cost.toLocaleString(undefined, {maximumFractionDigits: 2})}</td>
                            <td class="data-cell">¥${link.adCost.toLocaleString(undefined, {maximumFractionDigits: 2})}</td>
                            <td class="data-cell">${link.breakEvenROI.toFixed(1)}%</td>
                            <td class="data-cell ${roiClass}">${link.actualROI.toFixed(1)}%</td>
                            <td>${link.shop_name || '未知店铺'}</td>
                            <td>
                                <div class="table-actions">
                                    <button class="table-btn edit-btn" data-id="${link.id}">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                        </svg>
                                    </button>
                                    <button class="table-btn delete-btn" data-id="${link.id}">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polyline points="3 6 5 6 21 6"></polyline>
                                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        `;

                        tbody.appendChild(tr);
                    });
                }

                table.appendChild(tbody);
                linksContainer.appendChild(table);

                // 更新分页
                if (data.pagination) {
                    updatePagination(data.pagination, startDate, endDate);
                }

                // 初始化图表
                loadChartLibrary();
            } else {
                // 显示错误信息
                linksContainer.innerHTML = `<div class="error-message">获取数据失败: ${data.message || '未知错误'}</div>`;
            }
        })
        .catch(error => {
            console.error('Error fetching links:', error);
            linksContainer.innerHTML = `<div class="error-message">获取数据失败: ${error.message || '未知错误'}</div>`;
        });
}

// 更新日期范围显示文本
function getDateRangeText(startDate, endDate) {
    if (startDate && endDate) {
        // 格式化日期显示 (YYYY-MM-DD 转为 YYYY年MM月DD日)
        const formatDateForDisplay = (dateString) => {
            if (!dateString) return '';
            const [year, month, day] = dateString.split('-');
            return `${year}年${month}月${day}日`;
        };

        return `时间范围: ${formatDateForDisplay(startDate)} 至 ${formatDateForDisplay(endDate)}`;
    } else {
        return '时间范围: 最新数据';
    }
}

// 更新分页控件
function updatePagination(pagination, startDate, endDate) {
    const paginationContainer = document.querySelector('.pagination');

    if (paginationContainer) {
        const totalPages = pagination.totalPages || 1;
        const currentPage = pagination.currentPage || 1;

        let paginationHTML = '';

        // 上一页按钮
        paginationHTML += `
            <button class="page-btn ${currentPage === 1 ? 'disabled' : ''}" ${currentPage === 1 ? 'disabled' : ''} data-page="${currentPage - 1}">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="15 18 9 12 15 6"></polyline>
                </svg>
            </button>
        `;

        // 页码按钮
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, startPage + 4);

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button class="page-btn ${i === currentPage ? 'active' : ''}" data-page="${i}">${i}</button>
            `;
        }

        // 下一页按钮
        paginationHTML += `
            <button class="page-btn ${currentPage === totalPages ? 'disabled' : ''}" ${currentPage === totalPages ? 'disabled' : ''} data-page="${currentPage + 1}">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="9 18 15 12 9 6"></polyline>
                </svg>
            </button>
        `;

        paginationContainer.innerHTML = paginationHTML;

        // 为分页按钮添加事件监听器
        const pageButtons = paginationContainer.querySelectorAll('.page-btn:not([disabled])');
        pageButtons.forEach(button => {
            button.addEventListener('click', function() {
                const page = this.getAttribute('data-page');
                if (page) {
                    // 获取当前搜索关键词
                    const searchQuery = document.getElementById('linkSearchInput').value.trim();
                    // 调用API加载选定页码的数据，保持当前日期范围
                    generateTableView(searchQuery, startDate, endDate, parseInt(page));
                }
            });
        });
    }
}

// 初始化日期选择器
function initializeDatePickers() {
    // 获取当前日期
    const today = new Date();
    
    // 设置默认的日期范围 - 最近7天
    const endDate = formatDateForInput(today);
    const startDate = formatDateForInput(new Date(today.setDate(today.getDate() - 7)));
    
    const startDateInput = document.getElementById('startDate');
    const endDateInput = document.getElementById('endDate');

    if (startDateInput && endDateInput) {
        // 设置初始值
        startDateInput.value = startDate;
        endDateInput.value = endDate;

        // 设置最大日期为今天
        const todayStr = formatDateForInput(new Date());
        startDateInput.setAttribute('max', todayStr);
        endDateInput.setAttribute('max', todayStr);
        
        // 初始加载时不调用 updateDateRangeDisplay，
        // 会在 generateTableView 加载完成后自动更新
    }
}

// 更新日期范围显示
function updateDateRangeDisplay(startDate, endDate) {
    const dateRangeDisplay = document.getElementById('dateRangeDisplay');
    if (dateRangeDisplay) {
        dateRangeDisplay.textContent = getDateRangeText(startDate, endDate);
    }
}

// 将日期对象格式化为输入框所需格式 YYYY-MM-DD
function formatDateForInput(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

function showAggregatedDataSummary(aggregatedData) {
    // 创建聚合数据摘要元素
    const aggregatedSummary = document.createElement('div');
    aggregatedSummary.className = 'aggregated-summary';
    aggregatedSummary.innerHTML = `
        <div class="aggregated-title">所选时间范围内数据总计</div>
        <div class="aggregated-grid">
            <div class="aggregated-item">
                <div class="item-label">总销售额</div>
                <div class="item-value">¥${aggregatedData.totalSales.toLocaleString(undefined, {maximumFractionDigits: 2})}</div>
            </div>
            <div class="aggregated-item">
                <div class="item-label">总销量</div>
                <div class="item-value">${aggregatedData.totalQuantity}</div>
            </div>
            <div class="aggregated-item">
                <div class="item-label">总利润</div>
                <div class="item-value">¥${aggregatedData.totalProfit.toLocaleString(undefined, {maximumFractionDigits: 2})}</div>
            </div>
            <div class="aggregated-item">
                <div class="item-label">平均利润率</div>
                <div class="item-value">${aggregatedData.avgProfitRate.toFixed(2)}%</div>
            </div>
            <div class="aggregated-item">
                <div class="item-label">总推广费</div>
                <div class="item-value">¥${aggregatedData.totalAdCost.toLocaleString(undefined, {maximumFractionDigits: 2})}</div>
            </div>
            <div class="aggregated-item">
                <div class="item-label">平均ROI</div>
                <div class="item-value">${aggregatedData.avgROI.toFixed(2)}%</div>
            </div>
        </div>
    `;

    // 在链接容器顶部插入摘要
    const linksContainer = document.getElementById('linksContainer');
    linksContainer.insertBefore(aggregatedSummary, linksContainer.firstChild);

    // 添加聚合摘要样式（如果尚不存在）
    if (!document.getElementById('aggregated-summary-styles')) {
        const style = document.createElement('style');
        style.id = 'aggregated-summary-styles';
        style.textContent = `
            .aggregated-summary {
                background: #f8faff;
                border-radius: 8px;
                padding: 16px;
                margin-bottom: 20px;
                border: 1px solid #e1e7ff;
            }

            .aggregated-title {
                font-size: 15px;
                font-weight: 600;
                color: #333;
                margin-bottom: 12px;
            }

            .aggregated-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 16px;
            }

            .aggregated-item {
                background: white;
                padding: 12px;
                border-radius: 6px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            }

            .item-label {
                font-size: 13px;
                color: #666;
                margin-bottom: 5px;
            }

            .item-value {
                font-size: 18px;
                font-weight: 600;
                color: #333;
            }

            @media (max-width: 768px) {
                .aggregated-grid {
                    grid-template-columns: repeat(2, 1fr);
                }
            }

            @media (max-width: 480px) {
                .aggregated-grid {
                    grid-template-columns: 1fr;
                }
            }

            .shimmer-loading {
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: shimmer 1.5s infinite;
                height: 20px;
                border-radius: 4px;
            }

            @keyframes shimmer {
                0% { background-position: 200% 0; }
                100% { background-position: -200% 0; }
            }
        `;
        document.head.appendChild(style);
    }
}

function loadSummaryData(startDate = '', endDate = '') {
    // 构建API请求URL
    let apiUrl = '/api/links/summary';
    
    if (startDate && endDate) {
        apiUrl += `?startDate=${startDate}&endDate=${endDate}`;
    }
    
    // 发起请求
    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateSummaryCards(data.summary);
            } else {
                console.error('加载摘要数据失败:', data.message);
            }
        })
        .catch(error => {
            console.error('获取摘要数据时出错:', error);
        });
}

function updateSummaryCards(summary) {
    // 更新爆款热销卡片
    const hotSellingCard = document.querySelector('.summary-card.hot-selling');
    if (hotSellingCard) {
        const valueEl = hotSellingCard.querySelector('.summary-value');
        const changeEl = hotSellingCard.querySelector('.summary-change span');
        const topItemEl = hotSellingCard.querySelector('.top-item .item-value');
        
        if (valueEl) valueEl.innerHTML = `${summary.hot_selling.count}<span class="summary-unit">件</span>`;
        if (changeEl) changeEl.textContent = `${summary.hot_selling.growth.toFixed(1)}%`;
        if (topItemEl) topItemEl.textContent = summary.hot_selling.top_item;
        
        // 根据增长率调整上升/下降样式
        const changeContainer = hotSellingCard.querySelector('.summary-change');
        if (changeContainer) {
            if (summary.hot_selling.growth > 0) {
                changeContainer.classList.remove('down');
                changeContainer.classList.add('up');
                changeContainer.querySelector('svg').innerHTML = '<polyline points="18 15 12 9 6 15"></polyline>';
            } else {
                changeContainer.classList.remove('up');
                changeContainer.classList.add('down');
                changeContainer.querySelector('svg').innerHTML = '<polyline points="6 9 12 15 18 9"></polyline>';
            }
        }
    }
    
    // 更新快速增长卡片
    const fastGrowingCard = document.querySelector('.summary-card.fast-growing');
    if (fastGrowingCard) {
        const valueEl = fastGrowingCard.querySelector('.summary-value');
        const changeEl = fastGrowingCard.querySelector('.summary-change span');
        const topItemEl = fastGrowingCard.querySelector('.top-item .item-value');
        
        if (valueEl) valueEl.innerHTML = `${summary.fast_growing.count}<span class="summary-unit">款</span>`;
        if (changeEl) changeEl.textContent = `${summary.fast_growing.growth.toFixed(1)}%`;
        if (topItemEl) topItemEl.textContent = summary.fast_growing.top_item;
        
        // 根据增长率调整上升/下降样式
        const changeContainer = fastGrowingCard.querySelector('.summary-change');
        if (changeContainer) {
            if (summary.fast_growing.growth > 0) {
                changeContainer.classList.remove('down');
                changeContainer.classList.add('up');
                changeContainer.querySelector('svg').innerHTML = '<polyline points="18 15 12 9 6 15"></polyline>';
            } else {
                changeContainer.classList.remove('up');
                changeContainer.classList.add('down');
                changeContainer.querySelector('svg').innerHTML = '<polyline points="6 9 12 15 18 9"></polyline>';
            }
        }
    }
    
    // 更新利润王牌卡片
    const profitKingCard = document.querySelector('.summary-card.profit-king');
    if (profitKingCard) {
        const valueEl = profitKingCard.querySelector('.summary-value');
        const changeEl = profitKingCard.querySelector('.summary-change span');
        const topItemEl = profitKingCard.querySelector('.top-item .item-value');
        
        if (valueEl) valueEl.innerHTML = `¥${summary.profit_king.amount.toLocaleString()}`;
        if (changeEl) changeEl.textContent = `${summary.profit_king.growth.toFixed(1)}%`;
        if (topItemEl) topItemEl.textContent = summary.profit_king.top_item;
        
        // 根据增长率调整上升/下降样式
        const changeContainer = profitKingCard.querySelector('.summary-change');
        if (changeContainer) {
            if (summary.profit_king.growth > 0) {
                changeContainer.classList.remove('down');
                changeContainer.classList.add('up');
                changeContainer.querySelector('svg').innerHTML = '<polyline points="18 15 12 9 6 15"></polyline>';
            } else {
                changeContainer.classList.remove('up');
                changeContainer.classList.add('down');
                changeContainer.querySelector('svg').innerHTML = '<polyline points="6 9 12 15 18 9"></polyline>';
            }
        }
    }
    
    // 更新亏损黑洞卡片
    const lossBlackHoleCard = document.querySelector('.summary-card.loss-black-hole');
    if (lossBlackHoleCard) {
        const valueEl = lossBlackHoleCard.querySelector('.summary-value');
        const changeEl = lossBlackHoleCard.querySelector('.summary-change span');
        const topItemEl = lossBlackHoleCard.querySelector('.top-item .item-value');
        
        if (valueEl) valueEl.innerHTML = `¥${summary.loss_black_hole.amount.toLocaleString()}`;
        if (changeEl) changeEl.textContent = `${Math.abs(summary.loss_black_hole.rate).toFixed(1)}%`;
        if (topItemEl) topItemEl.textContent = summary.loss_black_hole.top_item;
        
        // 亏损率永远是负的，保持下降样式
        const changeContainer = lossBlackHoleCard.querySelector('.summary-change');
        if (changeContainer) {
            changeContainer.classList.remove('up');
            changeContainer.classList.add('down');
            changeContainer.querySelector('svg').innerHTML = '<polyline points="6 9 12 15 18 9"></polyline>';
        }
    }
}