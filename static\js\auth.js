/**
 * auth.js - Authentication related functions
 */

// Check if user is logged in
export function checkLoginStatus() {
    const cookies = document.cookie.split(';');
    const loginAuth = cookies.find(cookie => cookie.trim().startsWith('loginAuth='));
    if (!loginAuth || loginAuth.split('=')[1].trim() !== 'true') {
        window.location.href = 'login.html';
        return false;
    }
    return true;
}

// Get user role from cookie
export function getUserRole() {
    const cookies = document.cookie.split(';');
    const roleCookie = cookies.find(cookie => cookie.trim().startsWith('userRole='));
    return roleCookie ? roleCookie.split('=')[1].trim() : null;
}

// Get username from cookie
export function getUsername() {
    const cookies = document.cookie.split(';');
    const usernameCookie = cookies.find(cookie => cookie.trim().startsWith('username='));
    return usernameCookie ? decodeURIComponent(usernameCookie.split('=')[1].trim()) : '用户';
}

// Get user from cookie (the one set during login)
export function getUser() {
    const cookies = document.cookie.split(';');
    const userCookie = cookies.find(cookie => cookie.trim().startsWith('user='));
    return userCookie ? decodeURIComponent(userCookie.split('=')[1].trim()) : '用户';
}

// Logout user by clearing cookies and redirecting to login page
export function logout() {
    // Delete all related cookies
    document.cookie = 'loginAuth=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    document.cookie = 'userRole=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    document.cookie = 'username=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    document.cookie = 'user=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    
    // Redirect to login page
    window.location.href = 'login.html';
}

// Check if user has admin role
export function isAdmin() {
    return getUserRole() === 'admin';
}

// Login handler for login form
export async function handleLogin(username, password) {
    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Set cookies with 24 hour expiration
            const expirationDate = new Date();
            expirationDate.setHours(expirationDate.getHours() + 24);
            document.cookie = `loginAuth=true; expires=${expirationDate.toUTCString()}; path=/; SameSite=Strict`;
            document.cookie = `userRole=${data.role}; expires=${expirationDate.toUTCString()}; path=/; SameSite=Strict`;
            document.cookie = `user=${encodeURIComponent(data.user)}; expires=${expirationDate.toUTCString()}; path=/; SameSite=Strict`;
            
            return { success: true };
        } else {
            return { 
                success: false, 
                message: data.message || '用户名或密码错误'
            };
        }
    } catch (error) {
        console.error('Login error:', error);
        return { 
            success: false, 
            message: '网络错误，请稍后重试'
        };
    }
}
