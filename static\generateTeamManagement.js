// 检查登录状态
function checkLoginStatus() {
    const cookies = document.cookie.split(';');
    const loginAuth = cookies.find(cookie => cookie.trim().startsWith('loginAuth='));
    if (!loginAuth || loginAuth.split('=')[1].trim() !== 'true') {
        window.location.href = 'login.html';
        return false;
    }
    return true;
}

// 添加样式
function addStyles() {
    const style = document.createElement('style');
    style.textContent = `
        :root {
             /* 主色调 & 辅助色 */
            --primary-color: #4f46e5; /* Indigo-600 */
            --primary-light: #e0e7ff; /* Indigo-100 */
            --primary-dark: #4338ca;  /* Indigo-700 */
            --secondary-color: #059669; /* Emerald-600 */ /* Changed secondary */

            /* 状态颜色 */
            --positive-color: #16a34a; /* Green-600 */
            --positive-bg: #f0fdf4;   /* Green-50 */
            --negative-color: #dc2626; /* Red-600 */
            --negative-bg: #fef2f2;   /* Red-50 */
            --warning-color: #f97316; /* Orange-500 */
            --warning-bg: #fff7ed;   /* Orange-50 */
            --neutral-color: #52525b; /* Zinc-600 */
            --neutral-bg: #f4f4f5;    /* Zinc-100 */

            /* 文本颜色 */
            --text-primary: #1f2937;   /* Gray-800 */
            --text-secondary: #4b5563; /* Gray-600 */
            --text-tertiary: #9ca3af;  /* Gray-400 */
            --text-on-primary: #ffffff;
            --text-link: var(--primary-color);
            --text-code: #374151; /* Gray-700 for monospaced numbers */

            /* 背景颜色 */
            --bg-canvas: #f9fafb;     /* Gray-50 */
            --bg-card: #ffffff;
            --bg-header: #ffffff;
            --bg-table-header: #f9fafb; /* Gray-50 */
            --bg-table-row-alt: #f9fafb; /* Gray-50 */
            --bg-table-row-hover: #f3f4f6; /* Gray-100 */
            --bg-nested-table: #f8fafc; /* Slate-50 slightly different */

            /* 边框颜色 */
            --border-color: #e5e7eb;   /* Gray-200 */
            --border-strong: #d1d5db;  /* Gray-300 */
            --border-focus: var(--primary-color);

            /* 阴影效果 */
            --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.05), 0 1px 2px -1px rgba(0, 0, 0, 0.04);
            --shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.07), 0 2px 4px -2px rgba(0, 0, 0, 0.04);
            --shadow-md: 0 5px 8px -3px rgba(0, 0, 0, 0.07), 0 3px 5px -4px rgba(0, 0, 0, 0.04);
            --shadow-lg: 0 8px 13px -5px rgba(0, 0, 0, 0.07), 0 4px 6px -6px rgba(0, 0, 0, 0.05);
            --shadow-focus: 0 0 0 3px rgba(79, 70, 229, 0.2); /* Focus ring */

            /* 圆角 */
            --radius-sm: 0.25rem;  /* 4px */
            --radius: 0.375rem;  /* 6px */
            --radius-md: 0.5rem;   /* 8px */
            --radius-lg: 0.75rem;  /* 12px */
            --radius-xl: 1rem;     /* 16px */
            --radius-full: 9999px;

            /* 动画 */
            --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

            /* 间距 */
            --spacing-1: 0.25rem; --spacing-2: 0.5rem; --spacing-3: 0.75rem; --spacing-4: 1rem;
            --spacing-5: 1.25rem; --spacing-6: 1.5rem; --spacing-8: 2rem; --spacing-10: 2.5rem;
            --spacing-12: 3rem; --spacing-16: 4rem;

             /* Fonts */
            --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
            --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        }

        /* Base Styles */
        *, *::before, *::after { box-sizing: border-box; }
        body {
            background-color: var(--bg-canvas); color: var(--text-primary);
            font-family: var(--font-sans);
            margin: 0; padding: 0; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;
            line-height: 1.6;
        }

        /* Header */
        .header {
            padding: var(--spacing-4) var(--spacing-6); /* Slightly reduced padding */
            border-bottom: 1px solid var(--border-color); background: var(--bg-header);
            position: sticky; top: 0; z-index: 100; box-shadow: var(--shadow-sm);
        }
        .header h1 {
            margin: 0; font-size: 1.375rem; /* 22px */ font-weight: 600;
            color: var(--text-primary); line-height: 1.3;
        }

        /* Main Content Area */
        .team-management {
            padding: var(--spacing-8) var(--spacing-6);
            max-width: 1600px; /* Wider for tables */ margin: 0 auto;
        }

        /* Section Title */
        .section-title {
            font-size: 1.125rem; /* 18px */ font-weight: 600;
            margin: 0 0 var(--spacing-5) 0; padding-bottom: var(--spacing-2);
            border-bottom: 1px solid var(--border-strong); color: var(--text-primary);
        }

        /* Summary Cards Grid */
        .team-stats-summary {
            display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); /* Slightly smaller min */
            gap: var(--spacing-5); margin-bottom: var(--spacing-8);
        }
        .summary-card {
            background: var(--bg-card); border-radius: var(--radius-lg);
            padding: var(--spacing-4) var(--spacing-5); box-shadow: var(--shadow);
            border: 1px solid var(--border-color); transition: var(--transition-normal);
            display: flex; flex-direction: column;
        }
        .summary-card:hover { transform: translateY(-2px); box-shadow: var(--shadow-md); }
        .summary-card h3 {
            margin: 0 0 var(--spacing-1) 0; font-size: 0.8rem; /* 13px */
            font-weight: 500; color: var(--text-secondary); text-transform: uppercase; letter-spacing: 0.04em;
        }
        .summary-card .value {
            font-size: 1.75rem; /* 28px */ font-weight: 600;
            color: var(--text-primary); line-height: 1.2; margin-bottom: var(--spacing-3);
            font-family: var(--font-mono); /* Monospaced for numbers */
        }
        .summary-card .trend {
            margin-top: auto; display: flex; align-items: center;
            font-size: 0.875rem; font-weight: 500;
        }
        .summary-card .trend.positive { color: var(--positive-color); }
        .summary-card .trend.negative { color: var(--negative-color); }
        .summary-card .trend-icon { margin-right: var(--spacing-1); width: 16px; height: 16px; }
        .summary-card .trend span { color: var(--text-tertiary); font-size: 0.8rem; margin-left: var(--spacing-1); }

        /* Team Group Section */
        .team-group {
            margin-bottom: var(--spacing-8);
            background: var(--bg-card); border-radius: var(--radius-lg);
            box-shadow: var(--shadow); border: 1px solid var(--border-color);
            overflow: hidden; /* Important for border-radius on tables */
        }
        .group-header {
            display: flex; align-items: center; gap: var(--spacing-4);
            padding: var(--spacing-4) var(--spacing-5); border-bottom: 1px solid var(--border-color);
            background-color: var(--bg-card); /* Simple white header */
        }
        .group-avatar {
            width: 36px; height: 36px; border-radius: var(--radius-md);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            display: flex; align-items: center; justify-content: center;
            font-size: 1rem; color: var(--text-on-primary); font-weight: 500; flex-shrink: 0;
        }
        .group-name {
            font-size: 1.1rem; font-weight: 600; color: var(--text-primary);
            flex-grow: 1; margin: 0;
        }

        /* Toggle Buttons (used for members/shops) */
        .toggle-button {
            display: inline-flex; align-items: center; justify-content: center;
            background: var(--bg-card); border: 1px solid var(--border-strong);
            border-radius: var(--radius); color: var(--text-secondary);
            font-size: 0.8125rem; /* 13px */ font-weight: 500;
            padding: var(--spacing-1) var(--spacing-2); cursor: pointer;
            transition: var(--transition-fast); gap: var(--spacing-1);
            white-space: nowrap; box-shadow: var(--shadow-xs);
        }
        .toggle-button:hover:not(:disabled) {
            background: var(--bg-table-row-hover); border-color: var(--text-tertiary);
            color: var(--text-primary); box-shadow: var(--shadow-sm);
        }
        .toggle-button:focus-visible { outline: 2px solid transparent; outline-offset: 2px; box-shadow: var(--shadow-focus); }
        .toggle-button:disabled {
            background: var(--bg-table-row-alt); color: var(--text-tertiary);
            border-color: var(--border-color); cursor: not-allowed; box-shadow: none; opacity: 0.7;
        }
        .toggle-button .toggle-icon { width: 16px; height: 16px; transition: transform 0.2s ease-in-out; color: var(--text-tertiary); }
        .toggle-button:hover:not(:disabled) .toggle-icon { color: var(--text-secondary); }
        .toggle-button.open .toggle-icon { transform: rotate(180deg); }

        /* --- Enhanced Table Styling --- */
        .table-container { width: 100%; overflow-x: auto; } /* Horizontal scroll on small screens */
        table {
            width: 100%; border-collapse: collapse; font-size: 0.875rem; /* 14px */
        }
        th, td {
            padding: var(--spacing-3) var(--spacing-4); /* Generous padding */
            text-align: left; border: none; /* Remove default borders */
            border-bottom: 1px solid var(--border-color); /* Horizontal separator */
            vertical-align: middle;
        }
        thead th {
            background-color: var(--bg-table-header); color: var(--text-secondary);
            font-size: 0.75rem; /* 12px */ font-weight: 500; /* Lighter header */
            text-transform: uppercase; letter-spacing: 0.05em;
            border-bottom: 2px solid var(--border-strong); /* Stronger header bottom border */
            white-space: nowrap; /* Prevent wrapping */
            position: sticky; top: 0; /* Sticky header within container if scrolled */
            z-index: 1; /* Ensure header stays above rows */
        }
        /* Specific alignment for numeric data */
        th.numeric, td.numeric { text-align: right; font-feature-settings: 'tnum'; font-variant-numeric: tabular-nums; }

        tbody tr { transition: background-color var(--transition-fast); }
        tbody tr:nth-child(even) { background-color: var(--bg-table-row-alt); }
        tbody tr:hover { background-color: var(--bg-table-row-hover); }
        tbody tr.expanded-content-row td { /* Row containing nested table */
            padding: 0; /* Remove padding to allow full width container */
            border-bottom: none; /* Border handled by container/nested table */
        }
         tbody tr:last-child td { border-bottom: none; } /* Remove border on very last row */

        /* Avatar in Table Cell */
        .cell-with-avatar { display: flex; align-items: center; gap: var(--spacing-3); }
        .avatar {
            width: 32px; height: 32px; border-radius: var(--radius-full);
            background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
            color: var(--primary-dark);
            display: flex; align-items: center; justify-content: center;
            font-size: 0.875rem; font-weight: 600; flex-shrink: 0;
        }
        .member-name { font-weight: 500; color: var(--text-primary); }
        .member-title { font-size: 0.8125rem; color: var(--text-secondary); margin-top: 2px; }

        /* Indicator Badge */
        .indicator {
            font-size: 0.8125rem; font-weight: 500; padding: 3px var(--spacing-2);
            border-radius: var(--radius); display: inline-flex; align-items: center;
            gap: var(--spacing-1); line-height: 1.2; white-space: nowrap;
        }
        .indicator .indicator-icon { width: 14px; height: 14px; }
        .indicator.positive { color: var(--positive-color); background: var(--positive-bg); }
        .indicator.negative { color: var(--negative-color); background: var(--negative-bg); }

        /* Trend Chart Container */
        .trend-chart-container { width: 90px; height: 30px; margin: 0 auto; }
        .trend-chart { display: block; width: 100%; height: 100%; }

        /* Collapsible Content (for Member and Shop Tables) */
        .collapsible-content-container {
            max-height: 0; opacity: 0; overflow: hidden;
            transition: max-height var(--transition-slow) ease-in-out, opacity var(--transition-normal) ease-in-out, padding var(--transition-slow) ease-in-out;
            will-change: max-height, opacity, padding;
            padding: 0; /* Padding applied inside */
            background-color: var(--bg-nested-table); /* Different bg for nested */
        }
        .collapsible-content-container.open {
            max-height: 5000px; /* Allow ample space */ opacity: 1;
            overflow: visible;
            padding: var(--spacing-4) var(--spacing-5); /* Add padding when open */
            border-top: 1px dashed var(--border-color); /* Separator line */
        }

        /* Nested Shop Table Specific Styles */
         /* The container is handled by .collapsible-content-container */
        .shop-table { /* Class for the nested shop table */
             font-size: 0.8125rem; /* Slightly smaller font */
        }
        .shop-table th, .shop-table td {
            padding: var(--spacing-2) var(--spacing-3); /* Reduced padding */
            border-bottom: 1px solid var(--border-color);
        }
        .shop-table thead th {
             background-color: var(--bg-table-header); /* Same header bg */
             font-size: 0.7rem; /* Even smaller header */
             border-bottom: 1px solid var(--border-strong);
        }
        .shop-table tbody tr:nth-child(even) { background-color: var(--bg-card); } /* Alternate zebra */
        .shop-table tbody tr:hover { background-color: var(--bg-table-row-hover); }
        .shop-table tbody tr:last-child td { border-bottom: none; }

        /* Shop Status Badge */
        .shop-status {
            font-size: 0.75rem; padding: 3px var(--spacing-2);
            border-radius: var(--radius-sm); font-weight: 500;
            display: inline-block; text-align: center; line-height: 1.3;
            white-space: nowrap;
        }
        .shop-status.active { background: var(--positive-bg); color: var(--positive-color); }
        .shop-status.inactive { background: var(--negative-bg); color: var(--negative-color); }

        /* Lazy Load / Load More */
        .lazy-load-container { text-align: center; padding: var(--spacing-4) 0; border-top: 1px solid var(--border-color); }
        .load-more-btn {
            background: var(--primary-light); color: var(--primary-dark); border: none;
            padding: var(--spacing-2) var(--spacing-4); border-radius: var(--radius);
            cursor: pointer; font-weight: 500; transition: var(--transition-fast);
            font-size: 0.875rem;
        }
        .load-more-btn:hover { background: var(--primary-color); color: var(--text-on-primary); box-shadow: var(--shadow-sm); }
        .load-more-btn:focus-visible { outline: 2px solid transparent; outline-offset: 2px; box-shadow: var(--shadow-focus); }

        /* Error Message */
        .error-message { /* Styles from previous version */ }

        /* Responsive Adjustments */
        @media (max-width: 992px) { /* Wider breakpoint for table adjustments */
             th, td { padding: var(--spacing-2) var(--spacing-3); } /* Reduce padding */
             .avatar { width: 28px; height: 28px; font-size: 0.8rem; }
             .cell-with-avatar { gap: var(--spacing-2); }
             .trend-chart-container { width: 70px; height: 25px; }
        }
        @media (max-width: 768px) {
            .team-management { padding: var(--spacing-6) var(--spacing-2); } /* Less horizontal padding */
            .header { padding: var(--spacing-3) var(--spacing-4); }
            .header h1 { font-size: 1.2rem; }
            .section-title { font-size: 1rem; }
            .summary-card .value { font-size: 1.5rem; }
            .group-header { padding: var(--spacing-3) var(--spacing-4); }
            .group-name { font-size: 1rem; }
            th, td { font-size: 0.8125rem; /* 13px */ }
            .collapsible-content-container.open { padding: var(--spacing-3) var(--spacing-4); }
            .shop-table th, .shop-table td { padding: var(--spacing-2); font-size: 0.75rem; } /* Even smaller nested */
            .toggle-button { font-size: 0.75rem; padding: 2px var(--spacing-2); }
        }

        /* Helper: SVG Icons */
        .svg-icon { display: inline-block; vertical-align: -0.125em; width: 1em; height: 1em; fill: currentColor; }

    `;
    document.head.appendChild(style);
}

// SVG Icons definition (reusing previous set)
const svgIcons = {
    chevronUp: `<svg class="svg-icon" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" /></svg>`,
    chevronDown: `<svg class="svg-icon" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" /></svg>`,
    arrowUp: `<svg class="svg-icon" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.22 14.78a.75.75 0 001.06 0l3.22-3.22v5.69a.75.75 0 001.5 0v-5.69l3.22 3.22a.75.75 0 101.06-1.06l-4.5-4.5a.75.75 0 00-1.06 0l-4.5 4.5a.75.75 0 000 1.06z" clip-rule="evenodd" /></svg>`,
    arrowDown: `<svg class="svg-icon" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M14.78 5.22a.75.75 0 01.001 1.06l-3.22 3.22v5.69a.75.75 0 01-1.5 0v-5.69l-3.22-3.22a.75.75 0 011.06-1.06l4.5 4.5a.75.75 0 010 1.061l-4.5 4.5a.75.75 0 01-1.06-1.06l3.22-3.22V6.75a.75.75 0 011.5 0v5.69l3.22-3.22a.75.75 0 011.06-.001z" clip-rule="evenodd" /></svg>`
};


// Main Function - Reverting to nested table structure
function generateTeamManagement() {
    if (!checkLoginStatus()) return;
    addStyles();

    const container = document.querySelector('.container');
    container.innerHTML = '';

    const header = document.createElement('div');
    header.className = 'header';
    header.innerHTML = '<h1>团队管理</h1>';
    container.appendChild(header);

    const teamManagement = document.createElement('div');
    teamManagement.className = 'team-management';
    container.appendChild(teamManagement);

    const loadingElement = document.createElement('div');
    loadingElement.textContent = '正在加载数据...';
    loadingElement.style.textAlign = 'center'; loadingElement.style.padding = '60px';
    loadingElement.style.fontSize = '1rem'; loadingElement.style.color = 'var(--text-secondary)';
    teamManagement.appendChild(loadingElement);

    // Fetch and process data
    fetch('/api/shops')
        .then(response => {
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            return response.json();
        })
        .then(shopData => {
            if (!shopData.success) throw new Error(shopData.message || '获取店铺数据失败');

            teamManagement.innerHTML = ''; // Clear loading

            const shops = shopData.data || [];
            console.log('API 返回的店铺数据 (示例):', shops.length > 0 ? shops[0] : '无数据');

            // --- Data Processing ---
            shops.forEach(shop => {
                shop.salesAmount = shop.dailySales || 0;
                shop.orderCount = shop.dailyOrders || 0;
                shop.profit = (shop.dailySales || 0) * 0.15;
                shop.profitRate = 15;
                shop.platformFee = (shop.dailySales || 0) * 0.03;
                shop.conversionRate = shop.conversionRate || 0;
                shop.operator = shop.operator || "未分配";
                shop.productCount = shop.productCount || 0;
                shop.rating = shop.rating || 'N/A';
                const shopDSR = shop.DSR ? parseFloat(String(shop.DSR).replace('%','')) : 0;
                shop.DSRValue = shopDSR;
                shop.DSRDisplay = shop.DSR || 'N/A';
                shop.status = shopDSR > 30 ? "active" : "inactive";
            });

            // --- Render Summary ---
            renderSummaryCards(shops, teamManagement);

            // --- Render Teams ---
            const teamsTitle = document.createElement('h2');
            teamsTitle.className = 'section-title';
            teamsTitle.textContent = '团队详情';
            teamManagement.appendChild(teamsTitle);

            renderTeamGroupsWithTables(shops, teamManagement);

            // Add Event Listeners (using delegation)
            addToggleListenersForTables();

        })
        .catch(error => {
            console.error('获取或处理团队管理数据失败:', error);
            teamManagement.innerHTML = createErrorMessage(error); // Assuming createErrorMessage exists
        });
}

// Helper: Render Summary Cards (can reuse from previous version)
function renderSummaryCards(shops, parentElement) {
    const totalSales = shops.reduce((sum, shop) => sum + shop.salesAmount, 0);
    const totalOrders = shops.reduce((sum, shop) => sum + shop.orderCount, 0);
    const totalProfit = shops.reduce((sum, shop) => sum + shop.profit, 0);
    const avgProfitRate = shops.length > 0
        ? shops.reduce((sum, shop) => sum + shop.profitRate, 0) / shops.length
        : 0;
    const dummyTrendValue = Math.random() * 5 - 2.5; // Example trend

    const statsSummary = document.createElement('div');
    statsSummary.className = 'team-stats-summary';

    const summaryData = [
        { title: '总销售额', value: `¥${totalSales.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`, trend: dummyTrendValue },
        { title: '总订单数', value: totalOrders.toLocaleString('zh-CN'), trend: dummyTrendValue * 1.2 },
        { title: '总利润', value: `¥${totalProfit.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`, trend: dummyTrendValue * 0.8 },
        { title: '平均利润率', value: `${avgProfitRate.toFixed(2)}%`, trend: dummyTrendValue * 0.5 }
    ];

    summaryData.forEach(item => {
         const isPositive = item.trend >= 0;
         const trendIcon = isPositive ? svgIcons.arrowUp : svgIcons.arrowDown;
         const trendClass = isPositive ? 'positive' : 'negative';
         const card = document.createElement('div');
         card.className = 'summary-card';
         card.innerHTML = `
            <h3>${item.title}</h3>
            <div class="value">${item.value}</div>
            <div class="trend ${trendClass}">
                <span class="trend-icon">${trendIcon}</span>
                ${Math.abs(item.trend).toFixed(1)}% <span>vs 昨日</span>
            </div>
         `;
         statsSummary.appendChild(card);
    });
    parentElement.appendChild(statsSummary);
}


// Helper: Render Team Groups using Tables
function renderTeamGroupsWithTables(shops, parentElement) {
    // Group shops by operator
     const operators = [...new Set(shops.map(shop => shop.operator).filter(Boolean))];
     const groupedByOperator = operators.map(operator => {
         const operatorShops = shops.filter(shop => shop.operator === operator);
         const operatorSales = operatorShops.reduce((sum, shop) => sum + shop.salesAmount, 0);
         const operatorProfit = operatorShops.reduce((sum, shop) => sum + shop.profit, 0);
         const operatorProfitRate = operatorShops.length > 0
             ? operatorShops.reduce((sum, shop) => sum + shop.profitRate, 0) / operatorShops.length
             : 0;
         const operatorOrders = operatorShops.reduce((sum, shop) => sum + shop.orderCount, 0);
         const operatorPlatformFee = operatorShops.reduce((sum, shop) => sum + shop.platformFee, 0);

         return {
             name: operator,
             title: '运营专员',
             salesAmount: operatorSales, // Needed for member row later
             profit: operatorProfit,
             profitRate: operatorProfitRate,
             orders: operatorOrders, // Needed for member row later
             platformFee: operatorPlatformFee, // Needed for member row later
             shops: operatorShops,
             trend: Array.from({length: 7}, () => Math.random() * (operatorProfit || 1000)), // Dummy trend for member
         };
     });

     // Structure into teams
    const teamGroups = [
        { name: '拼多多一组', members: groupedByOperator },
        { name: '拼多多二组', members: [] },
        { name: '拼多多三组', members: [] }
    ];

    // Calculate group totals
    teamGroups.forEach(group => {
         group.salesAmount = group.members.reduce((sum, member) => sum + (member.salesAmount || 0), 0);
         group.orders = group.members.reduce((sum, member) => sum + (member.orders || 0), 0);
         group.platformFee = group.members.reduce((sum, member) => sum + (member.platformFee || 0), 0);
         group.profit = group.members.reduce((sum, member) => sum + (member.profit || 0), 0);
         group.profitRate = group.members.length > 0
             ? group.members.reduce((sum, member) => sum + (member.profitRate || 0), 0) / group.members.length
             : 0;
         group.trend = group.members.length > 0 // Group trend (e.g., sum of member profits per day)
             ? Array.from({length: 7}, (_, i) => group.members.reduce((daySum, m) => daySum + (m.trend ? m.trend[i] : 0), 0))
             : [0,0,0,0,0,0,0];
     });

    // Render each team group
    teamGroups.forEach((group, index) => {
        const teamGroupEl = document.createElement('div');
        teamGroupEl.className = 'team-group';

        // --- Group Header ---
        const groupHeader = document.createElement('div');
        groupHeader.className = 'group-header';
        // Use a specific button to toggle members table, not the whole group
        groupHeader.innerHTML = `
            <div class="group-avatar">${group.name ? group.name[0] : '?'}</div>
            <h3 class="group-name">${group.name}</h3>
            <button class="toggle-button toggle-members" data-target="team-members-table-${index}" ${group.members.length === 0 ? 'disabled' : ''}>
                <span>${group.members.length > 0 ? `查看组员 (${group.members.length})` : '暂无人员'}</span>
                ${group.members.length > 0 ? `<span class="toggle-icon">${svgIcons.chevronDown}</span>` : ''}
            </button>
        `;
        teamGroupEl.appendChild(groupHeader);

        // --- Group Summary Table ---
        const groupTableContainer = document.createElement('div');
        groupTableContainer.className = 'table-container';
        const groupTable = document.createElement('table');
        groupTable.innerHTML = `
            <thead>
                <tr>
                    <th>组名</th>
                    <th class="numeric">销售额</th>
                    <th class="numeric">订单数</th>
                    <th class="numeric">平台费</th>
                    <th class="numeric">利润</th>
                    <th class="numeric">平均利润率</th>
                    <th>趋势 (7d)</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>${group.name}</td>
                    <td class="numeric">¥${group.salesAmount.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                    <td class="numeric">${group.orders.toLocaleString('zh-CN')}</td>
                    <td class="numeric">¥${group.platformFee.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                    <td class="numeric">¥${group.profit.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                    <td class="numeric">${renderIndicator(group.profitRate)}</td>
                    <td>
                        <div class="trend-chart-container">
                            <canvas class="trend-chart" data-trend="${group.trend.join(',')}"></canvas>
                        </div>
                    </td>
                </tr>
            </tbody>
        `;
        groupTableContainer.appendChild(groupTable);
        teamGroupEl.appendChild(groupTableContainer);
        drawTrendChart(groupTable.querySelector('.trend-chart')); // Draw chart after appending

        // --- Members Table (Collapsible) ---
        if (group.members && group.members.length > 0) {
            // Container for the collapsible members table
            const membersTableWrapper = document.createElement('div');
            membersTableWrapper.className = 'collapsible-content-container'; // This handles collapse
            membersTableWrapper.id = `team-members-table-${index}`; // Target for the toggle button

            const memberTableContainer = document.createElement('div');
            memberTableContainer.className = 'table-container'; // For potential scroll

            const memberTable = document.createElement('table');
            memberTable.innerHTML = `
                <thead>
                    <tr>
                        <th>运营人员</th>
                        <th>职位</th>
                        <th class="numeric">个人利润</th>
                        <th class="numeric">利润率</th>
                        <th class="numeric">管理店铺数</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="members-tbody-${index}">
                    <!-- Member rows will be injected here -->
                </tbody>
            `;
            memberTableContainer.appendChild(memberTable);
            membersTableWrapper.appendChild(memberTableContainer);


            // --- Lazy Load Logic for Member Rows ---
            const membersTbody = memberTable.querySelector(`#members-tbody-${index}`);
            const visibleMembersCount = 5; // Show first N members initially
            const initialMembers = group.members.slice(0, visibleMembersCount);
            const remainingMembers = group.members.slice(visibleMembersCount);

            renderMemberTableRows(initialMembers, membersTbody, index); // Render initial batch

            if (remainingMembers.length > 0) {
                const lazyLoadContainer = document.createElement('div');
                lazyLoadContainer.className = 'lazy-load-container';
                const loadMoreBtn = document.createElement('button');
                loadMoreBtn.className = 'load-more-btn';
                loadMoreBtn.textContent = `加载更多 (${remainingMembers.length})`;
                loadMoreBtn.onclick = () => {
                    renderMemberTableRows(remainingMembers, membersTbody, index); // Load the rest
                    lazyLoadContainer.remove(); // Remove button after loading
                };
                lazyLoadContainer.appendChild(loadMoreBtn);
                // Append lazy load button *inside* the collapsible wrapper, after the table container
                membersTableWrapper.appendChild(lazyLoadContainer);
            }
            // --- End Lazy Load ---

            teamGroupEl.appendChild(membersTableWrapper);
        }

        parentElement.appendChild(teamGroupEl);
    });
}

// Helper: Render Member Table Rows
function renderMemberTableRows(members, containerTbody, groupIndex) {
    members.forEach((member) => {
        if (!member) return;

        const memberRowIndex = containerTbody.rows.length / 2; // Each member takes 2 rows (data + collapsible)
        const memberId = `${groupIndex}-${memberRowIndex}`;
        const shopCount = member.shops ? member.shops.length : 0;
        const profitRate = member.profitRate || 0;

        // 1. Member Data Row
        const row = containerTbody.insertRow();
        row.id = `member-row-${memberId}`;
        row.innerHTML = `
            <td class="cell-with-avatar">
                <div class="avatar">${member.name ? member.name[0].toUpperCase() : '?'}</div>
                <div>
                    <div class="member-name">${member.name || '未知成员'}</div>
                    <div class="member-title">${member.title || '运营专员'}</div>
                </div>
            </td>
            <td>${member.title || '运营专员'}</td>
            <td class="numeric">¥${(member.profit || 0).toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
            <td class="numeric">${renderIndicator(profitRate)}</td>
            <td class="numeric">${shopCount}</td>
            <td>
                <button class="toggle-button toggle-shops" data-target="shop-table-container-${memberId}" ${shopCount === 0 ? 'disabled' : ''}>
                    <span>${shopCount > 0 ? '店铺' : '无店铺'}</span>
                    ${shopCount > 0 ? `<span class="toggle-icon">${svgIcons.chevronDown}</span>` : ''}
                </button>
            </td>
        `;

        // 2. Collapsible Row for Shops (initially hidden)
        const shopRow = containerTbody.insertRow();
        shopRow.className = 'expanded-content-row'; // Mark as row containing expandable content
        shopRow.style.display = 'none'; // Start hidden
        shopRow.id = `shop-row-${memberId}`; // ID for toggling display

        const shopCell = shopRow.insertCell();
        shopCell.colSpan = 6; // Span all columns
        shopCell.style.padding = '0'; // Remove padding for container
        shopCell.style.border = 'none';

        // Create the container div *inside* the cell
        const shopTableContainer = document.createElement('div');
        shopTableContainer.className = 'collapsible-content-container'; // This handles collapse animation
        shopTableContainer.id = `shop-table-container-${memberId}`; // Target for the button

        if (shopCount > 0 && member.shops) {
            shopTableContainer.appendChild(renderShopTable(member.shops));
        } else {
            shopTableContainer.innerHTML = '<div style="padding: var(--spacing-3); text-align: center; color: var(--text-tertiary);">无店铺信息</div>';
        }
        shopCell.appendChild(shopTableContainer);
    });
}

// Helper: Render the Shop Table Element
function renderShopTable(shops) {
    const tableContainer = document.createElement('div');
    tableContainer.className = 'table-container'; // For potential scroll

    const table = document.createElement('table');
    table.className = 'shop-table'; // Apply specific nested table styles
    table.innerHTML = `
        <thead>
            <tr>
                <th>店铺名称</th>
                <th class="numeric">销售额</th>
                <th class="numeric">订单数</th>
                <th class="numeric">转化率</th>
                <th class="numeric">商品数量</th>
                <th>DSR</th>
                <th>评分</th>
                <th>状态</th>
            </tr>
        </thead>
        <tbody>
            ${shops.map(shop => {
                if (!shop) return '';
                const shopStatus = shop.status || 'inactive';
                const shopStatusText = shopStatus === 'active' ? '正常' : '异常';
                return `
                    <tr>
                        <td>${shop.name || '未命名店铺'}</td>
                        <td class="numeric">¥${(shop.salesAmount || 0).toLocaleString('zh-CN', {minimumFractionDigits: 2})}</td>
                        <td class="numeric">${shop.orderCount || 0}</td>
                        <td class="numeric">${(shop.conversionRate || 0).toFixed(2)}%</td>
                        <td class="numeric">${shop.productCount || 0}</td>
                        <td>${shop.DSRDisplay || 'N/A'}</td>
                        <td>${shop.rating || 'N/A'}</td>
                        <td><span class="shop-status ${shopStatus}">${shopStatusText}</span></td>
                    </tr>
                `;
            }).join('')}
        </tbody>
    `;
    tableContainer.appendChild(table);
    return tableContainer;
}

// Helper: Render Indicator Badge HTML (reusable)
function renderIndicator(value) {
    const numValue = value || 0;
    const isPositive = numValue >= 0;
    const icon = isPositive ? svgIcons.arrowUp : svgIcons.arrowDown;
    const className = isPositive ? 'positive' : 'negative';
    return `<span class="indicator ${className}">
                <span class="indicator-icon">${icon}</span>
                ${Math.abs(numValue).toFixed(2)}%
            </span>`;
}

// Helper: Draw Trend Chart (reusable)
function drawTrendChart(canvas) {
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    const trendString = canvas.getAttribute('data-trend') || '';
    const trend = trendString.split(',').map(Number).filter(n => !isNaN(n));

    const width = canvas.width = canvas.offsetWidth;
    const height = canvas.height = canvas.offsetHeight;

    if (trend.length < 2) {
        ctx.clearRect(0, 0, width, height);
        ctx.fillStyle = 'var(--text-tertiary)'; ctx.font = '10px sans-serif';
        ctx.textAlign = 'center'; ctx.textBaseline = 'middle';
        ctx.fillText('N/A', width / 2, height / 2);
        return;
    }

    const padding = 2;
    const maxVal = Math.max(...trend); const minVal = Math.min(...trend);
    const range = (maxVal - minVal) === 0 ? 1 : (maxVal - minVal);
    const stepX = (width - 2 * padding) / (trend.length - 1);
    const scaleY = (height - 2 * padding) / range;
    const getX = (index) => padding + index * stepX;
    const getY = (value) => height - padding - (value - minVal) * scaleY;

    ctx.clearRect(0, 0, width, height);
    ctx.beginPath();
    ctx.strokeStyle = 'var(--primary-color)'; ctx.lineWidth = 1.5;
    ctx.lineCap = 'round'; ctx.lineJoin = 'round';
    trend.forEach((value, index) => {
        const x = getX(index); const y = getY(value);
        index === 0 ? ctx.moveTo(x, y) : ctx.lineTo(x, y);
    });
    ctx.stroke();

    // Optional subtle fill
    ctx.beginPath();
    ctx.moveTo(getX(0), height - padding);
    trend.forEach((value, index) => ctx.lineTo(getX(index), getY(value)));
    ctx.lineTo(getX(trend.length - 1), height - padding);
    ctx.closePath();
    const gradient = ctx.createLinearGradient(0, padding, 0, height - padding);
    gradient.addColorStop(0, 'rgba(79, 70, 229, 0.1)');
    gradient.addColorStop(1, 'rgba(79, 70, 229, 0)');
    ctx.fillStyle = gradient;
    ctx.fill();
}

// Helper: Create Error Message HTML (reusable)
function createErrorMessage(error) {
     return `
        <div class="error-message" style="text-align: center; padding: var(--spacing-8); color: var(--negative-color); background: var(--negative-bg); border: 1px solid var(--negative-color); border-radius: var(--radius-lg); margin: var(--spacing-8) 0;">
            <h3 style="margin: 0 0 var(--spacing-2) 0; color: var(--negative-color); font-size: 1.1rem;">加载错误</h3>
            <p style="margin: 0 0 var(--spacing-4) 0; font-size: 0.9rem;">获取团队数据时遇到问题: ${error.message || '请检查网络连接或联系管理员'}</p>
            <button onclick="generateTeamManagement()" style="margin-top: 16px; padding: 8px 16px; background: var(--primary-color); color: white; border: none; border-radius: var(--radius-sm); cursor: pointer; font-weight: 500; transition: var(--transition-fast);">重试加载</button>
        </div>
    `;
}

// Helper: Add Toggle Event Listeners for Table Structure
function addToggleListenersForTables() {
    document.body.addEventListener('click', function(event) {
        const button = event.target.closest('.toggle-button');
        if (!button || button.disabled) return;

        const targetId = button.getAttribute('data-target');
        if (!targetId) return;

        const targetElement = document.getElementById(targetId); // This is the collapsible container div
        if (!targetElement) return;

        const isOpen = targetElement.classList.toggle('open');
        button.classList.toggle('open', isOpen);

        const icon = button.querySelector('.toggle-icon');
        if (icon) {
            icon.innerHTML = isOpen ? svgIcons.chevronUp : svgIcons.chevronDown;
        }

        // Update button text based on type
        if (button.classList.contains('toggle-members')) {
            const label = button.querySelector('span:first-child');
            if (label) {
                // Extract count if present
                const countMatch = label.textContent.match(/\((\d+)\)/);
                const countStr = countMatch ? ` (${countMatch[1]})` : '';
                label.textContent = isOpen ? `收起组员${countStr}` : `查看组员${countStr}`;
            }
        } else if (button.classList.contains('toggle-shops')) {
            const label = button.querySelector('span:first-child');
             if(label) label.textContent = isOpen ? '收起' : '店铺'; // Shorter text for shops

            // Also toggle the display of the *entire table row* containing the collapsible div
            const memberId = targetId.replace('shop-table-container-', '');
            const shopRow = document.getElementById(`shop-row-${memberId}`);
            if (shopRow) {
                shopRow.style.display = isOpen ? 'table-row' : 'none';
            }
        }
    });
}

// Initial Load Example
// generateTeamManagement();