// SPU数据分析平台 JavaScript
class SpuAnalytics {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 50;
        this.totalPages = 1;
        this.currentFilters = {};
        this.charts = {};
        this.dateRange = null;
        this.resizeHandler = null;
        this.currentChartType = 'line'; // 添加当前图表类型跟踪
        this.currentChartData = null; // 保存当前图表数据
        
        this.init();
    }

    async init() {
        console.log('初始化SPU分析平台...');
        this.initDatePicker();
        this.setDefaultDateRange();
        this.bindEvents();
        await this.loadInitialData();
    }

    setDefaultDateRange() {
        // 设置默认日期范围：2025年1月1日至昨天
        const yesterday = moment().subtract(1, 'day');
        const start = moment('2025-01-01');
        
        $('#dateRange').val(start.format('YYYY-MM-DD') + ' - ' + yesterday.format('YYYY-MM-DD'));
        this.dateRange = {
            start: start.format('YYYY-MM-DD'),
            end: yesterday.format('YYYY-MM-DD')
        };
    }

    initDatePicker() {
        // 初始化日期范围选择器
        $('#dateRange').daterangepicker({
            autoUpdateInput: false,
            locale: {
                cancelLabel: '清除',
                applyLabel: '确定',
                format: 'YYYY-MM-DD',
                customRangeLabel: '自定义',
                daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
                monthNames: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                firstDay: 1
            }
        });

        $('#dateRange').on('apply.daterangepicker', (ev, picker) => {
            $(ev.target).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
            this.dateRange = {
                start: picker.startDate.format('YYYY-MM-DD'),
                end: picker.endDate.format('YYYY-MM-DD')
            };
        });

        $('#dateRange').on('cancel.daterangepicker', (ev, picker) => {
            $(ev.target).val('');
            this.dateRange = null;
        });
    }

    bindEvents() {
        // 搜索输入框
        $('#searchInput').on('input', this.debounce(() => {
            this.currentPage = 1;
            this.loadTableData();
        }, 300));

        // 页面大小改变
        $('#pageSize').on('change', () => {
            this.pageSize = parseInt($('#pageSize').val());
            this.currentPage = 1;
            this.loadTableData();
        });

        // 图表类型切换
        $('[data-chart-type]').on('click', (e) => {
            const chartType = $(e.target).closest('button').data('chart-type');
            $('[data-chart-type]').removeClass('active');
            $(e.target).closest('button').addClass('active');
            this.updateChartType(chartType);
        });



        // 趋势图刷新按钮
        $('#refreshTrendBtn').on('click', () => {
            this.refreshTrendChart();
        });

        // 全选功能
        $('#selectAll').on('change', (e) => {
            const isChecked = $(e.target).is(':checked');
            $('.spu-select').prop('checked', isChecked);
            
            // 根据年度对比状态调用相应的方法
            const isYearCompare = $('#yearCompareSwitch').is(':checked');
            if (isYearCompare) {
                this.updateSelectedTrendYearCompare();
            } else {
                this.updateSelectedTrend();
            }
        });

        // 单选功能
        $(document).on('change', '.spu-select', () => {
            // 根据年度对比状态调用相应的方法
            const isYearCompare = $('#yearCompareSwitch').is(':checked');
            if (isYearCompare) {
                this.updateSelectedTrendYearCompare();
            } else {
                this.updateSelectedTrend();
            }
        });

        // 维度切换
        $('input[name="dataDimension"]').on('change', () => {
            const dimension = $('input[name="dataDimension"]:checked').val();
            const isYearCompare = $('#yearCompareSwitch').is(':checked');
            console.log('维度切换到:', dimension, '当前年度对比状态:', isYearCompare);
            
            // 立即更新表头
            this.updateTableHeaders(dimension);
            
            // 重新加载表格数据和统计数据
            Promise.all([
                this.loadTableData(),
                this.loadStatistics()
            ]).then(() => {
                console.log('维度切换后数据加载完成');
                // 数据加载完成后，重新渲染趋势图
                setTimeout(() => {
                    // 检查年度对比状态和选中项目
                    const isYearCompare = $('#yearCompareSwitch').is(':checked');
                    const selectedItems = $('.spu-select:checked');
                    console.log('维度切换后选中项目数量:', selectedItems.length, '年度对比状态:', isYearCompare);
                    
                    if (selectedItems.length > 0) {
                        console.log('重新渲染多线趋势图，维度:', dimension, '年度对比:', isYearCompare);
                        // 根据年度对比状态调用相应的方法
                        if (isYearCompare) {
                            this.updateSelectedTrendYearCompare();
                        } else {
                            this.updateSelectedTrend();
                        }
                    } else {
                        console.log('没有选中项目，显示空图表');
                        // 如果没有选中项目，根据年度对比状态显示相应的空图表
                        if (isYearCompare) {
                            // 年度对比模式下的空图表
                            this.loadTrendDataYearCompare();
                        } else {
                            // 常规模式下的空图表
                            this.renderTrendChart([], []);
                        }
                    }
                }, 300); // 增加延迟时间，确保表格渲染完成
            }).catch(error => {
                console.error('维度切换后数据加载失败:', error);
                // 出错时显示空图表
                const isYearCompare = $('#yearCompareSwitch').is(':checked');
                if (isYearCompare) {
                    this.loadTrendDataYearCompare();
                } else {
                    this.renderTrendChart([], []);
                }
            });
        });

        // 年度对比功能
        $('#yearCompareSwitch').on('change', (e) => {
            const isEnabled = $(e.target).is(':checked');
            console.log('年度对比开关:', isEnabled);
            
            if (isEnabled) {
                $('#yearCompareControls').fadeIn(300);
                
                // 添加年度对比提示
                this.showYearCompareHint();
                
                // 为图表区域添加年度对比样式
                $('.chart-card').parent().addClass('year-compare-active');
                
                // 启用年度对比时，重新加载趋势数据
                this.refreshTrendChart();
            } else {
                $('#yearCompareControls').fadeOut(300);
                
                // 移除年度对比提示
                this.hideYearCompareHint();
                
                // 移除图表区域的年度对比样式
                $('.chart-card').parent().removeClass('year-compare-active');
                
                // 关闭年度对比时，重新加载常规趋势数据
                this.refreshTrendChart();
            }
        });

        // 年度对比年份选择
        $('#compareYear1, #compareYear2').on('change', () => {
            const year1 = $('#compareYear1').val();
            const year2 = $('#compareYear2').val();
            
            if (year1 === year2) {
                alert('请选择不同的年份进行对比');
                return;
            }
            
            console.log('年度对比年份变更:', year1, 'vs', year2);
            
            // 如果年度对比开关是打开的，更新提示并重新加载数据
            if ($('#yearCompareSwitch').is(':checked')) {
                this.showYearCompareHint(); // 更新提示内容
                this.refreshTrendChart();
            }
        });
    }

    updateTableHeaders(dimension) {
        const productCodeHeader = $('#productCodeHeader');
        
        if (dimension === 'style_code') {
            // 款式编码维度：隐藏商品编码列
            productCodeHeader.hide();
            // 隐藏所有商品编码数据单元格
            $('#spuTable tbody tr').each(function() {
                $(this).find('td:nth-child(3)').hide();
            });
        } else {
            // 商品编码维度：显示商品编码列
            productCodeHeader.show();
            // 显示所有商品编码数据单元格
            $('#spuTable tbody tr').each(function() {
                $(this).find('td:nth-child(3)').show();
            });
        }
    }

    refreshTrendChart() {
        const dimension = $('input[name="dataDimension"]:checked').val();
        const isYearCompare = $('#yearCompareSwitch').is(':checked');
        
        console.log('刷新趋势图... 当前维度:', dimension, '年度对比:', isYearCompare);
        
        // 根据当前状态决定刷新逻辑
        const selectedItems = $('.spu-select:checked');
        
        console.log('选中项目数量:', selectedItems.length);
        
        if (isYearCompare) {
            // 年度对比模式
            if (selectedItems.length > 0) {
                console.log('刷新年度对比多条线图');
                this.updateSelectedTrendYearCompare();
            } else {
                console.log('刷新年度对比默认趋势图');
                this.loadTrendDataYearCompare();
            }
        } else {
            // 常规模式
            if (selectedItems.length > 0) {
                console.log('刷新多条线图');
                this.updateSelectedTrend();
            } else {
                console.log('刷新默认趋势图');
                this.loadTrendData();
            }
        }
    }

    async loadInitialData() {
        try {
            console.log('开始加载初始数据...');
            this.showLoading();
            
            // 设置默认的表头显示状态
            const dimension = $('input[name="dataDimension"]:checked').val();
            this.updateTableHeaders(dimension);
            
            // 只加载表格数据和统计数据，不触碰趋势图
            await Promise.all([
                this.loadTableData(),
                this.loadStatistics()
            ]);
            
            // 只有在没有选中SPU的情况下才显示空图表
            const selectedItems = $('.spu-select:checked');
            if (selectedItems.length === 0) {
                this.renderTrendChart([], []);
            }
            
            this.hideLoading();
            console.log('初始数据加载完成');
        } catch (error) {
            console.error('数据加载失败:', error);
            this.showError('数据加载失败，请刷新页面重试');
            this.hideLoading();
        }
    }

    async loadStatistics() {
        try {
            console.log('加载统计数据...');
            const params = new URLSearchParams();

            // 添加日期范围
            if (this.dateRange) {
                params.append('start_date', this.dateRange.start);
                params.append('end_date', this.dateRange.end);
            }

            // 添加维度参数
            const dimension = $('input[name="dataDimension"]:checked').val();
            if (dimension) {
                params.append('dimension', dimension);
            }

            const response = await fetch(`/api/spu-statistics?${params}`);
            if (!response.ok) throw new Error('统计数据获取失败');
            
            const data = await response.json();
            console.log('统计数据:', data);
            
            if (data.success && data.statistics) {
                const stats = data.statistics;
                $('#totalRecords').text(this.formatNumber(stats.total_records || 0));
                $('#totalStyles').text(this.formatNumber(stats.total_styles || 0));
                $('#totalProducts').text(this.formatNumber(stats.total_products || 0));
                $('#totalSales').text(this.formatCurrency(stats.total_sales || 0));
            }
            
        } catch (error) {
            console.error('统计数据加载失败:', error);
            $('#totalRecords').text('-');
            $('#totalStyles').text('-');
            $('#totalProducts').text('-');
            $('#totalSales').text('-');
        }
    }

    async loadTrendData() {
        try {
            console.log('加载趋势数据...');
            const params = new URLSearchParams({
                granularity: $('#granularity').val() || 'daily'
            });

            if (this.dateRange) {
                params.append('date_start', this.dateRange.start);
                params.append('date_end', this.dateRange.end);
            }

            // 获取趋势数据
            const trendResponse = await fetch(`/api/spu-trend-data?${params}`);
            if (!trendResponse.ok) throw new Error('趋势数据获取失败');
            const trendData = await trendResponse.json();
            
            console.log('趋势数据:', trendData);
            
            // 处理趋势数据
            if (trendData.success) {
                this.renderTrendChart(trendData.dates || [], trendData.data || []);
            } else {
                this.renderTrendChart([], []);
            }
            
        } catch (error) {
            console.error('趋势数据加载失败:', error);
            this.renderTrendChart([], []);
        }
    }



    async updateSelectedTrend() {
        const selectedItems = $('.spu-select:checked');
        if (selectedItems.length === 0) {
            // 如果没有选择，显示空图表
            this.renderTrendChart([], []);
            return;
        }

        try {
            const dimension = $('input[name="dataDimension"]:checked').val();
            const selectedSpus = [];
            
            selectedItems.each(function() {
                const checkbox = $(this);
                const row = checkbox.closest('tr');
                
                // 从复选框的data属性中获取数据，这样更可靠
                const styleCode = checkbox.data('style-code') || row.find('td:eq(1)').text();
                const productCode = checkbox.data('product-code') || '';
                let productName = checkbox.data('product-name') || '';
                
                // 如果从data属性获取不到商品名称，从DOM中获取
                if (!productName) {
                    const productNameElement = row.find('td:eq(3) .fw-semibold');
                    productName = productNameElement.length > 0 ? productNameElement.text() : row.find('td:eq(3)').text();
                }
                
                if (dimension === 'style_code') {
                    // 款式编码维度：使用款式编码作为显示名称
                    selectedSpus.push({ 
                        style_code: styleCode, 
                        product_code: '',
                        product_name: styleCode, // 使用款式编码作为显示名称
                        dimension: 'style_code'
                    });
                } else {
                    // 商品编码维度：使用完整信息
                    selectedSpus.push({ 
                        style_code: styleCode, 
                        product_code: productCode,
                        product_name: productName,
                        dimension: 'product_code'
                    });
                }
            });

            console.log('更新选择的SPU趋势 (维度:', dimension, '):', selectedSpus);
            console.log('准备发送的SPU数据:', JSON.stringify(selectedSpus, null, 2));
            
            // 验证发送的数据
            selectedSpus.forEach((spu, index) => {
                console.log(`SPU ${index + 1}:`, {
                    dimension: spu.dimension,
                    style_code: spu.style_code,
                    product_code: spu.product_code,
                    product_name: spu.product_name
                });
            });
            
            const params = new URLSearchParams({
                granularity: $('#granularity').val() || 'daily',
                selected_spus: JSON.stringify(selectedSpus),
                multi_line: 'true',  // 标识需要多条线
                dimension: dimension  // 传递维度信息
            });

            if (this.dateRange) {
                params.append('date_start', this.dateRange.start);
                params.append('date_end', this.dateRange.end);
            }

            const response = await fetch(`/api/spu-trend-data?${params}`);
            if (!response.ok) throw new Error('选择的SPU趋势数据获取失败');
            const data = await response.json();
            
            console.log('API响应数据:', data);
            console.log('图表日期数组:', data.dates);
            console.log('图表数据数组:', data.data);
            
            if (data.success) {
                this.renderMultiLineTrendChart(data.dates || [], data.data || []);
            } else {
                console.error('API返回失败状态:', data.message || '未知错误');
                this.renderTrendChart([], []);
            }
            
        } catch (error) {
            console.error('选择的SPU趋势数据加载失败:', error);
            this.renderTrendChart([], []);
        }
    }

    async loadTableData() {
        try {
            console.log('加载表格数据...');
            const params = new URLSearchParams({
                page: this.currentPage,
                page_size: this.pageSize,
                sort_by: $('#sortBy').val() || 'total_amount',
                sort_order: 'desc'
            });

            const searchTerm = $('#searchInput').val().trim();
            if (searchTerm) {
                params.append('search', searchTerm);
            }

            if (this.dateRange) {
                params.append('start_date', this.dateRange.start);
                params.append('end_date', this.dateRange.end);
            }

            // 添加维度参数
            const dimension = $('input[name="dataDimension"]:checked').val();
            if (dimension) {
                params.append('dimension', dimension);
            }

            $('#tableLoading').show();
            const response = await fetch(`/api/spu-data?${params}`);
            if (!response.ok) throw new Error('表格数据获取失败');
            
            const data = await response.json();
            console.log('表格数据:', data);
            
            if (data.success) {
                this.renderTable(data.data || []);
                this.renderPagination(data.pagination || {});
            } else {
                this.renderTable([]);
                this.renderPagination({});
            }
            
            return Promise.resolve(); // 明确返回Promise
            
        } catch (error) {
            console.error('表格数据加载失败:', error);
            this.renderTable([]);
            this.renderPagination({});
            return Promise.reject(error); // 错误时返回rejected Promise
        } finally {
            $('#tableLoading').hide();
        }
    }

    renderTrendChart(dates, spuData) {
        console.log('渲染ECharts趋势图表:', dates, spuData);
        
        // 保存图表数据
        this.currentChartData = { dates, data: spuData };
        
        try {
            const chartDom = document.getElementById('trendChart');
            
            // 销毁现有图表实例
            if (this.charts.trend) {
                this.charts.trend.dispose();
            }

            if (!dates || dates.length === 0 || !spuData || spuData.length === 0) {
                // 显示无数据提示
                chartDom.innerHTML = `
                    <div class="d-flex align-items-center justify-content-center h-100">
                        <div class="text-center">
                            <i class="bi bi-graph-up text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-3 mb-1">暂无趋势数据</p>
                            <small class="text-muted">请选择日期范围或调整筛选条件</small>
                        </div>
                    </div>
                `;
                this.currentChartData = null;
                return;
            }

            // 清空容器内容
            chartDom.innerHTML = '';
            
            // 初始化ECharts实例
            this.charts.trend = echarts.init(chartDom);

            // 聚合所有SPU的数据按日期
            const aggregatedData = {};
            
            spuData.forEach(spu => {
                dates.forEach((date, index) => {
                    if (!aggregatedData[date]) {
                        aggregatedData[date] = { amount: 0, quantity: 0, profit: 0 };
                    }
                    aggregatedData[date].amount += spu.amounts[index] || 0;
                    aggregatedData[date].quantity += spu.quantities[index] || 0;
                    aggregatedData[date].profit += spu.profits ? (spu.profits[index] || 0) : 0;
                });
            });

            const salesData = dates.map(date => aggregatedData[date].amount);

            // 格式化日期标签
            const formattedLabels = dates.map(date => {
                const momentDate = moment(date);
                return momentDate.format('MM/DD');
            });

            // 根据当前图表类型配置系列
            const seriesConfig = {
                name: '销售额',
                data: salesData,
                emphasis: {
                    focus: 'series'
                }
            };

            // 根据图表类型设置不同的配置
            if (this.currentChartType === 'bar') {
                seriesConfig.type = 'bar';
                seriesConfig.itemStyle = {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#4f46e5' },
                        { offset: 1, color: '#06b6d4' }
                    ])
                };
            } else if (this.currentChartType === 'area') {
                seriesConfig.type = 'line';
                seriesConfig.smooth = true;
                seriesConfig.symbol = 'none';
                seriesConfig.lineStyle = {
                    width: 3,
                    color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                        { offset: 0, color: '#4f46e5' },
                        { offset: 1, color: '#06b6d4' }
                    ])
                };
                seriesConfig.areaStyle = {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(79, 70, 229, 0.4)' },
                        { offset: 1, color: 'rgba(79, 70, 229, 0.1)' }
                    ])
                };
            } else { // line
                seriesConfig.type = 'line';
                seriesConfig.smooth = true;
                seriesConfig.symbol = 'none';
                seriesConfig.lineStyle = {
                    width: 3,
                    color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                        { offset: 0, color: '#4f46e5' },
                        { offset: 1, color: '#06b6d4' }
                    ])
                };
                // 折线图默认不显示面积
            }

            // ECharts配置
            const option = {
                title: {
                    text: '销售趋势分析',
                    left: 'center',
                    textStyle: {
                        color: '#374151',
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    borderColor: 'rgba(79, 70, 229, 0.3)',
                    borderWidth: 1,
                    textStyle: {
                        color: '#ffffff'
                    },
                    formatter: function(params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(param => {
                            result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ¥${param.value.toLocaleString()}<br/>`;
                        });
                        return result;
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: this.currentChartType === 'bar', // 柱状图需要边界间隙
                    data: formattedLabels,
                    axisLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    },
                    axisLabel: {
                        color: '#6b7280',
                        fontSize: 12
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '销售额 (¥)',
                    nameTextStyle: {
                        color: '#374151',
                        fontSize: 14,
                        fontWeight: 'bold'
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    },
                    axisLabel: {
                        color: '#6b7280',
                        fontSize: 12,
                        formatter: function(value) {
                            return '¥' + value.toLocaleString();
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                },
                series: [seriesConfig]
            };

            // 设置配置项并渲染图表
            this.charts.trend.setOption(option);
            
            // 监听窗口大小变化
            this.setupResizeListener();
            
        } catch (error) {
            console.error('ECharts图表渲染失败:', error);
            const chartDom = document.getElementById('trendChart');
            chartDom.innerHTML = `
                <div class="d-flex align-items-center justify-content-center h-100">
                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3 mb-1">图表加载失败</p>
                        <small class="text-muted">请刷新页面重试</small>
                    </div>
                </div>
            `;
        }
    }

    renderMultiLineTrendChart(dates, spuData) {
        console.log('渲染ECharts多条线趋势图表:', dates, spuData);
        
        // 保存图表数据
        this.currentChartData = { dates, data: spuData };
        
        try {
            const chartDom = document.getElementById('trendChart');
            
            // 销毁现有图表实例
            if (this.charts.trend) {
                this.charts.trend.dispose();
            }

            if (!dates || dates.length === 0 || !spuData || spuData.length === 0) {
                // 显示无数据提示
                chartDom.innerHTML = `
                    <div class="d-flex align-items-center justify-content-center h-100">
                        <div class="text-center">
                            <i class="bi bi-graph-up text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-3 mb-1">请选择SPU查看趋势</p>
                            <small class="text-muted">在下方数据表格中勾选要查看的商品</small>
                        </div>
                    </div>
                `;
                this.currentChartData = null;
                return;
            }

            // 清空容器内容
            chartDom.innerHTML = '';
            
            // 初始化ECharts实例
            this.charts.trend = echarts.init(chartDom);

            // 格式化日期标签
            const formattedLabels = dates.map(date => {
                const momentDate = moment(date);
                return momentDate.format('MM/DD');
            });

            // 颜色配置
            const colors = [
                '#4f46e5', '#06b6d4', '#10b981', '#f59e0b', '#ef4444', 
                '#8b5cf6', '#22c55e', '#f97316', '#6366f1', '#14b8a6'
            ];

            // 为每个SPU创建系列
            const series = spuData.map((spu, index) => {
                const color = colors[index % colors.length];
                const dimension = $('input[name="dataDimension"]:checked').val();
                
                // 根据维度决定显示名称
                let displayName;
                if (dimension === 'style_code') {
                    displayName = spu.style_code; // 款式编码维度显示款式编码
                } else {
                    displayName = `${spu.product_code} ${spu.product_name}`; // 商品编码维度显示完整信息
                }
                
                const seriesConfig = {
                    name: displayName,
                    data: spu.amounts || [],
                    emphasis: {
                        focus: 'series'
                    }
                };

                // 根据图表类型设置不同的配置
                if (this.currentChartType === 'bar') {
                    seriesConfig.type = 'bar';
                    seriesConfig.itemStyle = {
                        color: color
                    };
                } else if (this.currentChartType === 'area') {
                    seriesConfig.type = 'line';
                    seriesConfig.smooth = true;
                    seriesConfig.symbol = 'none';
                    seriesConfig.lineStyle = {
                        width: 2,
                        color: color
                    };
                    seriesConfig.areaStyle = {
                        color: color + '30', // 添加透明度
                    };
                } else { // line
                    seriesConfig.type = 'line';
                    seriesConfig.smooth = true;
                    seriesConfig.symbol = 'none';
                    seriesConfig.lineStyle = {
                        width: 2,
                        color: color
                    };
                }
                
                return seriesConfig;
            });

            // ECharts配置
            const option = {
                title: {
                    text: '多SPU销售趋势对比',
                    left: 'center',
                    textStyle: {
                        color: '#374151',
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    borderColor: 'rgba(79, 70, 229, 0.3)',
                    borderWidth: 1,
                    textStyle: {
                        color: '#ffffff'
                    },
                    formatter: function(params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(param => {
                            result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ¥${param.value.toLocaleString()}<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    type: 'scroll',
                    orient: 'horizontal',
                    left: 'center',
                    top: 30,
                    textStyle: {
                        color: '#374151',
                        fontSize: 12
                    },
                    formatter: function(name) {
                        // 截断过长的图例名称
                        return name.length > 20 ? name.substring(0, 20) + '...' : name;
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: formattedLabels,
                    axisLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    },
                    axisLabel: {
                        color: '#6b7280',
                        fontSize: 12
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '销售额 (¥)',
                    nameTextStyle: {
                        color: '#374151',
                        fontSize: 14,
                        fontWeight: 'bold'
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    },
                    axisLabel: {
                        color: '#6b7280',
                        fontSize: 12,
                        formatter: function(value) {
                            return '¥' + value.toLocaleString();
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                },
                series: series
            };

            // 设置配置项并渲染图表
            this.charts.trend.setOption(option);
            
            // 监听窗口大小变化
            this.setupResizeListener();
            
        } catch (error) {
            console.error('ECharts多条线图表渲染失败:', error);
            const chartDom = document.getElementById('trendChart');
            chartDom.innerHTML = `
                <div class="d-flex align-items-center justify-content-center h-100">
                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3 mb-1">图表加载失败</p>
                        <small class="text-muted">请刷新页面重试</small>
                    </div>
                </div>
            `;
        }
    }

    renderTable(data) {
        const tbody = $('#spuTableBody');
        tbody.empty();

        if (!data.length) {
            tbody.append(`
                <tr>
                    <td colspan="11" class="text-center text-muted py-4">
                        <i class="bi bi-inbox fs-1"></i>
                        <p class="mt-2">暂无数据</p>
                    </td>
                </tr>
            `);
            return;
        }

        const dimension = $('input[name="dataDimension"]:checked').val();
        
        data.forEach((item, index) => {
            // 计算同比增长率
            const currentAmount = item.total_amount || 0;
            const lastYearAmount = item.last_year_amount || 0;
            let yoyGrowth = 0;
            let yoyDisplay = '-';
            
            if (lastYearAmount > 0) {
                yoyGrowth = ((currentAmount - lastYearAmount) / lastYearAmount) * 100;
                const growthClass = yoyGrowth >= 0 ? 'text-success' : 'text-danger';
                const growthIcon = yoyGrowth >= 0 ? 'bi-arrow-up' : 'bi-arrow-down';
                yoyDisplay = `<span class="${growthClass}">
                    <i class="bi ${growthIcon}"></i> ${Math.abs(yoyGrowth).toFixed(1)}%
                </span>`;
            } else if (currentAmount > 0) {
                yoyDisplay = '<span class="text-info">新增</span>';
            }
            
            // 根据维度决定商品编码列的显示
            const productCodeCell = dimension === 'style_code' 
                ? `<td style="display: none;">${item.product_code || '-'}</td>`
                : `<td>${item.product_code || '-'}</td>`;
            
            const row = `
                <tr>
                    <td class="text-center">
                        <input type="checkbox" class="form-check-input spu-select" 
                               data-style-code="${item.style_code || ''}" 
                               data-product-code="${item.product_code || ''}"
                               data-product-name="${(item.product_name || '').replace(/"/g, '&quot;')}"
                               ${index < 5 ? 'checked' : ''}>
                    </td>
                    <td>${item.style_code || '-'}</td>
                    ${productCodeCell}
                    <td>
                        <div class="d-flex align-items-center">
                            <div>
                                <div class="fw-semibold">${item.product_name || '未知商品'}</div>
                                ${item.product_category ? `<small class="text-muted">${item.product_category}</small>` : ''}
                            </div>
                        </div>
                    </td>
                    <td>${item.supplier || '-'}</td>
                    <td class="text-end">${this.formatNumber(item.total_quantity || 0)}</td>
                    <td class="text-end text-primary fw-semibold">${this.formatCurrency(item.total_amount || 0)}</td>
                    <td class="text-end">${yoyDisplay}</td>
                    <td class="text-center">${item.days_count || 0}</td>
                    <td class="text-center">
                        <button class="btn btn-sm btn-outline-primary" onclick="spuAnalytics.showDetail('${item.product_code || item.style_code}')">
                            <i class="bi bi-eye"></i>
                        </button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });

        // 渲染完成后触发默认选择的趋势更新
        setTimeout(() => {
            const selectedItems = $('.spu-select:checked');
            if (selectedItems.length > 0) {
                // 根据年度对比状态调用相应的方法
                const isYearCompare = $('#yearCompareSwitch').is(':checked');
                if (isYearCompare) {
                    this.updateSelectedTrendYearCompare();
                } else {
                    this.updateSelectedTrend();
                }
            }
        }, 100);
    }

    renderPagination(pagination) {
        const paginationEl = $('#pagination');
        paginationEl.empty();

        if (!pagination.total_pages || pagination.total_pages <= 1) {
            return;
        }

        this.totalPages = pagination.total_pages;
        const currentPage = pagination.page || 1;

        // 上一页
        const prevDisabled = currentPage <= 1 ? 'disabled' : '';
        paginationEl.append(`
            <li class="page-item ${prevDisabled}">
                <a class="page-link" href="#" onclick="spuAnalytics.goToPage(${currentPage - 1})">
                    <i class="bi bi-chevron-left"></i>
                </a>
            </li>
        `);

        // 页码
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(this.totalPages, currentPage + 2);

        if (startPage > 1) {
            paginationEl.append(`
                <li class="page-item">
                    <a class="page-link" href="#" onclick="spuAnalytics.goToPage(1)">1</a>
                </li>
            `);
            if (startPage > 2) {
                paginationEl.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const active = i === currentPage ? 'active' : '';
            paginationEl.append(`
                <li class="page-item ${active}">
                    <a class="page-link" href="#" onclick="spuAnalytics.goToPage(${i})">${i}</a>
                </li>
            `);
        }

        if (endPage < this.totalPages) {
            if (endPage < this.totalPages - 1) {
                paginationEl.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
            }
            paginationEl.append(`
                <li class="page-item">
                    <a class="page-link" href="#" onclick="spuAnalytics.goToPage(${this.totalPages})">${this.totalPages}</a>
                </li>
            `);
        }

        // 下一页
        const nextDisabled = currentPage >= this.totalPages ? 'disabled' : '';
        paginationEl.append(`
            <li class="page-item ${nextDisabled}">
                <a class="page-link" href="#" onclick="spuAnalytics.goToPage(${currentPage + 1})">
                    <i class="bi bi-chevron-right"></i>
                </a>
            </li>
        `);
    }

    goToPage(page) {
        if (page < 1 || page > this.totalPages || page === this.currentPage) return;
        this.currentPage = page;
        this.loadTableData();
    }

    updateChartType(chartType) {
        console.log('切换图表类型到:', chartType);
        this.currentChartType = chartType;
        
        // 如果有当前图表数据，重新渲染
        if (this.currentChartData) {
            const isYearCompare = $('#yearCompareSwitch').is(':checked');
            const selectedItems = $('.spu-select:checked');
            
            if (isYearCompare) {
                if (selectedItems.length > 0) {
                    // 年度对比多条线图
                    this.renderMultiLineYearCompareTrendChart(
                        this.currentChartData.dates, 
                        this.currentChartData.data,
                        this.currentChartData.year1,
                        this.currentChartData.year2
                    );
                } else {
                    // 年度对比单图
                    this.renderYearCompareTrendChart(
                        this.currentChartData.dates, 
                        this.currentChartData.data,
                        this.currentChartData.year1,
                        this.currentChartData.year2
                    );
                }
            } else {
                if (selectedItems.length > 0) {
                    // 常规多条线图
                    this.renderMultiLineTrendChart(this.currentChartData.dates, this.currentChartData.data);
                } else {
                    // 常规单图
                    this.renderTrendChart(this.currentChartData.dates, this.currentChartData.data);
                }
            }
        }
    }

    async showDetail(code) {
        try {
            // 首次加载时设置搜索框的值
            $('#detailSearchInput').val(code);
            
            // 加载详情数据
            await this.loadDetailData(code);
            
            // 绑定搜索事件（只绑定一次）
            if (!this.detailSearchBound) {
                $('#detailSearchBtn').off('click').on('click', () => {
                    const searchCode = $('#detailSearchInput').val().trim();
                    if (searchCode) {
                        this.loadDetailData(searchCode);
                    }
                });
                
                $('#detailSearchInput').off('keypress').on('keypress', (e) => {
                    if (e.which === 13) { // 回车键
                        const searchCode = $('#detailSearchInput').val().trim();
                        if (searchCode) {
                            this.loadDetailData(searchCode);
                        }
                    }
                });
                
                this.detailSearchBound = true;
            }
            
            $('#detailModal').modal('show');
            
        } catch (error) {
            console.error('详情加载失败:', error);
            this.showError('详情加载失败');
        }
    }

    async loadDetailData(code) {
        try {
            $('#detailContent').html(`
                <div class="text-center py-4">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2 text-muted">数据加载中...</p>
                </div>
            `);
            
            const response = await fetch(`/api/spu-search?q=${encodeURIComponent(code)}&limit=1`);
            if (!response.ok) throw new Error('详情获取失败');
            
            const data = await response.json();
            const item = data.data?.[0];
            
            if (!item) {
                $('#detailContent').html(`
                    <div class="text-center py-4">
                        <i class="bi bi-search text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3">未找到商品编码为 "${code}" 的数据</p>
                        <small class="text-muted">请检查编码是否正确或尝试其他搜索条件</small>
                    </div>
                `);
                return;
            }

            const detailContent = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">基本信息</h6>
                        <table class="table table-sm">
                            <tr><td class="fw-semibold">款式编码:</td><td>${item.style_code || '-'}</td></tr>
                            <tr><td class="fw-semibold">商品编码:</td><td>${item.product_code || '-'}</td></tr>
                            <tr><td class="fw-semibold">商品名称:</td><td>${item.product_name || '-'}</td></tr>
                            <tr><td class="fw-semibold">品牌:</td><td>${item.brand || '-'}</td></tr>
                            <tr><td class="fw-semibold">供应商:</td><td>${item.supplier || '-'}</td></tr>
                            <tr><td class="fw-semibold">类别:</td><td>${item.category || '-'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">销售数据</h6>
                        <table class="table table-sm">
                            <tr><td class="fw-semibold">总销量:</td><td class="text-end">${this.formatNumber(item.total_quantity || 0)}</td></tr>
                            <tr><td class="fw-semibold">总销售额:</td><td class="text-end text-primary">${this.formatCurrency(item.total_amount || 0)}</td></tr>
                            <tr><td class="fw-semibold">总利润:</td><td class="text-end text-success">${this.formatCurrency(item.total_profit || 0)}</td></tr>
                            <tr><td class="fw-semibold">利润率:</td><td class="text-end">${item.total_amount > 0 ? ((item.total_profit / item.total_amount) * 100).toFixed(2) : '0.00'}%</td></tr>
                            <tr><td class="fw-semibold">销售天数:</td><td class="text-end">${item.sales_days || 0}</td></tr>
                            <tr><td class="fw-semibold">平均日销:</td><td class="text-end">${item.sales_days > 0 ? (item.total_quantity / item.sales_days).toFixed(2) : '0.00'}</td></tr>
                        </table>
                    </div>
                </div>
            `;

            $('#detailContent').html(detailContent);
            
        } catch (error) {
            console.error('详情数据加载失败:', error);
            $('#detailContent').html(`
                <div class="text-center py-4">
                    <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                    <p class="text-muted mt-3">数据加载失败</p>
                    <small class="text-muted">${error.message}</small>
                </div>
            `);
        }
    }

    showLoading() {
        // 可以添加全局加载指示器
        console.log('显示加载状态');
    }

    hideLoading() {
        // 隐藏全局加载指示器
        console.log('隐藏加载状态');
    }

    showError(message) {
        console.error(message);
        alert(message);  // 临时使用alert，可以替换为更美观的提示
    }

    formatNumber(num) {
        return new Intl.NumberFormat('zh-CN').format(num || 0);
    }

    formatCurrency(amount) {
        return '¥' + new Intl.NumberFormat('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount || 0);
    }

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 年度对比相关方法
    async loadTrendDataYearCompare() {
        try {
            console.log('加载年度对比趋势数据...');
            const year1 = $('#compareYear1').val();
            const year2 = $('#compareYear2').val();
            
            if (year1 === year2) {
                this.renderTrendChart([], []);
                return;
            }

            // 获取筛选面板的日期范围
            let dateStart, dateEnd;
            if (this.dateRange) {
                dateStart = this.dateRange.start;
                dateEnd = this.dateRange.end;
            } else {
                // 年度对比模式下，需要覆盖两个对比年份的完整范围
                const minYear = Math.min(parseInt(year1), parseInt(year2));
                const maxYear = Math.max(parseInt(year1), parseInt(year2));
                
                // 使用较早年份的1月1日作为开始日期
                dateStart = `${minYear}-01-01`;
                
                // 使用较晚年份的12月31日作为结束日期，如果是当前年份则使用昨天
                const currentYear = moment().year();
                if (maxYear >= currentYear) {
                    const yesterday = moment().subtract(1, 'day');
                    dateEnd = yesterday.format('YYYY-MM-DD');
                } else {
                    dateEnd = `${maxYear}-12-31`;
                }
            }

            console.log('年度对比使用日期范围:', dateStart, '到', dateEnd);

            const params = new URLSearchParams({
                granularity: $('#granularity').val() || 'daily',
                year_compare: 'true',
                year1: year1,
                year2: year2,
                date_start: dateStart,
                date_end: dateEnd
            });

            // 获取趋势数据
            const trendResponse = await fetch(`/api/spu-trend-data?${params}`);
            if (!trendResponse.ok) throw new Error('年度对比趋势数据获取失败');
            const trendData = await trendResponse.json();
            
            console.log('年度对比趋势数据:', trendData);
            
            // 处理趋势数据
            if (trendData.success) {
                this.renderYearCompareTrendChart(trendData.dates || [], trendData.data || [], year1, year2);
            } else {
                this.renderTrendChart([], []);
            }
            
        } catch (error) {
            console.error('年度对比趋势数据加载失败:', error);
            this.renderTrendChart([], []);
        }
    }



    async updateSelectedTrendYearCompare() {
        const selectedItems = $('.spu-select:checked');
        if (selectedItems.length === 0) {
            this.renderTrendChart([], []);
            return;
        }

        try {
            const dimension = $('input[name="dataDimension"]:checked').val();
            const year1 = $('#compareYear1').val();
            const year2 = $('#compareYear2').val();
            
            if (year1 === year2) {
                this.renderTrendChart([], []);
                return;
            }

            const selectedSpus = [];
            
            selectedItems.each(function() {
                const checkbox = $(this);
                const row = checkbox.closest('tr');
                
                const styleCode = checkbox.data('style-code') || row.find('td:eq(1)').text();
                const productCode = checkbox.data('product-code') || '';
                let productName = checkbox.data('product-name') || '';
                
                if (!productName) {
                    const productNameElement = row.find('td:eq(3) .fw-semibold');
                    productName = productNameElement.length > 0 ? productNameElement.text() : row.find('td:eq(3)').text();
                }
                
                if (dimension === 'style_code') {
                    selectedSpus.push({ 
                        style_code: styleCode, 
                        product_code: '',
                        product_name: styleCode,
                        dimension: 'style_code'
                    });
                } else {
                    selectedSpus.push({ 
                        style_code: styleCode, 
                        product_code: productCode,
                        product_name: productName,
                        dimension: 'product_code'
                    });
                }
            });

            console.log('更新选择的SPU年度对比趋势 (维度:', dimension, '):', selectedSpus);
            
            // 获取筛选面板的日期范围
            let dateStart, dateEnd;
            if (this.dateRange) {
                dateStart = this.dateRange.start;
                dateEnd = this.dateRange.end;
            } else {
                // 年度对比模式下，需要覆盖两个对比年份的完整范围
                const minYear = Math.min(parseInt(year1), parseInt(year2));
                const maxYear = Math.max(parseInt(year1), parseInt(year2));
                
                // 使用较早年份的1月1日作为开始日期
                dateStart = `${minYear}-01-01`;
                
                // 使用较晚年份的12月31日作为结束日期，如果是当前年份则使用昨天
                const currentYear = moment().year();
                if (maxYear >= currentYear) {
                    const yesterday = moment().subtract(1, 'day');
                    dateEnd = yesterday.format('YYYY-MM-DD');
                } else {
                    dateEnd = `${maxYear}-12-31`;
                }
            }

            console.log('年度对比多选使用日期范围:', dateStart, '到', dateEnd);
            
            const params = new URLSearchParams({
                granularity: $('#granularity').val() || 'daily',
                selected_spus: JSON.stringify(selectedSpus),
                multi_line: 'true',
                dimension: dimension,
                year_compare: 'true',
                year1: year1,
                year2: year2,
                date_start: dateStart,
                date_end: dateEnd
            });

            const response = await fetch(`/api/spu-trend-data?${params}`);
            if (!response.ok) throw new Error('选择的SPU年度对比趋势数据获取失败');
            const data = await response.json();
            
            console.log('年度对比API响应数据:', data);
            
            if (data.success) {
                this.renderMultiLineYearCompareTrendChart(data.dates || [], data.data || [], year1, year2);
            } else {
                console.error('年度对比API返回失败状态:', data.message || '未知错误');
                this.renderTrendChart([], []);
            }
            
        } catch (error) {
            console.error('选择的SPU年度对比趋势数据加载失败:', error);
            this.renderTrendChart([], []);
        }
    }

    renderYearCompareTrendChart(dates, spuData, year1, year2) {
        console.log('渲染年度对比趋势图表:', dates, spuData, year1, year2);
        
        // 保存图表数据
        this.currentChartData = { dates, data: spuData, year1, year2 };
        
        try {
            const chartDom = document.getElementById('trendChart');
            
            if (this.charts.trend) {
                this.charts.trend.dispose();
            }

            if (!dates || dates.length === 0 || !spuData || spuData.length < 2) {
                chartDom.innerHTML = `
                    <div class="d-flex align-items-center justify-content-center h-100">
                        <div class="text-center">
                            <i class="bi bi-graph-up text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-3 mb-1">暂无年度对比数据</p>
                            <small class="text-muted">请选择不同的年份进行对比</small>
                        </div>
                    </div>
                `;
                return;
            }

            chartDom.innerHTML = '';
            this.charts.trend = echarts.init(chartDom);

            // 格式化日期标签
            const formattedLabels = dates.map(date => {
                const momentDate = moment(date);
                return momentDate.format('MM/DD');
            });

            // 准备数据 - 确定哪个是今年（较晚年份）和去年（较早年份）
            const thisYear = Math.max(parseInt(year1), parseInt(year2));
            const lastYear = Math.min(parseInt(year1), parseInt(year2));
            
            const thisYearData = spuData.find(item => item.year === thisYear.toString());
            const lastYearData = spuData.find(item => item.year === lastYear.toString());

            const thisYearSales = thisYearData ? thisYearData.amounts : [];
            const lastYearSales = lastYearData ? lastYearData.amounts : [];

            // ECharts配置
            const option = {
                title: {
                    text: `${lastYear}年 vs ${thisYear}年 销售对比`,
                    left: 'center',
                    textStyle: {
                        color: '#374151',
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    borderColor: 'rgba(79, 70, 229, 0.3)',
                    borderWidth: 1,
                    textStyle: {
                        color: '#ffffff'
                    },
                    formatter: function(params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(param => {
                            result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ¥${param.value.toLocaleString()}<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    data: [`${lastYear}年(去年)`, `${thisYear}年(今年)`],
                    top: 40,
                    textStyle: {
                        fontSize: 12,
                        color: '#374151'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: formattedLabels,
                    axisLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    },
                    axisLabel: {
                        color: '#6b7280',
                        fontSize: 12
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '销售额 (¥)',
                    nameTextStyle: {
                        color: '#374151',
                        fontSize: 14,
                        fontWeight: 'bold'
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    },
                    axisLabel: {
                        color: '#6b7280',
                        fontSize: 12,
                        formatter: function(value) {
                            return '¥' + value.toLocaleString();
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                },
                series: [
                    {
                        name: `${lastYear}年(去年)`,
                        type: 'line',
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 4,
                        lineStyle: {
                            width: 3,
                            color: '#94a3b8',
                            type: 'dashed' // 去年使用虚线
                        },
                        itemStyle: {
                            color: '#94a3b8'
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: 'rgba(148, 163, 184, 0.2)' },
                                { offset: 1, color: 'rgba(148, 163, 184, 0.02)' }
                            ])
                        },
                        data: lastYearSales,
                        emphasis: {
                            focus: 'series'
                        }
                    },
                    {
                        name: `${thisYear}年(今年)`,
                        type: 'line',
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 4,
                        lineStyle: {
                            width: 3,
                            color: '#4f46e5'
                            // 今年使用实线（默认）
                        },
                        itemStyle: {
                            color: '#4f46e5'
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: 'rgba(79, 70, 229, 0.3)' },
                                { offset: 1, color: 'rgba(79, 70, 229, 0.05)' }
                            ])
                        },
                        data: thisYearSales,
                        emphasis: {
                            focus: 'series'
                        }
                    }
                ]
            };

            this.charts.trend.setOption(option);
            this.setupResizeListener();
            
        } catch (error) {
            console.error('年度对比图表渲染失败:', error);
            const chartDom = document.getElementById('trendChart');
            chartDom.innerHTML = `
                <div class="d-flex align-items-center justify-content-center h-100">
                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3 mb-1">年度对比图表加载失败</p>
                        <small class="text-muted">请刷新页面重试</small>
                    </div>
                </div>
            `;
        }
    }

    renderMultiLineYearCompareTrendChart(dates, spuData, year1, year2) {
        console.log('渲染多条线年度对比趋势图表:', dates, spuData, year1, year2);
        
        // 保存图表数据
        this.currentChartData = { dates, data: spuData, year1, year2 };
        
        try {
            const chartDom = document.getElementById('trendChart');
            
            if (this.charts.trend) {
                this.charts.trend.dispose();
            }

            if (!dates || dates.length === 0 || !spuData || spuData.length === 0) {
                chartDom.innerHTML = `
                    <div class="d-flex align-items-center justify-content-center h-100">
                        <div class="text-center">
                            <i class="bi bi-graph-up text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-3 mb-1">请选择SPU查看年度对比</p>
                            <small class="text-muted">在下方数据表格中勾选要查看的商品</small>
                        </div>
                    </div>
                `;
                return;
            }

            chartDom.innerHTML = '';
            this.charts.trend = echarts.init(chartDom);

            // 格式化日期标签
            const formattedLabels = dates.map(date => {
                const momentDate = moment(date);
                return momentDate.format('MM/DD');
            });

            // 颜色配置
            const colors = ['#4f46e5', '#06b6d4', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];
            const series = [];
            const legendData = [];

            // 确定哪个是今年（较晚年份）和去年（较早年份）
            const thisYear = Math.max(parseInt(year1), parseInt(year2)).toString();
            const lastYear = Math.min(parseInt(year1), parseInt(year2)).toString();
            
            // 为每个SPU创建两条线（两个年份）
            spuData.forEach((spu, index) => {
                const dimension = $('input[name="dataDimension"]:checked').val();
                
                let displayName;
                if (dimension === 'style_code') {
                    displayName = spu.style_code;
                } else {
                    displayName = spu.product_name || spu.product_code || spu.style_code;
                }

                const baseColor = colors[index % colors.length];
                
                // 先添加去年数据（虚线）
                if (spu.year_data && spu.year_data[lastYear]) {
                    const seriesName = `${displayName} (${lastYear}年)`;
                    legendData.push(seriesName);
                    series.push({
                        name: seriesName,
                        type: 'line',
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 4,
                        lineStyle: {
                            width: 2,
                            color: baseColor,
                            type: 'dashed' // 去年使用虚线
                        },
                        itemStyle: {
                            color: baseColor
                        },
                        data: spu.year_data[lastYear].amounts || [],
                        emphasis: {
                            focus: 'series'
                        }
                    });
                }

                // 再添加今年数据（实线）
                if (spu.year_data && spu.year_data[thisYear]) {
                    const seriesName = `${displayName} (${thisYear}年)`;
                    legendData.push(seriesName);
                    series.push({
                        name: seriesName,
                        type: 'line',
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 4,
                        lineStyle: {
                            width: 2,
                            color: baseColor
                            // 今年使用实线（默认）
                        },
                        itemStyle: {
                            color: baseColor
                        },
                        data: spu.year_data[thisYear].amounts || [],
                        emphasis: {
                            focus: 'series'
                        }
                    });
                }
            });

            // ECharts配置
            const option = {
                title: {
                    text: `SPU年度对比 (${lastYear}年 vs ${thisYear}年)`,
                    left: 'center',
                    textStyle: {
                        color: '#374151',
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    borderColor: 'rgba(79, 70, 229, 0.3)',
                    borderWidth: 1,
                    textStyle: {
                        color: '#ffffff'
                    },
                    formatter: function(params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(param => {
                            result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ¥${param.value.toLocaleString()}<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    data: legendData,
                    top: 40,
                    type: 'scroll',
                    pageIconColor: '#374151',
                    pageIconInactiveColor: '#cbd5e1',
                    pageTextStyle: {
                        color: '#374151'
                    },
                    textStyle: {
                        fontSize: 11,
                        color: '#374151'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '20%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: formattedLabels,
                    axisLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    },
                    axisLabel: {
                        color: '#6b7280',
                        fontSize: 12
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '销售额 (¥)',
                    nameTextStyle: {
                        color: '#374151',
                        fontSize: 14,
                        fontWeight: 'bold'
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    },
                    axisLabel: {
                        color: '#6b7280',
                        fontSize: 12,
                        formatter: function(value) {
                            return '¥' + value.toLocaleString();
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                },
                series: series
            };

            this.charts.trend.setOption(option);
            this.setupResizeListener();
            
        } catch (error) {
            console.error('多条线年度对比图表渲染失败:', error);
            const chartDom = document.getElementById('trendChart');
            chartDom.innerHTML = `
                <div class="d-flex align-items-center justify-content-center h-100">
                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3 mb-1">年度对比图表加载失败</p>
                        <small class="text-muted">请刷新页面重试</small>
                    </div>
                </div>
            `;
        }
    }

    // 年度对比UI提示方法
    showYearCompareHint() {
        // 如果提示已存在，先移除
        this.hideYearCompareHint();
        
        const year1 = $('#compareYear1').val();
        const year2 = $('#compareYear2').val();
        const thisYear = Math.max(parseInt(year1), parseInt(year2));
        const lastYear = Math.min(parseInt(year1), parseInt(year2));
        
        // 获取当前设置的日期范围
        let dateRangeText = '';
        if (this.dateRange) {
            dateRangeText = `日期范围：${this.dateRange.start} 到 ${this.dateRange.end}`;
        } else {
            dateRangeText = '日期范围：使用筛选面板设置的范围';
        }
        
        const hintHtml = `
            <div class="year-compare-hint" id="yearCompareHint">
                <i class="bi bi-lightbulb"></i>
                <strong>年度对比模式已启用</strong> - 正在对比 <strong>${lastYear}年</strong> 与 <strong>${thisYear}年</strong> 的数据
                <br>
                <small>
                    • <strong>实线</strong>表示${thisYear}年（今年）数据，<strong>虚线</strong>表示${lastYear}年（去年）数据
                    <br>
                    • ${dateRangeText}
                    <br>
                    • 请在下方SPU数据详情表格中勾选商品来查看具体的年度对比趋势
                    <br>
                    • 支持切换不同图表类型：折线图、柱状图、面积图
                </small>
            </div>
        `;
        
        // 在图表卡片内容前插入提示
        $('.chart-card .card-body').prepend(hintHtml);
    }
    
    hideYearCompareHint() {
        $('#yearCompareHint').remove();
    }

    setupResizeListener() {
        // 移除之前的监听器
        if (this.resizeHandler) {
            window.removeEventListener('resize', this.resizeHandler);
        }
        
        // 创建新的监听器
        this.resizeHandler = () => {
            if (this.charts.trend) {
                this.charts.trend.resize();
            }
        };
        
        // 添加监听器
        window.addEventListener('resize', this.resizeHandler);
    }
}

// 全局函数
window.setQuickDate = function(period) {
    const today = moment();
    const yesterday = moment().subtract(1, 'day');
    let start, end;
    
    switch(period) {
        case '2025':
            start = moment('2025-01-01');
            end = yesterday;
            break;
        case '2024':
            start = moment('2024-01-01');
            end = moment('2024-12-31');
            break;
        case '2023':
            start = moment('2023-01-01');
            end = moment('2023-12-31');
            break;
        case 'last30':
            start = moment().subtract(30, 'days');
            end = yesterday;
            break;
        case 'last90':
            start = moment().subtract(90, 'days');
            end = yesterday;
            break;
        default:
            return;
    }
    
    $('#dateRange').val(start.format('YYYY-MM-DD') + ' - ' + end.format('YYYY-MM-DD'));
    window.spuAnalytics.dateRange = {
        start: start.format('YYYY-MM-DD'),
        end: end.format('YYYY-MM-DD')
    };
    
    // 自动应用新的日期范围
    if (window.spuAnalytics) {
        window.spuAnalytics.currentPage = 1;
        // 重新加载数据但保持图表状态
        Promise.all([
            window.spuAnalytics.loadTableData(),
            window.spuAnalytics.loadStatistics()
        ]).then(() => {
            // 如果有选中的SPU，重新渲染多线图
            const selectedItems = $('.spu-select:checked');
            if (selectedItems.length > 0) {
                // 根据年度对比状态调用相应的方法
                const isYearCompare = $('#yearCompareSwitch').is(':checked');
                if (isYearCompare) {
                    window.spuAnalytics.updateSelectedTrendYearCompare();
                } else {
                    window.spuAnalytics.updateSelectedTrend();
                }
            }
        });
    }
};

window.applyFilters = function() {
    window.spuAnalytics.currentPage = 1;
    // 重新加载表格数据和统计数据，保持现有的图表状态
    Promise.all([
        window.spuAnalytics.loadTableData(),
        window.spuAnalytics.loadStatistics()
    ]).then(() => {
        // 如果有选中的SPU，重新渲染多线图
        const selectedItems = $('.spu-select:checked');
        if (selectedItems.length > 0) {
            // 根据年度对比状态调用相应的方法
            const isYearCompare = $('#yearCompareSwitch').is(':checked');
            if (isYearCompare) {
                window.spuAnalytics.updateSelectedTrendYearCompare();
            } else {
                window.spuAnalytics.updateSelectedTrend();
            }
        }
    });
};

window.resetFilters = function() {
    $('#dateRange').val('');
    $('#granularity').val('daily');
    $('#sortBy').val('total_amount');
    $('#searchInput').val('');
    
    window.spuAnalytics.dateRange = null;
    window.spuAnalytics.currentPage = 1;
    window.spuAnalytics.setDefaultDateRange();
    
    // 重新加载数据但保持图表状态
    Promise.all([
        window.spuAnalytics.loadTableData(),
        window.spuAnalytics.loadStatistics()
    ]).then(() => {
        // 清除所有选中状态
        $('.spu-select').prop('checked', false);
        $('#selectAll').prop('checked', false);
        
        // 根据年度对比状态显示相应的空图表
        const isYearCompare = $('#yearCompareSwitch').is(':checked');
        if (isYearCompare) {
            // 年度对比模式下的空图表
            window.spuAnalytics.loadTrendDataYearCompare();
        } else {
            // 常规模式下的空图表
            window.spuAnalytics.renderTrendChart([], []);
        }
    });
};

window.exportData = function() {
    // 导出功能实现
    const params = new URLSearchParams({
        format: 'excel',
        sort_by: $('#sortBy').val() || 'total_amount',
        sort_order: 'desc'
    });

    const searchTerm = $('#searchInput').val().trim();
    if (searchTerm) {
        params.append('search', searchTerm);
    }

    if (window.spuAnalytics.dateRange) {
        params.append('start_date', window.spuAnalytics.dateRange.start);
        params.append('end_date', window.spuAnalytics.dateRange.end);
    }
    
    window.open(`/api/spu-data?${params}&export=true`, '_blank');
};

// 初始化
$(document).ready(function() {
    window.spuAnalytics = new SpuAnalytics();
}); 