---
description: 
globs: 
alwaysApply: true
---
# 部署与运维指南

## 🚀 系统部署指南

基于 [app.py](mdc:app.py) 和 [requirements.txt](mdc:requirements.txt) 的分析，提供完整的部署和运维指南：

## 📋 部署前准备

### 系统要求
- **操作系统**: Windows 10/11, Linux, macOS
- **Python版本**: Python 3.8+
- **内存要求**: 最小4GB，推荐8GB+
- **存储空间**: 最小10GB可用空间
- **网络要求**: 支持HTTP/HTTPS访问

### 依赖安装
```bash
# 安装Python依赖
pip install -r requirements.txt

# 验证关键依赖
python -c "import flask, pandas, sqlite3; print('依赖检查通过')"
```

### 环境配置
```bash
# 创建必要目录
mkdir -p static/db
mkdir -p static/uploads
mkdir -p static/task_attachments
mkdir -p static/temp
mkdir -p static/sale_data_xlsx
mkdir -p static/spu_data_xlsx

# 设置目录权限
chmod -R 755 static/
```

## 🏗️ 部署模式

### 开发环境部署
```bash
# 直接运行Flask应用
python app.py

# 开启调试模式
export FLASK_DEBUG=1
python app.py
```

### 生产环境部署

#### 使用 Gunicorn (推荐)
```bash
# 安装Gunicorn
pip install gunicorn

# 启动应用
gunicorn -w 4 -b 0.0.0.0:5000 app:app

# 使用配置文件
gunicorn -c gunicorn.conf.py app:app
```

#### Gunicorn配置文件 (gunicorn.conf.py)
```python
bind = "0.0.0.0:5000"
workers = 4
worker_class = "sync"
timeout = 300
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
accesslog = "logs/access.log"
errorlog = "logs/error.log"
loglevel = "info"
```

#### 使用 uWSGI
```bash
# 安装uWSGI
pip install uwsgi

# 启动应用
uwsgi --http :5000 --wsgi-file app.py --callable app --processes 4 --threads 2
```

### Docker 部署
```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

RUN mkdir -p static/db static/uploads static/task_attachments static/temp

EXPOSE 5000

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  web:
    build: .
    ports:
      - "5000:5000"
    volumes:
      - ./static:/app/static
      - ./logs:/app/logs
    environment:
      - FLASK_ENV=production
    restart: unless-stopped
```

## 🔒 安全配置

### HTTPS配置
```python
# 在app.py中添加SSL配置
from flask_talisman import Talisman

# 强制HTTPS
Talisman(app, force_https=True)

# SSL证书配置
if __name__ == '__main__':
    app.run(
        host='0.0.0.0',
        port=443,
        ssl_context=('cert.pem', 'key.pem')
    )
```

### 防火墙配置
```bash
# Ubuntu/Debian
sudo ufw allow 5000/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --add-port=5000/tcp --permanent
sudo firewall-cmd --reload
```

### 用户权限管理
```python
# 定期更改默认管理员密码
# 在首次部署后立即修改 宜承账号.json 中的默认密码
{
    "zhanghu": [
        {
            "user": "admin",
            "password": "your_secure_password_here",  # 修改此处
            "role": "admin",
            "tags": ["系统管理员"]
        }
    ]
}
```

## 🔄 反向代理配置

### Nginx配置
```nginx
# /etc/nginx/sites-available/ecommerce-dashboard
server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # 上传文件大小限制
    client_max_body_size 100M;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_read_timeout 300;
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
    }
    
    # 静态文件直接服务
    location /static {
        alias /path/to/app/static;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # 日志配置
    access_log /var/log/nginx/ecommerce_access.log;
    error_log /var/log/nginx/ecommerce_error.log;
}
```

### Apache配置
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    Redirect permanent / https://your-domain.com/
</VirtualHost>

<VirtualHost *:443>
    ServerName your-domain.com
    
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    ProxyPreserveHost On
    ProxyPass / http://127.0.0.1:5000/
    ProxyPassReverse / http://127.0.0.1:5000/
    
    # 上传大小限制
    LimitRequestBody 104857600
</VirtualHost>
```

## 📊 监控和日志

### 系统监控
```python
# 在app.py中添加监控端点
@app.route('/health')
def health_check():
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "database": check_database_health(),
        "disk_usage": get_disk_usage(),
        "memory_usage": get_memory_usage()
    })

@app.route('/metrics')
def metrics():
    return jsonify({
        "active_users": get_active_user_count(),
        "database_size": get_database_sizes(),
        "request_count": get_request_metrics(),
        "error_rate": get_error_rate()
    })
```

### 日志配置
```python
import logging
from logging.handlers import RotatingFileHandler

# 配置日志轮转
if not app.debug:
    file_handler = RotatingFileHandler(
        'logs/app.log', 
        maxBytes=10240000, 
        backupCount=10
    )
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    
    app.logger.setLevel(logging.INFO)
    app.logger.info('应用启动')
```

### 性能监控
```bash
# 安装系统监控工具
pip install psutil

# 监控脚本
#!/bin/bash
# monitor.sh
while true; do
    echo "$(date): CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%, Memory: $(free | grep Mem | awk '{printf "%.2f", $3/$2 * 100.0}')%" >> system_monitor.log
    sleep 300  # 每5分钟记录一次
done
```

## 🗃️ 数据库管理

### 数据库备份
```bash
#!/bin/bash
# backup_databases.sh
BACKUP_DIR="/path/to/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p "$BACKUP_DIR/$DATE"

# 备份所有数据库
cp static/db/*.db "$BACKUP_DIR/$DATE/"

# 压缩备份
tar -czf "$BACKUP_DIR/backup_$DATE.tar.gz" -C "$BACKUP_DIR" "$DATE"

# 清理临时目录
rm -rf "$BACKUP_DIR/$DATE"

# 保留最近7天的备份
find "$BACKUP_DIR" -name "backup_*.tar.gz" -mtime +7 -delete

echo "数据库备份完成: backup_$DATE.tar.gz"
```

### 数据库维护
```sql
-- 定期优化数据库
PRAGMA optimize;
ANALYZE;
VACUUM;

-- 检查数据库完整性
PRAGMA integrity_check;

-- 查看数据库统计信息
.dbinfo
```

### 自动备份 (Crontab)
```bash
# 编辑crontab
crontab -e

# 添加自动备份任务（每日凌晨2点）
0 2 * * * /path/to/backup_databases.sh >> /var/log/backup.log 2>&1

# 每周日凌晨3点进行数据库优化
0 3 * * 0 sqlite3 /path/to/static/db/sale_data.db "VACUUM; ANALYZE;"
```

## 🔧 运维脚本

### 服务管理脚本
```bash
#!/bin/bash
# service_manager.sh

APP_DIR="/path/to/app"
PID_FILE="$APP_DIR/app.pid"
LOG_FILE="$APP_DIR/logs/service.log"

start() {
    if [ -f "$PID_FILE" ]; then
        echo "服务已在运行 (PID: $(cat $PID_FILE))"
        exit 1
    fi
    
    echo "启动服务..."
    cd "$APP_DIR"
    nohup gunicorn -c gunicorn.conf.py app:app > "$LOG_FILE" 2>&1 &
    echo $! > "$PID_FILE"
    echo "服务已启动 (PID: $!)"
}

stop() {
    if [ ! -f "$PID_FILE" ]; then
        echo "服务未运行"
        exit 1
    fi
    
    PID=$(cat "$PID_FILE")
    echo "停止服务 (PID: $PID)..."
    kill "$PID"
    rm -f "$PID_FILE"
    echo "服务已停止"
}

restart() {
    stop
    sleep 3
    start
}

status() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null; then
            echo "服务正在运行 (PID: $PID)"
        else
            echo "PID文件存在但进程不存在"
            rm -f "$PID_FILE"
        fi
    else
        echo "服务未运行"
    fi
}

case "$1" in
    start) start ;;
    stop) stop ;;
    restart) restart ;;
    status) status ;;
    *) echo "用法: $0 {start|stop|restart|status}" ;;
esac
```

### 部署脚本
```bash
#!/bin/bash
# deploy.sh

set -e

echo "开始部署..."

# 备份当前版本
if [ -d "backup" ]; then
    rm -rf backup
fi
mkdir backup
cp -r static/db backup/

# 更新代码
git pull origin main

# 安装依赖
pip install -r requirements.txt

# 运行数据库迁移（如果需要）
python database_migration.py

# 重启服务
./service_manager.sh restart

# 验证部署
sleep 5
curl -f http://localhost:5000/health || {
    echo "部署验证失败，回滚..."
    # 回滚逻辑
    exit 1
}

echo "部署完成！"
```

## 📈 性能优化

### 应用层优化
```python
# 启用响应压缩
from flask_compress import Compress
Compress(app)

# 缓存配置
from flask_caching import Cache
cache = Cache(app, config={'CACHE_TYPE': 'simple'})

@cache.memoize(timeout=300)
def get_dashboard_data():
    # 缓存仪表盘数据5分钟
    pass
```

### 数据库优化
```sql
-- 创建索引提高查询性能
CREATE INDEX idx_sale_data_date ON sale_data(date);
CREATE INDEX idx_sale_data_shop_code ON sale_data(shop_code);
CREATE INDEX idx_sale_data_shop_date ON sale_data(shop_code, date);

-- SPU数据索引
CREATE INDEX idx_spu_data_date ON spu_data(date);
CREATE INDEX idx_spu_data_spu_name ON spu_data(spu_name);
```

### 系统优化
```bash
# 调整系统参数
echo 'net.core.somaxconn = 1024' >> /etc/sysctl.conf
echo 'fs.file-max = 65536' >> /etc/sysctl.conf
sysctl -p

# 优化文件描述符限制
echo '* soft nofile 65536' >> /etc/security/limits.conf
echo '* hard nofile 65536' >> /etc/security/limits.conf
```

## 🚨 灾难恢复

### 数据恢复流程
```bash
# 1. 停止服务
./service_manager.sh stop

# 2. 恢复数据库
tar -xzf backup_20240115_020000.tar.gz
cp backup_20240115_020000/*.db static/db/

# 3. 验证数据完整性
sqlite3 static/db/sale_data.db "PRAGMA integrity_check;"

# 4. 重启服务
./service_manager.sh start

# 5. 验证服务
curl -f http://localhost:5000/health
```

### 应急处理预案
1. **服务无响应**: 重启应用服务
2. **数据库损坏**: 从备份恢复
3. **磁盘空间不足**: 清理日志和临时文件
4. **内存不足**: 重启服务器或增加swap
5. **高CPU使用**: 检查慢查询，优化代码

## 📝 运维检查清单

### 日常检查
- [ ] 检查服务状态
- [ ] 查看错误日志
- [ ] 监控磁盘使用情况
- [ ] 验证数据库连接
- [ ] 检查内存使用

### 周期性任务
- [ ] 数据库备份验证
- [ ] 日志文件轮转
- [ ] 系统更新检查
- [ ] 性能指标分析
- [ ] 安全漏洞扫描

### 月度维护
- [ ] 数据库优化
- [ ] 依赖包更新
- [ ] 备份策略评估
- [ ] 容量规划评估
- [ ] 灾难恢复演练

