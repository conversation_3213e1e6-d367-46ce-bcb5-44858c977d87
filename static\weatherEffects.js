/**
 * 天气效果动画库
 * 提供晴天和星空两种优化的天气背景效果，带有增强的用户交互体验
 */

class WeatherEffects {
    constructor(container) {
        this.container = container;
        this.weatherTypes = ['sunny', 'starry'];
        this.currentWeather = null;
        this.elements = [];
        this.animationFrameId = null;
        this.isInteractive = true;
        this.parallaxEnabled = true;
        this.audioEnabled = false;
        this.mouseMoveHandler = this.handleMouseMove.bind(this);
        this.clickHandler = this.handleClick.bind(this);
    }

    // 初始化天气效果
    init() {
        // 设置容器样式
        this.container.style.position = 'relative';
        this.container.style.overflow = 'hidden';
        
        // 默认设置为晴天效果
        this.setWeather('sunny');
    }

    // 设置天气类型
    setWeather(type) {
        if (!this.weatherTypes.includes(type)) {
            console.error(`不支持的天气类型: ${type}`);
            return;
        }

        // 清除现有效果
        this.clear();

        // 设置当前天气类型
        this.currentWeather = type;

        // 创建新的天气效果
        if (type === 'sunny') {
            this.createSunnyEffect();
        } else if (type === 'starry') {
            this.createStarryEffect();
        }

        // 设置交互性
        if (this.isInteractive) {
            this.setupInteractivity();
        }

        // 开始动画
        this.animate();
    }

    // 清除现有效果
    clear() {
        // 停止动画
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }

        // 移除交互性
        this.removeInteractivity();

        // 移除所有元素
        this.elements.forEach(element => {
            if (element.el && element.el.parentNode) {
                element.el.parentNode.removeChild(element.el);
            }
        });

        // 清空元素数组
        this.elements = [];
    }

    // 创建晴天效果
    createSunnyEffect() {
        const containerWidth = this.container.offsetWidth;
        const containerHeight = this.container.offsetHeight;

        // 创建太阳
        const sunSize = Math.min(containerWidth, containerHeight) * 0.15;
        const sunElement = document.createElement('div');
        sunElement.className = 'weather-sun';
        sunElement.style.cssText = `
            position: absolute;
            width: ${sunSize}px;
            height: ${sunSize}px;
            background: radial-gradient(circle, #ffdb58 0%, #ff9d00 70%);
            border-radius: 50%;
            box-shadow: 0 0 50px #ffdb58;
            top: ${containerHeight * 0.2}px;
            left: ${containerWidth * 0.8}px;
            transform: translate(-50%, -50%);
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            z-index: 1;
        `;
        this.container.appendChild(sunElement);
        this.elements.push({
            type: 'sun',
            el: sunElement,
            x: containerWidth * 0.8,
            y: containerHeight * 0.2,
            size: sunSize,
            baseX: containerWidth * 0.8,
            baseY: containerHeight * 0.2
        });

        // 创建云朵
        const cloudCount = Math.floor(containerWidth / 300) + 1;
        for (let i = 0; i < cloudCount; i++) {
            const cloudSize = Math.random() * 100 + 100;
            const cloudElement = document.createElement('div');
            cloudElement.className = 'weather-cloud';
            
            const x = Math.random() * containerWidth;
            const y = Math.random() * (containerHeight * 0.5);
            const speed = Math.random() * 0.5 + 0.1;
            
            cloudElement.style.cssText = `
                position: absolute;
                width: ${cloudSize}px;
                height: ${cloudSize * 0.6}px;
                background: rgba(255, 255, 255, 0.8);
                border-radius: 50%;
                box-shadow: 
                    ${cloudSize * 0.5}px ${-cloudSize * 0.1}px 0 rgba(255, 255, 255, 0.8),
                    ${cloudSize * 0.7}px ${cloudSize * 0.1}px 0 rgba(255, 255, 255, 0.8),
                    ${cloudSize * 0.3}px ${cloudSize * 0.2}px 0 rgba(255, 255, 255, 0.8);
                top: ${y}px;
                left: ${x}px;
                cursor: pointer;
                transition: transform 0.3s ease;
                z-index: 2;
            `;
            this.container.appendChild(cloudElement);
            this.elements.push({
                type: 'cloud',
                el: cloudElement,
                x: x,
                y: y,
                size: cloudSize,
                speed: speed,
                baseX: x,
                baseY: y
            });
        }

        // 创建鸟
        const birdCount = Math.floor(containerWidth / 500) + 1;
        for (let i = 0; i < birdCount; i++) {
            const birdSize = Math.random() * 20 + 10;
            const birdElement = document.createElement('div');
            birdElement.className = 'weather-bird';
            
            const x = Math.random() * containerWidth;
            const y = Math.random() * (containerHeight * 0.7) + containerHeight * 0.1;
            const speed = Math.random() * 1 + 0.5;
            
            birdElement.style.cssText = `
                position: absolute;
                width: ${birdSize}px;
                height: ${birdSize / 2}px;
                background: transparent;
                top: ${y}px;
                left: ${x}px;
                cursor: pointer;
                z-index: 3;
            `;
            
            // 添加鸟的形状 (V形)
            birdElement.innerHTML = `
                <svg width="${birdSize}" height="${birdSize / 2}" viewBox="0 0 100 50">
                    <path d="M0,25 L50,50 L100,25 L50,0 Z" fill="#333" />
                </svg>
            `;
            
            this.container.appendChild(birdElement);
            this.elements.push({
                type: 'bird',
                el: birdElement,
                x: x,
                y: y,
                size: birdSize,
                speed: speed,
                direction: Math.random() > 0.5 ? 1 : -1,
                baseX: x,
                baseY: y
            });
        }
    }

    // 设置交互性
    setupInteractivity() {
        if (this.parallaxEnabled) {
            this.container.addEventListener('mousemove', this.mouseMoveHandler);
        }
        this.container.addEventListener('click', this.clickHandler);
    }

    // 移除交互性
    removeInteractivity() {
        this.container.removeEventListener('mousemove', this.mouseMoveHandler);
        this.container.removeEventListener('click', this.clickHandler);
    }

    // 处理鼠标移动（视差效果）
    handleMouseMove(event) {
        const containerRect = this.container.getBoundingClientRect();
        const mouseX = event.clientX - containerRect.left;
        const mouseY = event.clientY - containerRect.top;
        
        const containerWidth = this.container.offsetWidth;
        const containerHeight = this.container.offsetHeight;
        
        // 计算鼠标位置相对于容器中心的偏移
        const offsetX = (mouseX / containerWidth - 0.5) * 2;
        const offsetY = (mouseY / containerHeight - 0.5) * 2;
        
        // 为每个元素应用视差效果
        this.elements.forEach(element => {
            let parallaxFactor;
            
            // 根据元素类型设置不同的视差因子
            switch (element.type) {
                case 'sun':
                case 'moon':
                    parallaxFactor = 20;
                    break;
                case 'cloud':
                    parallaxFactor = 10 + (element.size / 50);
                    break;
                case 'star':
                    parallaxFactor = 5 + (element.size * 2);
                    break;
                case 'bird':
                    parallaxFactor = 30;
                    break;
                default:
                    parallaxFactor = 10;
            }
            
            // 应用视差位移
            const translateX = offsetX * parallaxFactor;
            const translateY = offsetY * parallaxFactor;
            
            element.el.style.transform = `translate(${translateX}px, ${translateY}px)`;
        });
    }

    // 处理点击事件
    handleClick(event) {
        // 获取点击的元素
        const target = event.target;
        
        // 查找被点击的天气元素
        for (const element of this.elements) {
            if (element.el.contains(target)) {
                // 根据元素类型处理点击事件
                switch (element.type) {
                    case 'sun':
                        this.handleSunClick(element);
                        break;
                    case 'cloud':
                        this.handleCloudClick(element);
                        break;
                    case 'bird':
                        this.handleBirdClick(element);
                        break;
                    case 'star':
                        this.handleStarClick(element);
                        break;
                    case 'moon':
                        this.handleMoonClick(element);
                        break;
                }
                
                // 播放音效
                if (this.audioEnabled) {
                    this.playAudioEffect(element.type);
                }
                
                break;
            }
        }
    }

    // 处理太阳点击
    handleSunClick(sunElement) {
        // 创建脉动动画
        const pulseAnimation = sunElement.el.animate([
            { boxShadow: '0 0 50px #ffdb58', transform: 'scale(1)' },
            { boxShadow: '0 0 100px #ffdb58', transform: 'scale(1.2)' },
            { boxShadow: '0 0 50px #ffdb58', transform: 'scale(1)' }
        ], {
            duration: 1000,
            easing: 'ease-in-out'
        });
        
        // 创建光线效果
        const rays = Math.floor(Math.random() * 3) + 3;
        for (let i = 0; i < rays; i++) {
            const angle = Math.random() * Math.PI * 2;
            const distance = sunElement.size * (Math.random() * 2 + 2);
            const rayX = sunElement.x + Math.cos(angle) * distance;
            const rayY = sunElement.y + Math.sin(angle) * distance;
            
            this.createSunRay(sunElement.x, sunElement.y, rayX, rayY);
        }
    }

    // 处理云朵点击
    handleCloudClick(cloudElement) {
        // 创建抖动动画
        const originalTransform = cloudElement.el.style.transform;
        
        const shakeAnimation = cloudElement.el.animate([
            { transform: `${originalTransform} translate(-5px, 0)` },
            { transform: `${originalTransform} translate(5px, 0)` },
            { transform: `${originalTransform} translate(-3px, 0)` },
            { transform: `${originalTransform} translate(3px, 0)` },
            { transform: `${originalTransform} translate(-1px, 0)` },
            { transform: `${originalTransform} translate(1px, 0)` },
            { transform: originalTransform }
        ], {
            duration: 500,
            easing: 'ease-in-out'
        });
        
        // 随机创建雨滴
        const raindrops = Math.floor(Math.random() * 5) + 3;
        for (let i = 0; i < raindrops; i++) {
            const offsetX = (Math.random() - 0.5) * cloudElement.size;
            const startY = cloudElement.y + cloudElement.size * 0.3;
            
            this.createRaindrop(cloudElement.x + offsetX, startY);
        }
    }

    // 处理鸟点击
    handleBirdClick(birdElement) {
        // 创建飞行动画
        const flyAnimation = birdElement.el.animate([
            { transform: 'translateY(0)' },
            { transform: 'translateY(-20px)' },
            { transform: 'translateY(0)' }
        ], {
            duration: 500,
            easing: 'ease-in-out'
        });
        
        // 改变鸟的方向
        birdElement.direction *= -1;
        birdElement.el.style.transform = `scaleX(${birdElement.direction})`;
    }

    // 处理星星点击
    handleStarClick(starElement) {
        // 创建闪烁动画
        const twinkleAnimation = starElement.el.animate([
            { opacity: starElement.opacity, transform: 'scale(1)' },
            { opacity: 1, transform: 'scale(1.5)' },
            { opacity: starElement.opacity, transform: 'scale(1)' }
        ], {
            duration: 1000,
            easing: 'ease-in-out'
        });
        
        // 创建星星轨迹
        const trail = document.createElement('div');
        trail.style.cssText = `
            position: absolute;
            top: ${starElement.y}px;
            left: ${starElement.x}px;
            width: ${starElement.size * 3}px;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
            transform: rotate(${Math.random() * 360}deg);
            transform-origin: center;
            z-index: 1;
        `;
        this.container.appendChild(trail);
        
        // 淡出并移除轨迹
        setTimeout(() => {
            trail.style.transition = 'opacity 1s';
            trail.style.opacity = 0;
            setTimeout(() => trail.remove(), 1000);
        }, 100);
    }

    // 处理月亮点击
    handleMoonClick(moonElement) {
        // 创建脉动动画
        const pulseAnimation = moonElement.el.animate([
            { boxShadow: '0 0 20px rgba(255, 255, 255, 0.6)', transform: 'scale(1)' },
            { boxShadow: '0 0 40px rgba(255, 255, 255, 0.8)', transform: 'scale(1.1)' },
            { boxShadow: '0 0 20px rgba(255, 255, 255, 0.6)', transform: 'scale(1)' }
        ], {
            duration: 1000,
            easing: 'ease-in-out'
        });
        
        // 创建月光效果
        const moonlightCount = Math.floor(Math.random() * 3) + 2;
        for (let i = 0; i < moonlightCount; i++) {
            const angle = Math.random() * Math.PI;
            const distance = moonElement.size * (Math.random() + 1.5);
            const rayX = moonElement.x + Math.cos(angle) * distance;
            const rayY = moonElement.y + Math.sin(angle + Math.PI) * distance;
            
            this.createMoonlight(moonElement.x, moonElement.y, rayX, rayY);
        }
    }

    // 播放音效
    playAudioEffect(elementType) {
        // 这里可以根据元素类型播放不同的音效
        // 需要预先加载音效文件
        console.log(`播放${elementType}的音效`);
    }

    // 创建星空效果
    createStarryEffect() {
        const containerWidth = this.container.offsetWidth;
        const containerHeight = this.container.offsetHeight;

        // 创建月亮
        const moonSize = Math.min(containerWidth, containerHeight) * 0.12;
        const moonElement = document.createElement('div');
        moonElement.className = 'weather-moon';
        moonElement.style.cssText = `
            position: absolute;
            width: ${moonSize}px;
            height: ${moonSize}px;
            background: radial-gradient(circle, #ffffff 0%, #f4f4f4 50%, #e0e0e0 100%);
            border-radius: 50%;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.6);
            top: ${containerHeight * 0.2}px;
            left: ${containerWidth * 0.8}px;
            transform: translate(-50%, -50%);
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            z-index: 1;
        `;
        this.container.appendChild(moonElement);
        this.elements.push({
            type: 'moon',
            el: moonElement,
            x: containerWidth * 0.8,
            y: containerHeight * 0.2,
            size: moonSize,
            baseX: containerWidth * 0.8,
            baseY: containerHeight * 0.2
        });

        // 创建星星
        const starCount = Math.floor((containerWidth * containerHeight) / 10000) + 20;
        for (let i = 0; i < starCount; i++) {
            const starSize = Math.random() * 3 + 1;
            const starElement = document.createElement('div');
            starElement.className = 'weather-star';
            
            const x = Math.random() * containerWidth;
            const y = Math.random() * containerHeight * 0.8;
            const opacity = Math.random() * 0.5 + 0.3;
            const twinkleSpeed = Math.random() * 3 + 1;
            
            starElement.style.cssText = `
                position: absolute;
                width: ${starSize}px;
                height: ${starSize}px;
                background: #ffffff;
                border-radius: 50%;
                box-shadow: 0 0 ${starSize * 2}px rgba(255, 255, 255, ${opacity});
                top: ${y}px;
                left: ${x}px;
                opacity: ${opacity};
                cursor: pointer;
                z-index: 1;
            `;
            this.container.appendChild(starElement);
            this.elements.push({
                type: 'star',
                el: starElement,
                x: x,
                y: y,
                size: starSize,
                opacity: opacity,
                twinkleSpeed: twinkleSpeed,
                twinkleOffset: Math.random() * Math.PI * 2,
                baseX: x,
                baseY: y
            });
        }

        // 创建流星
        this.createShootingStar();
        
        // 每隔一段时间创建一个新的流星
        setInterval(() => {
            if (this.currentWeather === 'starry' && Math.random() < 0.3) {
                this.createShootingStar();
            }
        }, 5000);
    }

    // 创建流星
    createShootingStar() {
        const containerWidth = this.container.offsetWidth;
        const containerHeight = this.container.offsetHeight;
        
        const startX = Math.random() * containerWidth;
        const startY = Math.random() * containerHeight * 0.3;
        const angle = Math.PI / 4 + (Math.random() * Math.PI / 4);
        const length = Math.random() * 150 + 100;
        const duration = Math.random() * 1000 + 1000;
        
        const shootingStarElement = document.createElement('div');
        shootingStarElement.className = 'weather-shooting-star';
        shootingStarElement.style.cssText = `
            position: absolute;
            top: ${startY}px;
            left: ${startX}px;
            width: ${length}px;
            height: 2px;
            background: linear-gradient(90deg, transparent, #ffffff, transparent);
            transform: rotate(${angle}rad);
            transform-origin: left center;
            opacity: 0;
            z-index: 2;
        `;
        
        this.container.appendChild(shootingStarElement);
        
        // 创建流星动画
        const animation = shootingStarElement.animate([
            { opacity: 0, transform: `rotate(${angle}rad) translateX(0)` },
            { opacity: 1, transform: `rotate(${angle}rad) translateX(30px)` },
            { opacity: 0, transform: `rotate(${angle}rad) translateX(${containerWidth}px)` }
        ], {
            duration: duration,
            easing: 'ease-out'
        });
        
        animation.onfinish = () => {
            shootingStarElement.remove();
        };
    }

    // 创建太阳光线
    createSunRay(startX, startY, endX, endY) {
        const rayElement = document.createElement('div');
        rayElement.className = 'weather-sun-ray';
        
        // 计算角度和距离
        const angle = Math.atan2(endY - startY, endX - startX);
        const distance = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));
        
        rayElement.style.cssText = `
            position: absolute;
            top: ${startY}px;
            left: ${startX}px;
            width: ${distance}px;
            height: 2px;
            background: linear-gradient(90deg, rgba(255, 219, 88, 0.8), transparent);
            transform: rotate(${angle}rad);
            transform-origin: left center;
            opacity: 0;
            z-index: 0;
        `;
        
        this.container.appendChild(rayElement);
        
        // 创建光线动画
        const animation = rayElement.animate([
            { opacity: 0 },
            { opacity: 0.8 },
            { opacity: 0 }
        ], {
            duration: 1000,
            easing: 'ease-in-out'
        });
        
        animation.onfinish = () => {
            rayElement.remove();
        };
    }

    // 创建雨滴
    createRaindrop(startX, startY) {
        const raindropElement = document.createElement('div');
        raindropElement.className = 'weather-raindrop';
        
        raindropElement.style.cssText = `
            position: absolute;
            top: ${startY}px;
            left: ${startX}px;
            width: 2px;
            height: 10px;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), rgba(200, 200, 255, 0.8));
            border-radius: 50%;
            opacity: 0.7;
            z-index: 2;
        `;
        
        this.container.appendChild(raindropElement);
        
        // 创建下落动画
        const endY = this.container.offsetHeight;
        const duration = Math.random() * 1000 + 1000;
        
        const animation = raindropElement.animate([
            { transform: 'translateY(0) scaleY(1)', opacity: 0.7 },
            { transform: `translateY(${endY - startY}px) scaleY(1.5)`, opacity: 0 }
        ], {
            duration: duration,
            easing: 'ease-in'
        });
        
        animation.onfinish = () => {
            raindropElement.remove();
        };
    }

    // 创建月光
    createMoonlight(startX, startY, endX, endY) {
        const moonlightElement = document.createElement('div');
        moonlightElement.className = 'weather-moonlight';
        
        // 计算角度和距离
        const angle = Math.atan2(endY - startY, endX - startX);
        const distance = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));
        
        moonlightElement.style.cssText = `
            position: absolute;
            top: ${startY}px;
            left: ${startX}px;
            width: ${distance}px;
            height: 1px;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.6), transparent);
            transform: rotate(${angle}rad);
            transform-origin: left center;
            opacity: 0;
            z-index: 0;
        `;
        
        this.container.appendChild(moonlightElement);
        
        // 创建月光动画
        const animation = moonlightElement.animate([
            { opacity: 0 },
            { opacity: 0.6 },
            { opacity: 0 }
        ], {
            duration: 1500,
            easing: 'ease-in-out'
        });
        
        animation.onfinish = () => {
            moonlightElement.remove();
        };
    }

    // 动画循环
    animate() {
        const containerWidth = this.container.offsetWidth;
        const containerHeight = this.container.offsetHeight;
        const time = Date.now() / 1000;
        
        // 更新每个元素的位置
        this.elements.forEach(element => {
            switch (element.type) {
                case 'cloud':
                    // 云朵水平移动
                    element.x += element.speed;
                    if (element.x > containerWidth + element.size) {
                        element.x = -element.size;
                    }
                    element.el.style.left = `${element.x}px`;
                    break;
                    
                case 'bird':
                    // 鸟水平移动，带有轻微的垂直波动
                    element.x += element.speed * element.direction;
                    element.y = element.baseY + Math.sin(time * 2 + element.baseX) * 10;
                    
                    // 当鸟飞出屏幕时，从另一侧飞入
                    if (element.x > containerWidth + element.size) {
                        element.x = -element.size;
                    } else if (element.x < -element.size) {
                        element.x = containerWidth + element.size;
                    }
                    
                    element.el.style.left = `${element.x}px`;
                    element.el.style.top = `${element.y}px`;
                    
                    // 设置鸟的方向
                    element.el.style.transform = `scaleX(${element.direction})`;
                    break;
                    
                case 'star':
                    // 星星闪烁
                    const opacity = element.opacity + Math.sin(time * element.twinkleSpeed + element.twinkleOffset) * 0.2;
                    element.el.style.opacity = opacity;
                    break;
            }
        });
        
        // 继续动画循环
        this.animationFrameId = requestAnimationFrame(() => this.animate());
    }
}

// 初始化天气效果的辅助函数
function initWeatherEffects(options = {}) {
    const container = options.container || document.body;
    const weatherType = options.type || 'sunny';
    const interactive = options.interactive !== undefined ? options.interactive : true;
    const parallax = options.parallax !== undefined ? options.parallax : true;
    const audio = options.audio !== undefined ? options.audio : false;
    
    const effects = new WeatherEffects(container);
    effects.isInteractive = interactive;
    effects.parallaxEnabled = parallax;
    effects.audioEnabled = audio;
    
    effects.init();
    effects.setWeather(weatherType);
    
    return effects;
}

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { WeatherEffects, initWeatherEffects };
} else {
    window.WeatherEffects = WeatherEffects;
    window.initWeatherEffects = initWeatherEffects;
} 