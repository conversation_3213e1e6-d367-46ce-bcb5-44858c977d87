// 声明全局变量
window.timestamp = window.timestamp || null;
window.salesTrendChart = window.salesTrendChart || null;
window.topShopsChart = window.topShopsChart || null;

// 检查登录状态
function checkLoginStatus() {
    const cookies = document.cookie.split(';');
    const loginAuth = cookies.find(cookie => cookie.trim().startsWith('loginAuth='));
    if (!loginAuth || loginAuth.split('=')[1].trim() !== 'true') {
        window.location.href = 'login.html';
        return false;
    }
    return true;
}

// 使用原生HTML和CSS绘制数据面板
async function generateDashboard() {
    // 添加时间戳参数避免缓存
    window.timestamp = new Date().getTime();
    const container = document.querySelector('.container');
    if (!container) return;

    // 添加Chart.js库<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
    if (!document.getElementById('chartjs-script')) {
        const chartScript = document.createElement('script');
        chartScript.src = 'https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js';
        chartScript.id = 'chartjs-script';
        document.head.appendChild(chartScript);

        // 等待Chart.js加载完成
        await new Promise((resolve) => {
            chartScript.onload = resolve;
        });
    }

    // 清空现有内容并添加标题
    container.innerHTML = `
        <div class="dashboard-header">
            <div class="dashboard-title">
                <h1>宜承-数据概览</h1>
                <div class="update-time">更新时间：<span>--</span></div>
            </div>
            <div class="dashboard-actions">
                <button id="refreshBtn" class="refresh-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"/>
                    </svg>
                    刷新数据
                </button>
            </div>
        </div>
        <div class="dashboard-container">
            <div class="data-cards">
                <div class="data-card total-sales">
                    <div class="card-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
                            <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
                        </svg>
                    </div>
                    <div class="card-content">
                        <h3>昨日总销售额</h3>
                        <p class="value">加载中...</p>
                        <p class="trend">--</p>
                    </div>
                </div>
                <div class="data-card yesterday-sales">
                    <div class="card-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                    </div>
                    <div class="card-content">
                        <h3>昨日净销售额</h3>
                        <p class="value">加载中...</p>
                        <p class="trend">--</p>
                    </div>
                </div>
                <div class="data-card monthly-sales">
                    <div class="card-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                            <line x1="16" y1="2" x2="16" y2="6"></line>
                            <line x1="8" y1="2" x2="8" y2="6"></line>
                            <line x1="3" y1="10" x2="21" y2="10"></line>
                        </svg>
                    </div>
                    <div class="card-content">
                        <h3>近一月销售额</h3>
                        <p class="value">加载中...</p>
                        <p class="trend">--</p>
                    </div>
                </div>
                <div class="data-card monthly-net-sales">
                    <div class="card-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                            <line x1="16" y1="2" x2="16" y2="6"></line>
                            <line x1="8" y1="2" x2="8" y2="6"></line>
                            <line x1="3" y1="10" x2="21" y2="10"></line>
                        </svg>
                    </div>
                    <div class="card-content">
                        <h3>近一月净销售额</h3>
                        <p class="value">加载中...</p>
                        <p class="trend">--</p>
                    </div>
                </div>
                <div class="data-card promotion-expense">
                    <div class="card-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="12" y1="8" x2="12" y2="16"></line>
                            <line x1="8" y1="12" x2="16" y2="12"></line>
                        </svg>
                    </div>
                    <div class="card-content">
                        <h3>昨日推广支出</h3>
                        <p class="value">加载中...</p>
                        <p class="trend">--</p>
                    </div>
                </div>
                <div class="data-card roi">
                    <div class="card-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                        </svg>
                    </div>
                    <div class="card-content">
                        <h3>昨日全店ROI</h3>
                        <p class="value">加载中...</p>
                        <p class="trend">--</p>
                    </div>
                </div>
                <div class="data-card total-orders">
                    <div class="card-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path>
                            <line x1="3" y1="6" x2="21" y2="6"></line>
                            <path d="M16 10a4 4 0 0 1-8 0"></path>
                        </svg>
                    </div>
                    <div class="card-content">
                        <h3>昨日总订单数</h3>
                        <p class="value">加载中...</p>
                        <p class="trend">--</p>
                    </div>
                </div>  
                <div class="data-card net-orders">
                    <div class="card-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <polyline points="22 4 12 14.01 9 11.01"></polyline>
                        </svg>
                    </div>
                    <div class="card-content">
                        <h3>昨日净订单数</h3>
                        <p class="value">加载中...</p>
                        <p class="trend">--</p>
                    </div>
                </div>
                <div class="data-card monthly-orders">
                    <div class="card-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                        </svg>
                    </div>
                    <div class="card-content">
                        <h3>近一月订单数</h3>
                        <p class="value">加载中...</p>
                        <p class="trend">--</p>
                    </div>
                </div>
                <div class="data-card monthly-net-orders">
                    <div class="card-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <polyline points="22 4 12 14.01 9 11.01"></polyline>
                        </svg>
                    </div>
                    <div class="card-content">
                        <h3>近一月净订单数</h3>
                        <p class="value">加载中...</p>
                        <p class="trend">--</p>
                    </div>
                </div>
                <div class="data-card avg-price">
                    <div class="card-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="12" y1="1" x2="12" y2="23"></line>
                            <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                        </svg>
                    </div>
                    <div class="card-content">
                        <h3>昨日平均客单价</h3>
                        <p class="value">加载中...</p>
                        <p class="trend">--</p>
                    </div>
                </div>
                <div class="data-card conversion">
                    <div class="card-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                            <polyline points="17 6 23 6 23 12"></polyline>
                        </svg>
                    </div>
                    <div class="card-content">
                        <h3>昨日转化率</h3>
                        <p class="value">加载中...</p>
                        <p class="trend">--</p>
                    </div>
                </div>
                <div class="data-card refund-rate">
                    <div class="card-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                            <polyline points="9 22 9 12 15 12 15 22"></polyline>
                        </svg>
                    </div>
                    <div class="card-content">
                        <h3>昨日退款率</h3>
                        <p class="value">加载中...</p>
                        <p class="trend">--</p>
                    </div>
                </div>
                <div class="data-card active-shops">
                    <div class="card-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                            <polyline points="9 22 9 12 15 12 15 22"></polyline>
                        </svg>
                    </div>
                    <div class="card-content">
                        <h3>昨日活跃店铺数</h3>
                        <p class="value">加载中...</p>
                        <p class="trend">--</p>
                    </div>
                </div>
            </div>

            <div class="charts-container">
                <div class="chart-wrapper">
                    <div class="chart-header">
                        <h3 class="chart-title">销售趋势</h3>
                        <div class="chart-actions">
                            <button class="chart-action" data-days="7">7天</button>
                            <button class="chart-action" data-days="15">15天</button>
                            <button class="chart-action active" data-days="30">30天</button>
                        </div>
                    </div>
                    <div id="salesTrendChart" class="chart-container">
                        <canvas id="salesTrendCanvas"></canvas>
                    </div>
                </div>
                <div class="chart-wrapper">
                    <div class="chart-header">
                        <h3 class="chart-title">TOP5店铺销售额</h3>
                    </div>
                    <div id="topShopsChart" class="chart-container">
                        <canvas id="topShopsCanvas"></canvas>
                    </div>
                </div>
                </div>
                <div class="chart-wrapper full-width">
                    <div class="chart-header">
                        <h3 class="chart-title">近30天销售情况</h3>
                        <div class="chart-actions">
                            <button class="chart-action table-action active" data-view="table">表格视图</button>
                        </div>
                    </div>
                    <div id="salesTableContainer" class="chart-container">
                        <!-- 销售数据表格将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加样式
    addDashboardStyles();

    // 初始化数据
    initializeData();

    // 添加刷新按钮事件
    document.getElementById('refreshBtn').addEventListener('click', function() {
        // 添加动画效果
        this.classList.add('refreshing');
        // 重新获取数据
        initializeData().finally(() => {
            // 移除动画效果
            setTimeout(() => {
                this.classList.remove('refreshing');
            }, 500);
        });
    });

    // 添加时间范围选择按钮事件
    document.querySelectorAll('.chart-action').forEach(button => {
        button.addEventListener('click', function() {
            // 移除所有按钮的激活状态
            document.querySelectorAll('.chart-action').forEach(btn => btn.classList.remove('active'));
            // 添加当前按钮的激活状态
            this.classList.add('active');
            // 获取所选天数
            const days = this.getAttribute('data-days');
            // 重新获取数据
            initializeData(days);
        });
    });

    // 添加表格视图切换按钮事件
    document.querySelectorAll('.table-action').forEach(button => {
        button.addEventListener('click', function() {
            // 移除所有表格按钮的激活状态
            document.querySelectorAll('.table-action').forEach(btn => btn.classList.remove('active'));
            // 添加当前按钮的激活状态
            this.classList.add('active');
            // 重新绘制销售数据表格
            drawSalesTable();
        });
    });
}

function addDashboardStyles() {
    const styleElement = document.createElement('style');
    styleElement.textContent = `
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .dashboard-title h1 {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .update-time {
            font-size: 14px;
            color: #7f8c8d;
        }

        .dashboard-actions {
            display: flex;
            gap: 10px;
        }

        .refresh-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            color: #333;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .refresh-btn:hover {
            background: #f0f2f5;
            border-color: #ccc;
        }

        .refresh-btn.refreshing svg {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .dashboard-container {
            padding: 0;
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .data-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .data-card {
            background: white;
            padding: 24px;
            border-radius: 14px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.04);
            transition: all 0.3s ease;
            display: flex;
            align-items: flex-start;
            gap: 16px;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .data-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.05);
            border-color: rgba(0,0,0,0.1);
        }

        .card-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .card-content {
            flex: 1;
        }

        .card-content h3 {
            color: #64748b;
            font-size: 15px;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .data-card .value {
            font-size: 24px;
            font-weight: 600;
            margin: 8px 0;
            color: #1e293b;
        }

        .data-card .trend {
            font-size: 14px;
            margin: 0;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .trend.positive::before,
        .trend.negative::before {
            content: '';
            display: inline-block;
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            position: relative;
            top: -1px;
        }

        .trend.positive::before {
            border-bottom: 6px solid #4caf50;
        }

        .trend.negative::before {
            border-top: 6px solid #f44336;
        }

        .trend.positive {
            color: #4caf50;
        }

        .trend.negative {
            color: #f44336;
        }

        .total-sales .card-icon { background: linear-gradient(135deg, #4f80f7 0%, #2563eb 100%); }
        .total-orders .card-icon { background: linear-gradient(135deg, #9b6dff 0%, #7c3aed 100%); }
        .avg-price .card-icon { background: linear-gradient(135deg, #14cc9e 0%, #059669 100%); }
        .conversion .card-icon { background: linear-gradient(135deg, #ffb84d 0%, #d97706 100%); }
        .refund-rate .card-icon { background: linear-gradient(135deg, #ff6b6b 0%, #dc2626 100%); }
        .old-buyer .card-icon { background: linear-gradient(135deg, #7b7eff 0%, #4f46e5 100%); }
        .active-shops .card-icon { background: linear-gradient(135deg, #ff73b9 0%, #db2777 100%); }
        .promotion-expense .card-icon { background: linear-gradient(135deg, #1cd4bb 0%, #0d9488 100%); }
        .monthly-sales .card-icon { background: linear-gradient(135deg, #34d399 0%, #16a34a 100%); }
        .monthly-net-sales .card-icon { background: linear-gradient(135deg, #22d3ee 0%, #0891b2 100%); }
        .yesterday-sales .card-icon { background: linear-gradient(135deg, #93e2ff 0%, #38bdf8 100%); }
        .monthly-orders .card-icon { background: linear-gradient(135deg, #c4b5fd 0%, #8b5cf6 100%); }
        .monthly-net-orders .card-icon { background: linear-gradient(135deg, #f9a8d4 0%, #ec4899 100%); }
        .roi .card-icon { background: linear-gradient(135deg, #10b981 0%, #047857 100%); }
        .net-orders .card-icon { background: linear-gradient(135deg, #60a5fa 0%, #2563eb 100%); }

        .charts-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
        }

        .chart-wrapper.full-width {
            grid-column: 1 / -1;
        }

        .chart-wrapper {
            background: white;
            border-radius: 14px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.04);
            border: 1px solid rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-actions {
            display: flex;
            gap: 8px;
        }

        .chart-action {
            padding: 6px 12px;
            background: #f1f5f9;
            border: none;
            border-radius: 6px;
            color: #64748b;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .chart-action:hover {
            background: #e2e8f0;
            color: #334155;
        }

        .chart-action.active {
            background: #334155;
            color: white;
        }

        .chart-title {
            font-size: 18px;
            color: #1e293b;
            margin: 0;
            font-weight: 600;
        }

        .chart-container {
            flex: 1;
            position: relative;
            height: 300px;
            min-height: 300px;
        }

        .table-container {
            width: 100%;
            overflow-x: auto;
            margin-top: 10px;
        }

        .data-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .data-table th,
        .data-table td {
            padding: 14px;
            text-align: left;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        .data-table tr:hover td {
            background-color: #f8fafc;
        }

        .data-table th {
            background-color: #f8fafc;
            font-weight: 600;
            color: #64748b;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table td {
            border-bottom: 1px solid #e2e8f0;
            color: #334155;
        }

        .data-bar {
            height: 8px;
            background: linear-gradient(90deg, #3b82f6 var(--percent), #e2e8f0 var(--percent));
            border-radius: 4px;
            margin: 8px 0;
        }

        .grid-container {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 8px;
            padding: 20px 0;
        }

        .hour-block {
            aspect-ratio: 1;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            color: white;
            background-color: var(--intensity);
            font-weight: 500;
            transition: all 0.2s ease;
            cursor: default;
            position: relative;
        }

        .hour-block:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
            z-index: 10;
        }

        @media (max-width: 1200px) {
            .charts-container {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .data-cards {
                grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            }

            .dashboard-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .chart-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    `;
    document.head.appendChild(styleElement);

}

async function initializeData(days = 30) {
    try {
        // 从API获取数据，添加时间戳和天数参数避免缓存
        const response = await fetch(`/api/dashboard/trend?days=${days}&t=${window.timestamp}`);
        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message || '获取数据失败');
        }

        const data = result.data;
        
        // 存储数据到全局变量，供其他函数使用
        window.dashboardData = data;

        // 更新数据卡片
        const cards = document.querySelectorAll('.data-card');

        // 更新时间显示
        const updateTimeSpan = document.querySelector('.update-time span');
        if (updateTimeSpan) {
            updateTimeSpan.textContent = data.updateTime || new Date().toLocaleString();
        }

        cards.forEach(card => {
            const type = card.classList[1];
            const valueElement = card.querySelector('.value');
            const trendElement = card.querySelector('.trend');

            let overview;
            switch(type) {
                case 'total-sales':
                    overview = data.overview.totalSales;
                    valueElement.textContent = `¥${overview.value.toLocaleString()}`;
                    break;
                case 'monthly-sales':
                    // 近一月销售额可以从销售趋势数据中计算得出
                    if (data.salesTrend && data.salesTrend.values) {
                        // 计算近一月销售总额（不减去退款金额）
                        const monthlySales = data.salesTrend.values.reduce((sum, curr) => sum + curr, 0);
                        valueElement.textContent = `¥${monthlySales.toLocaleString()}`;

                        // 计算环比增长
                        if (data.overview.totalSales) {
                            const trend = ((monthlySales / data.overview.totalSales.value) * 100 - 100).toFixed(1);
                            trendElement.textContent = `${trend >= 0 ? '+' : ''}${trend}%`;
                            trendElement.className = `trend ${trend >= 0 ? 'positive' : 'negative'}`;
                        }
                    } else {
                        valueElement.textContent = `暂无数据`;
                    }
                    break;
                case 'monthly-net-sales':
                    // 计算近一月净销售额（销售额减去退款金额）
                    if (data.salesTrend && data.salesTrend.values) {
                        let monthlySales = data.salesTrend.values.reduce((sum, curr) => sum + curr, 0);
                        let monthlyRefunds = 0;

                        // 获取退款数据
                        if (data.salesTrend.refundValues) {
                            monthlyRefunds = data.salesTrend.refundValues.reduce((sum, curr) => sum + curr, 0);
                        } else if (data.overview.refundRate && data.overview.refundRate.value) {
                            // 如果没有具体退款数据但有退款率，使用退款率计算
                            monthlyRefunds = monthlySales * (data.overview.refundRate.value / 100);
                        }

                        // 计算净销售额
                        const monthlyNetSales = monthlySales - monthlyRefunds;
                        valueElement.textContent = `¥${monthlyNetSales.toLocaleString()}`;

                        // 计算趋势
                        if (data.overview.totalSales && data.overview.refundRate) {
                            const totalNetSales = data.overview.totalSales.value * (1 - data.overview.refundRate.value / 100);
                            const trend = ((monthlyNetSales / totalNetSales) * 100 - 100).toFixed(1);
                            trendElement.textContent = `${trend >= 0 ? '+' : ''}${trend}%`;
                            trendElement.className = `trend ${trend >= 0 ? 'positive' : 'negative'}`;
                        }
                    } else {
                        valueElement.textContent = `暂无数据`;
                    }
                    break;
                case 'yesterday-sales':
                    // 昨日净销售额（销售额减去退款金额）
                    if (data.overview.netYesterdaySales) {
                        // 如果API直接提供了净销售额，直接使用
                        overview = data.overview.netYesterdaySales;
                        valueElement.textContent = `¥${overview.value.toLocaleString()}`;
                    } else if (data.overview.yesterdaySales && data.overview.yesterdayRefunds) {
                        // 如果API分别提供了销售额和退款额，手动计算净销售额
                        const netSales = data.overview.yesterdaySales.value - data.overview.yesterdayRefunds.value;
                        valueElement.textContent = `¥${netSales.toLocaleString()}`;

                        // 计算趋势（如果有前一天的数据）
                        if (data.overview.dayBeforeYesterdaySales && data.overview.dayBeforeYesterdayRefunds) {
                            const prevNetSales = data.overview.dayBeforeYesterdaySales.value -
                                                data.overview.dayBeforeYesterdayRefunds.value;
                            if (prevNetSales > 0) {
                                const trend = ((netSales / prevNetSales) * 100 - 100).toFixed(1);
                                trendElement.textContent = `${trend >= 0 ? '+' : ''}${trend}%`;
                                trendElement.className = `trend ${trend >= 0 ? 'positive' : 'negative'}`;
                            }
                        }
                    } else if (data.overview.totalSales && data.overview.totalSales.value) {
                        // 如果有总销售额数据，可以使用总销售额减去退款金额
                        let netSales = data.overview.totalSales.value;

                        // 如果有退款率数据，使用退款率计算退款金额
                        if (data.overview.refundRate && data.overview.refundRate.value) {
                            const refundAmount = netSales * (data.overview.refundRate.value / 100);
                            netSales = netSales - refundAmount;
                        }

                        valueElement.textContent = `¥${netSales.toLocaleString()}`;

                        // 如果有趋势数据，显示趋势
                        if (data.overview.totalSales.trend) {
                            const trend = data.overview.totalSales.trend;
                            trendElement.textContent = `${trend >= 0 ? '+' : ''}${trend}%`;
                            trendElement.className = `trend ${trend >= 0 ? 'positive' : 'negative'}`;
                        }
                    } else if (data.salesTrend && data.salesTrend.values && data.salesTrend.values.length > 0) {
                        // 如果没有特定的昨日销售数据，尝试从销售趋势数据计算
                        let yesterdaySales = 0;
                        let yesterdayRefunds = 0;

                        // 获取昨日销售总额
                        yesterdaySales = data.salesTrend.values[data.salesTrend.values.length - 1];

                        // 获取昨日退款金额（如果有）
                        if (data.salesTrend.refundValues && data.salesTrend.refundValues.length > 0) {
                            yesterdayRefunds = data.salesTrend.refundValues[data.salesTrend.refundValues.length - 1];
                        } else if (data.overview.refundRate && data.overview.refundRate.value) {
                            // 如果没有具体退款数据但有退款率，使用退款率计算
                            yesterdayRefunds = yesterdaySales * (data.overview.refundRate.value / 100);
                        }

                        // 计算净销售额
                        const netYesterdaySales = yesterdaySales - yesterdayRefunds;
                        valueElement.textContent = `¥${netYesterdaySales.toLocaleString()}`;

                        // 计算环比增长
                        if (data.salesTrend.values.length > 1) {
                            // 获取前一天销售总额
                            const dayBeforeYesterdaySales = data.salesTrend.values[data.salesTrend.values.length - 2];

                            // 获取前一天退款金额（如果有）
                            let dayBeforeYesterdayRefunds = 0;
                            if (data.salesTrend.refundValues && data.salesTrend.refundValues.length > 1) {
                                dayBeforeYesterdayRefunds = data.salesTrend.refundValues[data.salesTrend.refundValues.length - 2];
                            } else if (data.overview.refundRate && data.overview.refundRate.value) {
                                // 如果没有具体退款数据但有退款率，使用退款率计算
                                dayBeforeYesterdayRefunds = dayBeforeYesterdaySales * (data.overview.refundRate.value / 100);
                            }

                            // 计算前一天净销售额
                            const prevNetSales = dayBeforeYesterdaySales - dayBeforeYesterdayRefunds;

                            if (prevNetSales > 0) {
                                const trend = ((netYesterdaySales / prevNetSales) * 100 - 100).toFixed(1);
                                trendElement.textContent = `${trend >= 0 ? '+' : ''}${trend}%`;
                                trendElement.className = `trend ${trend >= 0 ? 'positive' : 'negative'}`;
                            }
                        }
                    } else {
                        valueElement.textContent = `暂无数据`;
                    }
                    break;
                case 'promotion-expense':
                    overview = data.overview.promotionExpense;
                    valueElement.textContent = `¥${overview.value.toLocaleString()}`;

                    // 显示推广支出的趋势数据
                    if (overview.trend !== undefined) {
                        const trend = overview.trend;
                        trendElement.textContent = `${trend >= 0 ? '+' : ''}${trend}%`;
                        trendElement.className = `trend ${trend >= 0 ? 'negative' : 'positive'}`; // 注意：推广支出增加是负面的，减少是正面的
                    } else if (data.promotionTrend && data.promotionTrend.values && data.promotionTrend.values.length >= 2) {
                        // 如果有推广趋势数据，计算最近两天的变化率
                        const latestPromo = data.promotionTrend.values[data.promotionTrend.values.length - 1];
                        const previousPromo = data.promotionTrend.values[data.promotionTrend.values.length - 2];

                        if (previousPromo > 0) {
                            const trend = ((latestPromo / previousPromo) * 100 - 100).toFixed(1);
                            trendElement.textContent = `${trend >= 0 ? '+' : ''}${trend}%`;
                            trendElement.className = `trend ${trend >= 0 ? 'negative' : 'positive'}`; // 推广支出增加是负面的
                        }
                    }
                    break;
                case 'total-orders':
                    overview = data.overview.totalOrders;
                    valueElement.textContent = overview.value.toLocaleString();
                    break;
                case 'monthly-orders':
                    // 近一月订单数可以从订单趋势数据或通过平均客单价来估算
                    if (data.salesTrend && data.salesTrend.orderCounts) {
                        const monthlyOrders = data.salesTrend.orderCounts.reduce((sum, curr) => sum + curr, 0);
                        valueElement.textContent = monthlyOrders.toLocaleString();
                        // 计算环比增长
                        if (data.overview.totalOrders) {
                            const trend = ((monthlyOrders / data.overview.totalOrders.value) * 100 - 100).toFixed(1);
                            trendElement.textContent = `${trend >= 0 ? '+' : ''}${trend}%`;
                            trendElement.className = `trend ${trend >= 0 ? 'positive' : 'negative'}`;
                        }
                    } else if (data.salesTrend && data.salesTrend.values && data.overview.avgOrderPrice) {
                        // 如果没有订单数数据，使用销售额除以平均客单价来估算
                        const monthlySales = data.salesTrend.values.reduce((sum, curr) => sum + curr, 0);
                        const monthlyOrders = Math.round(monthlySales / data.overview.avgOrderPrice.value);
                        valueElement.textContent = monthlyOrders.toLocaleString();
                        // 计算环比增长
                        if (data.overview.totalOrders) {
                            const trend = ((monthlyOrders / data.overview.totalOrders.value) * 100 - 100).toFixed(1);
                            trendElement.textContent = `${trend >= 0 ? '+' : ''}${trend}%`;
                            trendElement.className = `trend ${trend >= 0 ? 'positive' : 'negative'}`;
                        }
                    } else {
                        valueElement.textContent = `暂无数据`;
                    }
                    break;
                case 'monthly-net-orders':
                    // 计算近一月净订单数（总订单数减去退款订单数）
                    if (data.salesTrend && data.salesTrend.orderCounts) {
                        // 获取订单总数
                        const monthlyOrders = data.salesTrend.orderCounts.reduce((sum, curr) => sum + curr, 0);
                        
                        // 估算退款订单数
                        let monthlyRefundOrders = 0;
                        if (data.salesTrend.refundCounts) {
                            monthlyRefundOrders = data.salesTrend.refundCounts.reduce((sum, curr) => sum + curr, 0);
                        } else if (data.overview.refundRate && data.overview.refundRate.value) {
                            // 如果没有具体退款订单数据但有退款率，使用退款率计算
                            monthlyRefundOrders = Math.round(monthlyOrders * (data.overview.refundRate.value / 100));
                        }
                        
                        // 计算净订单数
                        const monthlyNetOrders = monthlyOrders - monthlyRefundOrders;
                        valueElement.textContent = monthlyNetOrders.toLocaleString();
                        
                        // 计算趋势
                        if (data.overview.netOrders) {
                            const trend = ((monthlyNetOrders / data.overview.netOrders.value) * 100 - 100).toFixed(1);
                            trendElement.textContent = `${trend >= 0 ? '+' : ''}${trend}%`;
                            trendElement.className = `trend ${trend >= 0 ? 'positive' : 'negative'}`;
                        }
                    } else if (data.salesTrend && data.salesTrend.values && data.overview.avgOrderPrice) {
                        // 如果没有订单数数据，使用销售额除以平均客单价来估算
                        const monthlySales = data.salesTrend.values.reduce((sum, curr) => sum + curr, 0);
                        const monthlyOrders = Math.round(monthlySales / data.overview.avgOrderPrice.value);
                        
                        // 估算退款订单数
                        let monthlyRefundOrders = 0;
                        if (data.overview.refundRate && data.overview.refundRate.value) {
                            monthlyRefundOrders = Math.round(monthlyOrders * (data.overview.refundRate.value / 100));
                        }
                        
                        // 计算净订单数
                        const monthlyNetOrders = monthlyOrders - monthlyRefundOrders;
                        valueElement.textContent = monthlyNetOrders.toLocaleString();
                        
                        // 计算趋势
                        if (data.overview.netOrders) {
                            const trend = ((monthlyNetOrders / data.overview.netOrders.value) * 100 - 100).toFixed(1);
                            trendElement.textContent = `${trend >= 0 ? '+' : ''}${trend}%`;
                            trendElement.className = `trend ${trend >= 0 ? 'positive' : 'negative'}`;
                        }
                    } else {
                        valueElement.textContent = `暂无数据`;
                    }
                    break;
                case 'avg-price':
                    overview = data.overview.avgOrderPrice;
                    valueElement.textContent = `¥${overview.value.toFixed(2)}`;
                    break;
                case 'conversion':
                    overview = data.overview.conversion;
                    valueElement.textContent = `${overview.value.toFixed(1)}%`;
                    break;
                case 'refund-rate':
                    overview = data.overview.refundRate;
                    valueElement.textContent = `${overview.value.toFixed(1)}%`;
                    break;
                case 'net-orders':
                    overview = data.overview.netOrders;
                    valueElement.textContent = overview.value.toLocaleString();

                    // 显示趋势数据
                    if (overview.trend !== undefined) {
                        const trend = overview.trend;
                        trendElement.textContent = `${trend >= 0 ? '+' : ''}${trend}%`;
                        trendElement.className = `trend ${trend >= 0 ? 'positive' : 'negative'}`;
                    }
                    break;
                case 'roi':
                    overview = data.overview.roi;
                    valueElement.textContent = overview.value.toFixed(2); // 不显示百分号

                    // 显示趋势数据
                    if (overview.trend !== undefined) {
                        const trend = overview.trend;
                        trendElement.textContent = `${trend >= 0 ? '+' : ''}${trend}%`;
                        trendElement.className = `trend ${trend >= 0 ? 'positive' : 'negative'}`;
                    }
                    break;
                case 'active-shops':
                    overview = data.overview.activeShops;
                    valueElement.textContent = overview.value;
                    break;
            }

            if (overview && type !== 'monthly-sales' && type !== 'monthly-orders' && type !== 'yesterday-sales' 
                && type !== 'monthly-net-sales' && type !== 'monthly-net-orders') {
                trendElement.textContent = `${overview.trend >= 0 ? '+' : ''}${overview.trend}%`;
                trendElement.className = `trend ${overview.trend >= 0 ? 'positive' : 'negative'}`;
            }
        });

        // 销售趋势图表
        const salesTrendCanvas = document.getElementById('salesTrendCanvas');
        if (salesTrendCanvas) {
            if (data.salesTrend && data.salesTrend.values && data.salesTrend.dates) {
                // 如果之前已经初始化了图表，先销毁它
                if (window.salesTrendChart) {
                    window.salesTrendChart.destroy();
                }

                // 创建彩虹色渐变背景
                const ctx = salesTrendCanvas.getContext('2d');
                const gradientSales = ctx.createLinearGradient(0, 0, 0, 400);
                gradientSales.addColorStop(0, 'rgba(255, 99, 132, 0.3)');   // 红色
                gradientSales.addColorStop(0.16, 'rgba(255, 159, 64, 0.3)'); // 橙色
                gradientSales.addColorStop(0.33, 'rgba(255, 205, 86, 0.3)'); // 黄色
                gradientSales.addColorStop(0.5, 'rgba(75, 192, 192, 0.3)');  // 青色
                gradientSales.addColorStop(0.66, 'rgba(54, 162, 235, 0.3)'); // 蓝色
                gradientSales.addColorStop(0.83, 'rgba(153, 102, 255, 0.3)');// 紫色
                gradientSales.addColorStop(1, 'rgba(255, 99, 132, 0.1)');    // 红色淡化

                // 创建彩虹色边框渐变
                const borderGradient = ctx.createLinearGradient(0, 0, ctx.canvas.width, 0);
                borderGradient.addColorStop(0, '#ff6384');     // 红色
                borderGradient.addColorStop(0.16, '#ff9f40');  // 橙色
                borderGradient.addColorStop(0.33, '#ffcd56');  // 黄色
                borderGradient.addColorStop(0.5, '#4bc0c0');   // 青色
                borderGradient.addColorStop(0.66, '#36a2eb');  // 蓝色
                borderGradient.addColorStop(0.83, '#9966ff');  // 紫色
                borderGradient.addColorStop(1, '#ff6384');     // 红色

                // 初始化销售趋势图表
                window.salesTrendChart = new Chart(salesTrendCanvas, {
                    type: 'line',
                    data: {
                        labels: data.salesTrend.dates,
                        datasets: [
                            {
                                label: '销售额',
                                data: data.salesTrend.values,
                                backgroundColor: gradientSales,
                                borderColor: borderGradient,
                                borderWidth: 3,
                                pointBackgroundColor: '#ffffff',
                                pointBorderColor: function(context) {
                                    const chart = context.chart;
                                    const {ctx, chartArea} = chart;
                                    if (!chartArea) {
                                        return null;
                                    }
                                    const gradient = ctx.createLinearGradient(0, 0, chartArea.width, 0);
                                    gradient.addColorStop(0, '#ff6384');
                                    gradient.addColorStop(0.16, '#ff9f40');
                                    gradient.addColorStop(0.33, '#ffcd56');
                                    gradient.addColorStop(0.5, '#4bc0c0');
                                    gradient.addColorStop(0.66, '#36a2eb');
                                    gradient.addColorStop(0.83, '#9966ff');
                                    gradient.addColorStop(1, '#ff6384');
                                    return gradient;
                                },
                                pointBorderWidth: 2,
                                pointRadius: 5,
                                pointHoverRadius: 8,
                                tension: 0.4,
                                fill: true
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        interaction: {
                            mode: 'index',
                            intersect: false,
                        },
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top',
                                labels: {
                                    usePointStyle: true,
                                    padding: 20,
                                    font: {
                                        size: 12
                                    }
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(30, 41, 59, 0.85)',
                                titleColor: '#f1f5f9',
                                bodyColor: '#e2e8f0',
                                padding: 12,
                                cornerRadius: 6,
                                titleFont: {
                                    size: 14,
                                    weight: 'bold'
                                },
                                bodyFont: {
                                    size: 13
                                },
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        label += '¥' + context.parsed.y.toLocaleString();
                                        return label;
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    font: {
                                        size: 11
                                    },
                                    maxRotation: 45,
                                    minRotation: 45
                                }
                            },
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)'
                                },
                                ticks: {
                                    font: {
                                        size: 12
                                    },
                                    callback: function(value) {
                                        return '¥' + value.toLocaleString();
                                    }
                                }
                            }
                        }
                    }
                });
            } else {
                const chartContainer = document.getElementById('salesTrendChart');
                chartContainer.innerHTML = `
                    <div style="padding: 20px; text-align: center; color: #64748b;">
                        <p>销售趋势数据不可用</p>
                    </div>
                `;
            }
        }

        // TOP5店铺图表
        const topShopsCanvas = document.getElementById('topShopsCanvas');
        if (topShopsCanvas) {
            if (data.topShops && data.topShops.length > 0) {
                // 如果之前已经初始化了图表，先销毁它
                if (window.topShopsChart) {
                    window.topShopsChart.destroy();
                }

                // 准备数据
                const shopNames = data.topShops.map(shop => shop.name);
                const shopSales = data.topShops.map(shop => shop.sales);

                // 颜色数组
                const backgroundColors = [
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(255, 159, 64, 0.8)',
                    'rgba(153, 102, 255, 0.8)',
                    'rgba(255, 99, 132, 0.8)'
                ];

                // 初始化TOP5店铺图表
                window.topShopsChart = new Chart(topShopsCanvas, {
                    type: 'bar',
                    data: {
                        labels: shopNames,
                        datasets: [{
                            label: '销售额',
                            data: shopSales,
                            backgroundColor: backgroundColors,
                            borderColor: backgroundColors.map(color => color.replace('0.8', '1')),
                            borderWidth: 1,
                            borderRadius: 6,
                            barThickness: 'flex',
                            maxBarThickness: 30
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        indexAxis: 'y',
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                backgroundColor: 'rgba(30, 41, 59, 0.85)',
                                titleColor: '#f1f5f9',
                                bodyColor: '#e2e8f0',
                                padding: 12,
                                cornerRadius: 6,
                                titleFont: {
                                    size: 14,
                                    weight: 'bold'
                                },
                                bodyFont: {
                                    size: 13
                                },
                                callbacks: {
                                    label: function(context) {
                                        return `销售额: ¥${context.parsed.x.toLocaleString()}`;
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)'
                                },
                                ticks: {
                                    font: {
                                        size: 12
                                    },
                                    callback: function(value) {
                                        return '¥' + value.toLocaleString();
                                    }
                                }
                            },
                            y: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            } else {
                const chartContainer = document.getElementById('topShopsChart');
                chartContainer.innerHTML = `
                    <div style="padding: 20px; text-align: center; color: #64748b;">
                        <p>TOP5店铺数据不可用</p>
                    </div>
                `;
            }
        }

        // 时段销售分布网格
        const hourlySalesGrid = document.getElementById('hourlySalesGrid');
        if (hourlySalesGrid) {
            if (data.hourlySales && data.hourlySales.hours && data.hourlySales.values) {
                const maxHourlySales = Math.max(...data.hourlySales.values);
                const minHourlySales = Math.min(...data.hourlySales.values);
                const salesRange = maxHourlySales > minHourlySales ? maxHourlySales - minHourlySales : 1;

                hourlySalesGrid.innerHTML = data.hourlySales.hours.map((hour, index) => {
                    const salesValue = data.hourlySales.values[index];
                    // 调整强度计算，使其分布更均匀，并确保低值也有一定颜色
                    const intensityRatio = maxHourlySales > 0 ? (salesValue - minHourlySales) / salesRange : 0;
                    const colorIntensity = 0.15 + intensityRatio * 0.85; // 基础强度0.15，最大增加0.85

                    return `
                        <div class="hour-block"
                             style="--intensity: rgba(59, 130, 246, ${colorIntensity})"
                             title="${hour}时: ¥${salesValue.toLocaleString()}">
                            ${hour}时
                        </div>
                    `;
                }).join('');
            } else {
                hourlySalesGrid.innerHTML = `
                    <div style="padding: 20px; text-align: center; color: #64748b; grid-column: 1 / -1;">
                        <p>时段销售分布数据不可用</p>
                    </div>
                `;
            }
        }

        // 绘制销售数据表格
        drawSalesTable();

    } catch (error) {
        console.error('初始化数据时发生错误:', error);
        // 显示错误提示
        const container = document.querySelector('.container');
        if (container) {
            container.innerHTML += `
                <div class="error-message" style="color: #ef4444; text-align: center; margin-top: 20px; padding: 16px; background: #fef2f2; border-radius: 8px; border-left: 4px solid #ef4444;">
                    <p style="font-weight: 600; margin-bottom: 5px;">加载数据失败</p>
                    <p style="font-size: 14px; color: #64748b;">${error.message || '请稍后重试'}</p>
                </div>
            `;
        }
    }
}

// 绘制销售数据表格
function drawSalesTable() {
    // 获取容器元素
    const salesTableContainer = document.getElementById('salesTableContainer');
    
    if (!salesTableContainer) return;
    
    // 获取当前数据
    const data = window.dashboardData;
    
    if (!data || !data.salesTrend || !data.salesTrend.dates || !data.salesTrend.values) {
        salesTableContainer.innerHTML = `
            <div style="padding: 20px; text-align: center; color: #64748b;">
                <p>销售数据不可用</p>
            </div>
        `;
        return;
    }
    
    // 清空容器内容
    salesTableContainer.innerHTML = '';
    
    // 准备表格数据
    const tableData = prepareSalesTableData(data);
    
    // 渲染销售数据表格
    renderSalesTable(salesTableContainer, tableData);
}

// 准备销售表格数据
function prepareSalesTableData(data) {
    const tableData = [];
    
    // 如果没有销售趋势数据，返回空数组
    if (!data || !data.salesTrend || !data.salesTrend.dates || !data.salesTrend.values) {
        return [];
    }
    
    // 创建每日数据映射
    const salesByDate = {};
    const promotionByDate = {};
    const refundByDate = {};
    const ordersByDate = {};
    
    // 处理销售数据
    data.salesTrend.dates.forEach((dateStr, index) => {
        const dateKey = dateStr;
        salesByDate[dateKey] = data.salesTrend.values[index] || 0;
    });
    
    // 处理退款数据
    if (data.salesTrend.refundValues && data.salesTrend.refundValues.length > 0) {
        data.salesTrend.dates.forEach((dateStr, index) => {
            const dateKey = dateStr;
            refundByDate[dateKey] = data.salesTrend.refundValues[index] || 0;
        });
    } else if (data.overview && data.overview.refundRate) {
        // 使用退款率估算退款金额
        const refundRate = data.overview.refundRate.value / 100;
        Object.keys(salesByDate).forEach(dateKey => {
            refundByDate[dateKey] = salesByDate[dateKey] * refundRate;
        });
    }
    
    // 处理订单数据
    if (data.salesTrend.orderCounts && data.salesTrend.orderCounts.length > 0) {
        data.salesTrend.dates.forEach((dateStr, index) => {
            const dateKey = dateStr;
            ordersByDate[dateKey] = data.salesTrend.orderCounts[index] || 0;
        });
    } else if (data.overview && data.overview.avgOrderPrice && data.overview.avgOrderPrice.value > 0) {
        // 使用平均客单价估算订单数
        Object.keys(salesByDate).forEach(dateKey => {
            ordersByDate[dateKey] = Math.round(salesByDate[dateKey] / data.overview.avgOrderPrice.value);
        });
    }
    
    // 处理推广费用数据
    if (data.promotionTrend && data.promotionTrend.values && data.promotionTrend.dates) {
        data.promotionTrend.dates.forEach((dateStr, index) => {
            const dateKey = dateStr;
            promotionByDate[dateKey] = data.promotionTrend.values[index] || 0;
        });
    } else if (data.overview && data.overview.promotionExpense) {
        // 如果没有具体的每日推广费用，平均分配
        const totalDays = Object.keys(salesByDate).length;
        const avgPromotion = totalDays > 0 ? data.overview.promotionExpense.value / totalDays : 0;
        Object.keys(salesByDate).forEach(dateKey => {
            promotionByDate[dateKey] = avgPromotion;
        });
    }
    
    // 合并所有数据并创建表格行
    const allDates = [...new Set([
        ...Object.keys(salesByDate),
        ...Object.keys(promotionByDate)
    ])].sort().slice(-30); // 只取最近30天
    
    allDates.forEach(dateKey => {
        const sales = salesByDate[dateKey] || 0;
        const refund = refundByDate[dateKey] || 0;
        const promotion = promotionByDate[dateKey] || 0;
        const orders = ordersByDate[dateKey] || 0;
        const netSales = Math.max(0, sales - refund);
        const roi = promotion > 0 ? (netSales / promotion).toFixed(2) : '--';
        const avgOrderPrice = orders > 0 ? (sales / orders).toFixed(2) : '--';
        
        // 计算环比增长
        const prevDateIndex = allDates.indexOf(dateKey) - 1;
        let salesGrowth = '--';
        if (prevDateIndex >= 0) {
            const prevDate = allDates[prevDateIndex];
            const prevSales = salesByDate[prevDate] || 0;
            if (prevSales > 0) {
                const growth = ((sales / prevSales) * 100 - 100).toFixed(1);
                salesGrowth = `${growth >= 0 ? '+' : ''}${growth}%`;
            }
        }
        
        tableData.push({
            date: dateKey,
            sales: sales,
            netSales: netSales,
            promotion: promotion,
            orders: orders,
            roi: roi,
            avgOrderPrice: avgOrderPrice,
            refund: refund,
            salesGrowth: salesGrowth
        });
    });
    
    // 按日期倒序排列，最新的在最前面
    return tableData.reverse();
}

// 渲染销售数据表格
function renderSalesTable(container, tableData) {
    // 检查数据是否为空
    if (!tableData || tableData.length === 0) {
        container.innerHTML = `
            <div style="padding: 20px; text-align: center; color: #64748b;">
                <p>暂无近期销售数据</p>
            </div>
        `;
        return;
    }
    
    // 创建表格HTML结构
    const tableHTML = `
        <div class="sales-table-wrapper">
            <table class="sales-table">
                <thead>
                    <tr>
                        <th>日期</th>
                        <th>销售额</th>
                        <th>净销售额</th>
                        <th>退款金额</th>
                        <th>推广费用</th>
                        <th>ROI</th>
                        <th>订单数</th>
                        <th>客单价</th>
                        <th>环比增长</th>
                    </tr>
                </thead>
                <tbody>
                    ${tableData.map(row => `
                        <tr>
                            <td class="date-cell">${formatDate(row.date)}</td>
                            <td class="money-cell">¥${row.sales.toLocaleString()}</td>
                            <td class="money-cell">¥${row.netSales.toLocaleString()}</td>
                            <td class="money-cell">¥${row.refund.toLocaleString()}</td>
                            <td class="money-cell">¥${row.promotion.toLocaleString()}</td>
                            <td class="roi-cell">${row.roi}</td>
                            <td class="number-cell">${row.orders.toLocaleString()}</td>
                            <td class="money-cell">${row.avgOrderPrice === '--' ? '--' : '¥' + row.avgOrderPrice}</td>
                            <td class="growth-cell ${getGrowthClass(row.salesGrowth)}">${row.salesGrowth}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
    
    container.innerHTML = tableHTML;
    
    // 添加表格样式
    addSalesTableStyles();
}

// 格式化日期显示
function formatDate(dateStr) {
    const date = new Date(dateStr);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
        return '今天';
    } else if (date.toDateString() === yesterday.toDateString()) {
        return '昨天';
    } else {
        return `${date.getMonth() + 1}/${date.getDate()}`;
    }
}

// 获取增长率的CSS类名
function getGrowthClass(growthStr) {
    if (growthStr === '--') return '';
    const growth = parseFloat(growthStr);
    return growth >= 0 ? 'positive' : 'negative';
}

// 添加销售表格样式
function addSalesTableStyles() {
    // 检查是否已添加样式
    if (document.getElementById('sales-table-styles')) return;
    
    const styleElement = document.createElement('style');
    styleElement.id = 'sales-table-styles';
    styleElement.textContent = `
        .sales-table-wrapper {
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .sales-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            font-size: 13px;
        }
        
        .sales-table th {
            background: #f8fafc;
            color: #64748b;
            font-weight: 600;
            padding: 12px 8px;
            text-align: center;
            border-bottom: 2px solid #e2e8f0;
            position: sticky;
            top: 0;
            z-index: 10;
            vertical-align: middle;
        }
        
        .sales-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #f1f5f9;
            color: #334155;
            text-align: center;
            vertical-align: middle;
        }
        
        .sales-table tbody tr:hover {
            background: #f8fafc;
        }
        
        .sales-table tbody tr:nth-child(even) {
            background: #fafbfc;
        }
        
        .sales-table tbody tr:nth-child(even):hover {
            background: #f1f5f9;
        }
        
        .date-cell {
            font-weight: 500;
            color: #3b82f6;
            min-width: 80px;
            text-align: center !important;
        }
        
        .money-cell {
            text-align: center !important;
            font-weight: 500;
            min-width: 90px;
        }
        
        .number-cell {
            text-align: center !important;
            min-width: 70px;
        }
        
        .roi-cell {
            text-align: center !important;
            font-weight: 600;
            min-width: 60px;
        }
        
        .growth-cell {
            text-align: center !important;
            font-weight: 600;
            min-width: 80px;
        }
        
        .growth-cell.positive {
            color: #16a34a;
        }
        
        .growth-cell.negative {
            color: #dc2626;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sales-table {
                font-size: 12px;
            }
            
            .sales-table th,
            .sales-table td {
                padding: 8px 4px;
            }
        }
    `;
    document.head.appendChild(styleElement);
}


