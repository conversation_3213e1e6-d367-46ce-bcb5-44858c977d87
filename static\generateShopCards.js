// 检查登录状态
function checkLoginStatus() {
    const cookies = document.cookie.split(';');
    const loginAuth = cookies.find(cookie => cookie.trim().startsWith('loginAuth='));
    if (!loginAuth || loginAuth.split('=')[1].trim() !== 'true') {
        window.location.href = 'login.html';
        return false;
    }
    return true;
}

// 从API获取店铺数据
async function fetchShopData() {
    try {
        // 获取日期选择器
        // const datePicker = document.getElementById('date-picker'); // No longer needed
        const startDatePicker = document.getElementById('start-date-picker');
        const endDatePicker = document.getElementById('end-date-picker');
        
        let url = '/api/shops';
        
        // Always use date range
        if (startDatePicker && endDatePicker && startDatePicker.value && endDatePicker.value) {
            url = `/api/shops?startDate=${startDatePicker.value}&endDate=${endDatePicker.value}`;
        } else {
            // Fallback if somehow date pickers are not set, backend will default to yesterday
            console.warn('Start or End date picker not found or has no value, falling back to default API call.');
        }
        
        console.log('Fetching shop data from URL:', url); // Debug log
        const response = await fetch(url);
        const result = await response.json();

        if (result.success) {
            console.log('Fetched shop data:', result.data); // Debug log
            const processedData = result.data.map(shop => {
                if (shop.promoAmount === undefined || shop.promoAmount === null) {
                    shop.promoAmount = 0;
                }
                return shop;
            });
            return processedData;
        } else {
            console.error('获取店铺数据失败:', result.message);
            return [];
        }
    } catch (error) {
        console.error('获取店铺数据失败:', error);
        return [];
    }
}

// 生成店铺列表HTML
function generateShopList(shop) {
    // 计算退款率
    const refundRate = shop.refundAmount && shop.dailySales ? 
        (shop.refundAmount / shop.dailySales * 100).toFixed(2) : '0.00';
    
    // 确定高亮类
    const refundRateClass = parseFloat(refundRate) > 10 ? 'highlight-negative' : 
                           parseFloat(refundRate) < 5 ? 'highlight-positive' : '';
    
    // 确定转化率高亮
    const conversionRateClass = typeof shop.conversionRate === 'number' && shop.conversionRate > 10 ? 
        'highlight-positive' : '';
    
    // 格式化数值
    const formattedSales = typeof shop.dailySales === 'number' ? 
        shop.dailySales.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : 
        shop.dailySales;
    
    const formattedRefund = typeof shop.refundAmount === 'number' ? 
        shop.refundAmount.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : 
        '0.00';
    
    const formattedPromo = typeof shop.promoAmount === 'number' ? 
        shop.promoAmount.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : 
        '0.00';
    
    // DSR类
    const dsrClass = parseFloat(shop.DSR) > 80 ? 'highlight-positive' : 
                    parseFloat(shop.DSR) < 50 ? 'highlight-negative' : '';
    
    // 新上链接类
    const newLinksCount = parseInt(shop.productCount) || 0;
    const newLinksClass = newLinksCount > 5 ? 'highlight-positive' : 
                        newLinksCount === 0 ? 'highlight-negative' : '';
    
    // 获取新上链接的日期范围
    const newLinksDate = shop.newLinksDate || '无数据';
    
    return `
        <tr>
            <td title="${shop.name}">
                <div class="shop-name">${shop.name}</div>
            </td>
            <td title="${shop.operator}">
                <div class="operator-badge">${shop.operator}</div>
            </td>
            <td title="${shop.DSR}">
                <span class="${dsrClass}">${shop.DSR}</span>
            </td>
            <td title="¥${shop.dailySales}">
                <div class="sales-amount">¥${formattedSales}</div>
            </td>
            <td title="${shop.dailyOrders}">
                ${shop.dailyOrders}
            </td>
            <td title="${shop.dailyBuyers || '0'}">
                ${shop.dailyBuyers || '0'}
            </td>
            <td title="${shop.conversionRate || '0'}%">
                <span class="${conversionRateClass}">${typeof shop.conversionRate === 'number' ? shop.conversionRate.toFixed(2) : '0.00'}%</span>
            </td>
            <td title="¥${shop.promoAmount || '0'}">
                <div class="promo-amount">¥${formattedPromo}</div>
            </td>
            <td title="¥${shop.refundAmount || '0'}">
                <div class="refund-amount">¥${formattedRefund}</div>
            </td>
            <td title="${shop.refundOrders || '0'}">
                ${shop.refundOrders || '0'}
            </td>
            <td title="${refundRate}%">
                <span class="${refundRateClass}">${refundRate}%</span>
            </td>
            <td title="期间新上链接数: ${newLinksDate}">
                <div class="product-count ${newLinksClass}">${newLinksCount}</div>
            </td>
            <td title="${shop.rating}">
                <div class="rating-display">${shop.rating}</div>
            </td>
            <td>
                <button class="action-btn">查看</button>
            </td>
        </tr>
    `;
}

// 生成店铺卡片HTML
function generateShopDisplayCard(shop) {
    // 格式化数字为千分位显示
    const formatNumber = (num) => {
        return new Intl.NumberFormat('zh-CN').format(num);
    };

    // 根据日销售额大小设置不同的颜色和标识
    let salesClass = '';
    if (shop.dailySales > 500000) {
        salesClass = 'text-highlight';
    } else if (shop.dailySales > 100000) {
        salesClass = 'text-positive';
    }

    // ROI计算
    const roi = shop.promoAmount ? (shop.dailySales / shop.promoAmount * 100).toFixed(2) + '%' : 'N/A';
    let roiClass = '';
    if (shop.promoAmount) {
        const roiValue = shop.dailySales / shop.promoAmount;
        if (roiValue > 4) {
            roiClass = 'text-positive';
        } else if (roiValue < 2) {
            roiClass = 'text-negative';
        }
    }
    
    return `
        <div class="shop-card">
            <div class="shop-card-header">
                <h3 class="shop-card-title">${shop.name}</h3>
                <span class="shop-info-value">${shop.operator || '未分配'}</span>
            </div>
            <div class="shop-card-body">
                <div class="shop-info-item">
                    <span class="shop-info-label">日销售额</span>
                    <span class="shop-info-value highlight ${salesClass}">¥${formatNumber(shop.dailySales.toFixed(2))}</span>
                    </div>
                <div class="shop-info-item">
                    <span class="shop-info-label">日订单数</span>
                    <span class="shop-info-value">${formatNumber(shop.dailyOrders)}</span>
                    </div>
                <div class="shop-info-item">
                    <span class="shop-info-label">转化率</span>
                    <span class="shop-info-value">${shop.conversionRate.toFixed(2)}%</span>
                    </div>
                <div class="shop-info-item">
                    <span class="shop-info-label">推广费</span>
                    <span class="shop-info-value">¥${formatNumber(shop.promoAmount.toFixed(2))}</span>
                </div>
                <div class="shop-info-item">
                    <span class="shop-info-label">ROI</span>
                    <span class="shop-info-value ${roiClass}">${roi}</span>
                    </div>
                <div class="shop-info-item">
                    <span class="shop-info-label">DSR</span>
                    <span class="shop-info-value">${shop.DSR}</span>
                    </div>
                    </div>
            <div class="shop-card-footer">
                <button class="detail-btn" onClick="showShopDetails('${shop.name}')">查看详情</button>
            </div>
        </div>
    `;
}

// 生成运营人员卡片
function generateOperatorCard(operator, shops) {
    // 计算总销售额、总订单数、总推广费、总新上链接数
    let totalSales = 0;
    let totalOrders = 0;
    let totalPromo = 0;
    let totalNewLinks = 0;
    let shopCount = shops.length;
    let dateRange = shops.length > 0 ? shops[0].newLinksDate : "";

    shops.forEach(shop => {
        totalSales += shop.dailySales || 0;
        totalOrders += shop.dailyOrders || 0; 
        totalPromo += shop.promoAmount || 0;
        totalNewLinks += parseInt(shop.productCount) || 0;
    });

    // 计算总ROI
    const roi = totalPromo ? (totalSales / totalPromo).toFixed(2) : 'N/A';
    
    // 格式化数字为千分位显示
    const formatNumber = (num) => {
        if (typeof num !== 'number') return '0';
        return new Intl.NumberFormat('zh-CN').format(num);
    };
    
    // 确定ROI颜色
    let roiClass = '';
    if (totalPromo) {
        const roiValue = totalSales / totalPromo;
        if (roiValue > 4) {
            roiClass = 'text-positive';
        } else if (roiValue < 2) {
            roiClass = 'text-negative';
        }
    }
    
    // 新上链接数据高亮，修改为深色
    let newLinksClass = 'text-dark'; // 默认使用深色
    if (totalNewLinks > 15) {
        newLinksClass = 'text-dark highlight-glow'; // 高亮时也使用深色
    } else if (totalNewLinks === 0) {
        newLinksClass = 'text-negative';
    }

    return `
        <div class="operator-card" data-operator="${operator}">
            <div class="operator-header" onclick="toggleCollapse(this.parentNode)">
                <div class="operator-header-left">
                    <span class="collapse-icon">▶</span>
                    <div class="operator-name">${operator}</div>
                    <div class="operator-count">${shopCount} 个店铺</div>
                </div>
                <div class="operator-metrics">
                    <div class="metric-mini sortable-metric" onclick="sortOperatorShops('${operator}', 'dailySales')" title="点击按销售额排序">
                        <span class="metric-value">${formatNumber(totalSales)}</span>
                        <span class="metric-label">销售额</span>
                    </div>
                    <div class="metric-mini sortable-metric" onclick="sortOperatorShops('${operator}', 'dailyOrders')" title="点击按订单数排序">
                        <span class="metric-value">${formatNumber(totalOrders)}</span>
                        <span class="metric-label">订单数</span>
                    </div>
                    <div class="metric-mini sortable-metric" onclick="sortOperatorShops('${operator}', 'promoAmount')" title="点击按推广费排序">
                        <span class="metric-value">${formatNumber(totalPromo)}</span>
                        <span class="metric-label">推广费</span>
                    </div>
                    <div class="metric-mini sortable-metric" onclick="sortOperatorShops('${operator}', 'roi')" title="点击按ROI排序">
                        <span class="metric-value ${roiClass}">${roi}</span>
                        <span class="metric-label">ROI</span>
                    </div>
                    <div class="metric-mini sortable-metric" onclick="sortOperatorShops('${operator}', 'newLinks')" title="点击按新上链接数排序">
                        <span class="metric-value ${newLinksClass}">${formatNumber(totalNewLinks)}</span>
                        <span class="metric-label">新上链接</span>
                    </div>
                </div>
            </div>
            <div class="operator-shops" style="display:none;">
                ${shops.map((shop, index) => `
                    <div class="shop-lazy-container" data-lazy-index="${index}" data-operator="${operator}">
                        ${index < 5 ? generateOperatorShopItem(shop) : '<div class="lazy-placeholder"><span class="loading-dot"></span><span class="loading-dot"></span><span class="loading-dot"></span></div>'}
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// 生成运营人员下的店铺项
function generateOperatorShopItem(shop) {
    // 计算今日ROI
    const roi = shop.promoAmount ? (shop.dailySales / shop.promoAmount).toFixed(2) : 'N/A';
    
    // 格式化数字为千分位显示
    const formatNumber = (num) => {
        if (typeof num !== 'number') return '0';
        return new Intl.NumberFormat('zh-CN').format(num);
    };
    
    // 确定ROI颜色
    let roiClass = '';
    if (shop.promoAmount) {
        const roiValue = shop.dailySales / shop.promoAmount;
        if (roiValue > 4) {
            roiClass = 'text-positive';
        } else if (roiValue < 2) {
            roiClass = 'text-negative';
        }
    }
    
    // 计算退款率
    const refundRate = shop.dailySales ? ((shop.refundAmount / shop.dailySales) * 100).toFixed(2) + '%' : '0%';
    
    // 确定退款率颜色
    let refundClass = '';
    if (shop.dailySales) {
        const refundValue = (shop.refundAmount / shop.dailySales) * 100;
        if (refundValue < 3) {
            refundClass = 'text-positive';
        } else if (refundValue > 10) {
            refundClass = 'text-negative';
        }
    }

    // 获取新上链接数
    const newLinks = parseInt(shop.productCount) || 0;
    
    // 修改新上链接数的颜色
    let newLinksClass = 'text-dark'; // 默认使用深色
    if (newLinks > 5) {
        newLinksClass = 'text-dark highlight-glow'; // 高亮时也使用深色
    } else if (newLinks === 0) {
        newLinksClass = 'text-negative';
    }
    
    // 显示新上链接的日期范围
    const newLinksDate = shop.newLinksDate || '无数据';

    // 创建优化后的紧凑型树状结构的店铺项
    return `
        <div class="tree-shop-item" onclick="showShopDetails('${shop.name}')">
            <div class="tree-shop-connector">
                <div class="tree-branch"></div>
            </div>
            <div class="tree-shop-content">
                <div class="shop-name-section">
                    <div class="shop-name-container">
                        <div class="shop-name">${shop.name}</div>
                        <div class="shop-dsr">DSR: <span class="${parseFloat(shop.DSR) > 80 ? 'text-positive' : parseFloat(shop.DSR) < 50 ? 'text-negative' : ''}">${shop.DSR}</span></div>
                    </div>
                </div>
                <div class="shop-metrics-row">
                    <div class="shop-metric-tree sales-metric">
                        <span class="metric-value">${formatNumber(shop.dailySales)}</span>
                        <span class="metric-label">销售额</span>
                    </div>
                    <div class="shop-metric-tree orders-metric">
                        <span class="metric-value">${shop.dailyOrders}</span>
                        <span class="metric-label">订单</span>
                    </div>
                    <div class="shop-metric-tree promo-metric">
                        <span class="metric-value">${formatNumber(shop.promoAmount || 0)}</span>
                        <span class="metric-label">推广费</span>
                    </div>
                    <div class="shop-metric-tree roi-metric">
                        <span class="metric-value ${roiClass}">${roi}</span>
                        <span class="metric-label">ROI</span>
                    </div>
                    <div class="shop-metric-tree refund-metric">
                        <span class="metric-value ${refundClass}">${refundRate}</span>
                        <span class="metric-label">退款率</span>
                    </div>
                    <div class="shop-metric-tree links-metric" title="期间新上链接数: ${newLinksDate}">
                        <span class="metric-value ${newLinksClass}">${newLinks}</span>
                        <span class="metric-label">新上链接</span>
                    </div>
                </div>
                <div class="shop-action">
                    <button class="view-details-btn">查看详情</button>
                </div>
            </div>
        </div>
    `;
}

// 添加懒加载功能
window.addEventListener('scroll', handleLazyLoad);
window.addEventListener('resize', handleLazyLoad);

function handleLazyLoad() {
    const lazyContainers = document.querySelectorAll('.shop-lazy-container');
    
    lazyContainers.forEach(container => {
        if (container.querySelector('.lazy-placeholder') && isElementInViewport(container)) {
            const index = container.getAttribute('data-lazy-index');
            const operator = container.getAttribute('data-operator');
            
            // 获取该运营下的所有店铺
            const operatorShops = window.allShops.filter(shop => 
                (shop.operator || '未分配') === operator
            );
            
            if (operatorShops[index]) {
                container.innerHTML = generateOperatorShopItem(operatorShops[index]);
            }
        }
    });
}

function isElementInViewport(el) {
    const rect = el.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}

// 折叠/展开运营卡片的功能
function toggleCollapse(operatorCard) {
    const shopsContainer = operatorCard.querySelector('.operator-shops');
    const collapseIcon = operatorCard.querySelector('.collapse-icon');
    const operator = operatorCard.getAttribute('data-operator');
    
    // 添加或移除展开状态类
    operatorCard.classList.toggle('expanded');
    
    if (shopsContainer.style.display === 'none') {
        // 展开操作
        shopsContainer.style.display = 'block';
        collapseIcon.textContent = '▼';
        
        // 添加入场动画效果
        shopsContainer.style.animation = 'fadeIn 0.3s ease-out';
        
        // 检查是否已经有趋势图容器，如果没有则添加
        let trendContainer = operatorCard.querySelector('.operator-links-trend-container');
        if (!trendContainer) {
            const trendChartHTML = createOperatorLinksTrendChart(operator);
            shopsContainer.insertAdjacentHTML('afterbegin', trendChartHTML);
            
            // 加载趋势数据
            setTimeout(() => {
                refreshOperatorLinksTrend(operator);
            }, 100);
        }
        
        // 触发一次懒加载
        handleLazyLoad();
    } else {
        // 收起操作前先添加退出动画
        shopsContainer.style.animation = 'fadeOut 0.3s ease-in';
        
        // 等待动画完成后隐藏
        setTimeout(() => {
            shopsContainer.style.display = 'none';
            collapseIcon.textContent = '▶';
        }, 280); // 稍微短于动画时间，避免闪烁
    }
}

// 格式化日期，将YYYYMMDD转换为YYYY-MM-DD
function formatDate(dateString) {
    if (!dateString) return '无数据';
    if (dateString.length === 8) {
        const year = dateString.substring(0, 4);
        const month = dateString.substring(4, 6);
        const day = dateString.substring(6, 8);
        return `${year}-${month}-${day}`;
    }
    return dateString;
}

// 全局变量
window.allShops = window.allShops || [];
window.searchTerm = window.searchTerm || '';
window.currentView = window.currentView || 'card';
window.sortField = window.sortField || 'dailySales';
window.sortOrder = window.sortOrder || 'desc';

// 切换排序方式
function toggleSort(field) {
    if (field === window.sortField) {
        window.sortOrder = window.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
        window.sortField = field;
        window.sortOrder = 'desc';
    }
    renderShops();
}

// 搜索店铺
function searchShops() {
    const searchInput = document.getElementById('shop-search');
    if (searchInput) {
        window.searchTerm = searchInput.value.trim();
        renderShops();
    }
}

// 切换视图模式
function switchView(viewMode) {
    window.currentView = viewMode;
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.view === viewMode);
    });
    renderShops();
}

// 渲染店铺列表
async function renderShops() {
    const shopList = document.querySelector('.shop-list');
    const paginationContainer = document.getElementById('pagination-container');
    const loadingIndicator = document.getElementById('loading-indicator');

    if (!shopList || !paginationContainer) {
        console.error('Shop list or pagination container not found'); // Debug log
        if (loadingIndicator) loadingIndicator.style.display = 'none';
        return;
    }
    
    if (loadingIndicator) loadingIndicator.style.display = 'flex';

    shopList.innerHTML = '';
    
    console.log('Rendering shops. All shops count:', window.allShops.length); // Debug log
    console.log('Current search term:', window.searchTerm); // Debug log
    console.log('Current view:', window.currentView); // Debug log


    let filteredShops = window.allShops;
    if (window.searchTerm) {
        filteredShops = window.allShops.filter(shop => 
            shop.name.toLowerCase().includes(window.searchTerm.toLowerCase())
        );
    }
    console.log('Filtered shops count:', filteredShops.length); // Debug log


    // Sorting logic remains the same
    filteredShops.sort((a, b) => {
        let aValue, bValue;
        
        switch(window.sortField) {
            case 'name':
                aValue = a.name.toLowerCase();
                bValue = b.name.toLowerCase();
                return window.sortOrder === 'asc' ? 
                    aValue.localeCompare(bValue) : 
                    bValue.localeCompare(aValue);
            
            case 'operator':
                aValue = a.operator.toLowerCase();
                bValue = b.operator.toLowerCase();
                return window.sortOrder === 'asc' ? 
                    aValue.localeCompare(bValue) : 
                    bValue.localeCompare(aValue);
            
            case 'DSR':
                aValue = parseFloat(a.DSR) || 0;
                bValue = parseFloat(b.DSR) || 0;
                return window.sortOrder === 'asc' ? aValue - bValue : bValue - aValue;

            
            case 'dailySales':
                aValue = parseFloat(a.dailySales) || 0;
                bValue = parseFloat(b.dailySales) || 0;
                return window.sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
            
            case 'dailyOrders':
                aValue = parseInt(a.dailyOrders) || 0;
                bValue = parseInt(b.dailyOrders) || 0;
                return window.sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
            
            case 'dailyBuyers':
                aValue = parseInt(a.dailyBuyers) || 0;
                bValue = parseInt(b.dailyBuyers) || 0;
                return window.sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
            
            case 'conversionRate':
                aValue = parseFloat(a.conversionRate) || 0;
                bValue = parseFloat(b.conversionRate) || 0;
                return window.sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
            
            case 'refundAmount':
                aValue = parseFloat(a.refundAmount) || 0;
                bValue = parseFloat(b.refundAmount) || 0;
                return window.sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
            
            case 'refundOrders':
                aValue = parseInt(a.refundOrders) || 0;
                bValue = parseInt(b.refundOrders) || 0;
                return window.sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
            
            case 'refundRate':
                const aRefundRate = a.refundAmount && a.dailySales ? 
                    (a.refundAmount / a.dailySales * 100) : 0;
                const bRefundRate = b.refundAmount && b.dailySales ? 
                    (b.refundAmount / b.dailySales * 100) : 0;
                return window.sortOrder === 'asc' ? aRefundRate - bRefundRate : bRefundRate - aRefundRate;
            
            case 'productCount':
                aValue = parseInt(a.productCount) || 0;
                bValue = parseInt(b.productCount) || 0;
                return window.sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
            
            case 'rating':
                aValue = parseFloat(a.rating) || 0;
                bValue = parseFloat(b.rating) || 0;
                return window.sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
            
            case 'promoAmount':
                aValue = parseFloat(a.promoAmount) || 0;
                bValue = parseFloat(b.promoAmount) || 0;
                return window.sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
            
            default:
                return 0;
        }
    });
    
    if (window.currentView === 'list') {
        console.log('Rendering table view'); // Debug log
        shopList.innerHTML = `
            <div class="shop-table-container">
                <table class="shop-table">
                    <thead>
                        <tr>
                            <th class="sortable ${window.sortField === 'name' ? window.sortOrder : ''}" onclick="toggleSort('name')">店铺名称 <span class="sort-icon"></span></th>
                            <th class="sortable ${window.sortField === 'operator' ? window.sortOrder : ''}" onclick="toggleSort('operator')">负责运营 <span class="sort-icon"></span></th>
                            <th class="sortable ${window.sortField === 'DSR' ? window.sortOrder : ''}" onclick="toggleSort('DSR')">DSR <span class="sort-icon"></span></th>
                            <th class="sortable ${window.sortField === 'dailySales' ? window.sortOrder : ''}" onclick="toggleSort('dailySales')">销售额 <span class="sort-icon"></span></th>
                            <th class="sortable ${window.sortField === 'dailyOrders' ? window.sortOrder : ''}" onclick="toggleSort('dailyOrders')">订单数 <span class="sort-icon"></span></th>
                            <th class="sortable ${window.sortField === 'dailyBuyers' ? window.sortOrder : ''}" onclick="toggleSort('dailyBuyers')">买家数 <span class="sort-icon"></span></th>
                            <th class="sortable ${window.sortField === 'conversionRate' ? window.sortOrder : ''}" onclick="toggleSort('conversionRate')">转化率 <span class="sort-icon"></span></th>
                            <th class="sortable ${window.sortField === 'promoAmount' ? window.sortOrder : ''}" onclick="toggleSort('promoAmount')">推广费 <span class="sort-icon"></span></th>
                            <th class="sortable ${window.sortField === 'refundAmount' ? window.sortOrder : ''}" onclick="toggleSort('refundAmount')">退款金额 <span class="sort-icon"></span></th>
                            <th class="sortable ${window.sortField === 'refundOrders' ? window.sortOrder : ''}" onclick="toggleSort('refundOrders')">退款订单 <span class="sort-icon"></span></th>
                            <th class="sortable ${window.sortField === 'refundRate' ? window.sortOrder : ''}" onclick="toggleSort('refundRate')">退款率 <span class="sort-icon"></span></th>
                            <th class="sortable ${window.sortField === 'productCount' ? window.sortOrder : ''}" onclick="toggleSort('productCount')">新上链接 <span class="sort-icon"></span></th>
                            <th class="sortable ${window.sortField === 'rating' ? window.sortOrder : ''}" onclick="toggleSort('rating')">评分 <span class="sort-icon"></span></th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredShops.map(shop => generateShopList(shop)).join('')}
                    </tbody>
                </table>
            </div>
        `;
    } else { // Card view
        console.log('Rendering card view'); // Debug log
        const shopsByOperator = {};
        filteredShops.forEach(shop => {
            const operator = shop.operator || '未分配'; // Ensure operator is not undefined
            if (!shopsByOperator[operator]) {
                shopsByOperator[operator] = [];
            }
            shopsByOperator[operator].push(shop);
        });

        // 添加运营排序工具栏和搜索框
        shopList.innerHTML = `
            <div class="operator-sort-toolbar">
                <div class="search-section">
                    <div class="search-box">
                        <input type="text" id="shop-search" placeholder="搜索店铺..." value="${window.searchTerm || ''}" />
                        <button onclick="searchShops()"><i class="fas fa-search"></i> 搜索</button>
                    </div>
                </div>
                <div class="sort-section">
                    <span class="sort-label">运营排序:</span>
                    <button class="operator-sort-btn ${window.operatorSortField === 'totalSales' ? 'active' : ''}" data-sort="totalSales" onclick="sortOperators('totalSales')">销售额</button>
                    <button class="operator-sort-btn ${window.operatorSortField === 'totalOrders' ? 'active' : ''}" data-sort="totalOrders" onclick="sortOperators('totalOrders')">订单数</button>
                    <button class="operator-sort-btn ${window.operatorSortField === 'totalPromo' ? 'active' : ''}" data-sort="totalPromo" onclick="sortOperators('totalPromo')">推广费</button>
                    <button class="operator-sort-btn ${window.operatorSortField === 'totalROI' ? 'active' : ''}" data-sort="totalROI" onclick="sortOperators('totalROI')">ROI</button>
                    <button class="operator-sort-btn ${window.operatorSortField === 'totalNewLinks' ? 'active' : ''}" data-sort="totalNewLinks" onclick="sortOperators('totalNewLinks')">新上链接</button>
                </div>
            </div>
        `;

        // 计算每个运营商的汇总数据
        const operatorSummaries = [];
        for (const [operator, shops] of Object.entries(shopsByOperator)) {
            let totalSales = 0;
            let totalOrders = 0;
            let totalPromo = 0;
            let totalNewLinks = 0;
            
            shops.forEach(shop => {
                totalSales += shop.dailySales || 0;
                totalOrders += shop.dailyOrders || 0; 
                totalPromo += shop.promoAmount || 0;
                totalNewLinks += parseInt(shop.productCount) || 0;
            });
            
            const totalROI = totalPromo ? (totalSales / totalPromo) : 0;

            operatorSummaries.push({
                operator,
                shops,
                totalSales,
                totalOrders,
                totalPromo,
                totalROI,
                totalNewLinks
            });
        }

        // 排序运营商
        if (window.operatorSortField) {
            operatorSummaries.sort((a, b) => {
                let aValue, bValue;
                
                switch(window.operatorSortField) {
                    case 'totalSales':
                        aValue = a.totalSales;
                        bValue = b.totalSales;
                        break;
                    case 'totalOrders':
                        aValue = a.totalOrders;
                        bValue = b.totalOrders;
                        break;
                    case 'totalPromo':
                        aValue = a.totalPromo;
                        bValue = b.totalPromo;
                        break;
                    case 'totalROI':
                        aValue = a.totalROI;
                        bValue = b.totalROI;
                        break;
                    case 'totalNewLinks':
                        aValue = a.totalNewLinks;
                        bValue = b.totalNewLinks;
                        break;
                    default:
                        return 0;
                }
                
                return window.operatorSortOrder === 'asc' ? aValue - bValue : bValue - aValue;
            });
        }
        
        // 渲染运营商卡片
        const operatorCardsHTML = operatorSummaries.map(summary => 
            generateOperatorCard(summary.operator, summary.shops)
        ).join('');
        
        shopList.innerHTML += operatorCardsHTML;
    }
    
    if (filteredShops.length === 0 && window.allShops.length > 0) { // Data loaded, but search yielded no results
         shopList.innerHTML = '<div class="no-shops">沒有找到匹配的店铺</div>';
    } else if (window.allShops.length === 0) { // No data loaded at all (or API returned empty)
        shopList.innerHTML = '<div class="no-shops">暂无店铺数据，请检查日期或联系管理员</div>';
    }
    
    paginationContainer.innerHTML = `<div class="shop-count">共 ${filteredShops.length} 家店铺</div>`;

    if (loadingIndicator) loadingIndicator.style.display = 'none';
    
    // 初始触发一次懒加载，加载当前可见区域的卡片
    setTimeout(handleLazyLoad, 100);
}

// 添加对运营商排序的函数
function sortOperators(sortField) {
    // 如果点击当前排序字段，切换排序方向
    if (window.operatorSortField === sortField) {
        window.operatorSortOrder = window.operatorSortOrder === 'asc' ? 'desc' : 'asc';
    } else {
        window.operatorSortField = sortField;
        window.operatorSortOrder = 'desc'; // 默认降序
    }
    
    // 更新排序按钮状态
    const sortButtons = document.querySelectorAll('.operator-sort-btn');
    sortButtons.forEach(btn => {
        btn.classList.toggle('active', btn.getAttribute('data-sort') === sortField);
    });
    
    // 重新渲染店铺列表
    renderShops();
}

// 刷新数据
async function refreshData() {
    try {
        const shopData = await fetchShopData();
        window.allShops = shopData;
        renderShops();
        
        // 同时刷新全局链接趋势图
        refreshGlobalLinksTrend();
        
        // 刷新所有已展开的运营链接趋势图
        const expandedOperatorCards = document.querySelectorAll('.operator-card .operator-shops[style*="display: block"], .operator-card .operator-shops:not([style*="display: none"])');
        expandedOperatorCards.forEach(shopsContainer => {
            const operatorCard = shopsContainer.closest('.operator-card');
            const operator = operatorCard.getAttribute('data-operator');
            if (operator) {
                refreshOperatorLinksTrend(operator);
            }
        });
        
        // 如果有店铺详情弹窗打开，刷新店铺链接趋势图
        const modal = document.getElementById('shop-detail-modal');
        const modalShopName = document.getElementById('modal-shop-name');
        if (modal && modal.style.display === 'block' && modalShopName) {
            const currentTab = document.querySelector('.tab-button.active');
            if (currentTab && currentTab.textContent === '链接趋势') {
                fetchAndDisplayShopLinksTrend(modalShopName.textContent);
            }
        }
    } catch (error) {
        console.error('刷新数据失败:', error);
        document.querySelector('.shop-list').innerHTML = '<div class="no-shops">获取店铺数据失败，请稍后重试</div>';
    }
}

// 修改 addStyles 函数，添加排序工具栏的样式
function addStyles() {
    const styleElement = document.createElement('style');
    styleElement.textContent = `
        :root {
            --primary-color: #f1e9e3;
            --primary-light:rgb(103 151 255);
            --primary-dark:#aabee7;
            --accent-color: #ff4081;
            --text-primary: #212121;
            --text-secondary: #757575;
            --background-color: #f5f7fa;
            --card-color: #ffffff;
            --border-radius: 12px;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --danger-color: #f44336;
            --info-color: #2196f3;
            --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
            --shadow-md: 0 4px 8px rgba(0,0,0,0.1);
            --shadow-lg: 0 8px 16px rgba(0,0,0,0.1);
            --transition-speed: 0.3s;
            --tree-line-color: #e0e0e0;
            --tree-indent: 15px;
            --tree-connector-width: 2px;
        }
        
        body {
            background-color: var(--background-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-primary);
            margin: 0;
            padding: 0;
            line-height: 1.5;
        }
        
        /* 自定义滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #c1c9d6;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #a8b1c1;
        }
        
        .header {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 600;
            color: var(--primary-dark);
            margin: 0;
        }
        
        /* 控制面板样式 */
        .control-panel {
            background-color: var(--card-color);
            border-radius: var(--border-radius);
            padding: 15px 20px;
            margin-bottom: 20px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--shadow-sm);
        }
        
        .date-control {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
        }
        
        .date-range {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .date-range label {
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        .date-range input[type="date"] {
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #dbe1e6;
            background-color: white;
            font-size: 14px;
            font-family: inherit;
            cursor: pointer;
            transition: border-color 0.2s ease;
        }
        
        .date-range input[type="date"]:hover,
        .date-range input[type="date"]:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .search-box {
            display: flex;
            align-items: center;
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid #dbe1e6;
            background: white;
            transition: box-shadow 0.2s ease;
        }
        
        .search-box:hover,
        .search-box:focus-within {
            box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.2);
            border-color: var(--primary-color);
        }
        
        .search-box input {
            border: none;
            padding: 8px 12px;
            font-size: 14px;
            width: 200px;
            outline: none;
        }
        
        .search-box button {
            border: none;
            background: none;
            padding: 8px 12px;
            cursor: pointer;
            color: var(--primary-color);
            font-size: 16px;
            transition: background-color 0.2s ease;
        }
        
        .search-box button:hover {
            background-color: rgba(63, 81, 181, 0.1);
        }
        

        
        .view-btn {
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #dbe1e6;
            background-color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
            color: var(--text-secondary);
        }
        
        .view-btn:hover {
            background-color: rgba(63, 81, 181, 0.05);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .view-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .export-btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            background-color: var(--primary-color);
            color: #808080;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: background-color 0.2s ease;
        }
        
        .export-btn:hover {
            background-color: var(--primary-dark);
        }
        
        /* 店铺列表容器 */
        .shop-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 20px;
            padding: 0 5px;
        }
        
        /* 运营卡片 - 树状结构样式 */
        .operator-card {
            background-color: var(--card-color);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            transition: box-shadow 0.3s ease, transform 0.3s ease;
            margin-bottom: 4px;
        }
        
        .operator-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }
        
        .operator-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color:rgb(80, 80, 80);
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: var(--border-radius);
        }
        
        .operator-header:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-dark) 100%);
        }
        
        .operator-header-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .operator-name {
            font-size: 16px;
            font-weight: 600;
        }
        
        .operator-count {
            font-size: 13px;
            opacity: 0.8;
            background-color: rgba(255, 255, 255, 0.2);
            padding: 2px 8px;
            border-radius: 10px;
        }
        
        .collapse-icon {
            font-size: 12px;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.3s ease;
        }
        
        .operator-metrics {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .metric-mini {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 70px;
            background-color: rgba(255, 255, 255, 0.1);
            padding: 5px 8px;
            border-radius: 6px;
            transition: background-color 0.2s ease, transform 0.2s ease;
        }
        
        .metric-mini:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .metric-mini .metric-value {
            font-size: 15px;
            font-weight: 600;
        }
        
        .metric-mini .metric-label {
            font-size: 12px;
            opacity: 0.8;
            white-space: nowrap;
        }
        
        .operator-shops {
            padding: 10px 0px 10px var(--tree-indent);
            border-left: var(--tree-connector-width) solid var(--tree-line-color);
            margin-left: calc(var(--tree-indent) * 1.2);
            transition: all 0.3s ease;
        }
        
        /* 树状结构店铺项 */
        .shop-lazy-container {
            padding: 2px 0;
            position: relative;
        }
        
        .lazy-placeholder {
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-size: 13px;
            padding-left: calc(var(--tree-indent) * 0.5);
            gap: 5px;
        }
        
        .loading-dot {
            width: 8px;
            height: 8px;
            background-color: var(--primary-light);
            border-radius: 50%;
            display: inline-block;
            animation: pulse 1.2s infinite ease-in-out;
        }
        
        .loading-dot:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .loading-dot:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        @keyframes pulse {
            0%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
        }
        
        .tree-shop-item {
            display: flex;
            align-items: stretch;
            cursor: pointer;
            position: relative;
            margin: 5px 0;
            transition: transform 0.2s ease;
        }
        
        .tree-shop-connector {
            width: var(--tree-indent);
            position: relative;
            flex-shrink: 0;
        }
        
        .tree-branch {
            position: absolute;
            top: 15px;
            left: 0;
            width: var(--tree-indent);
            height: var(--tree-connector-width);
            background-color: var(--tree-line-color);
            transition: background-color 0.2s ease;
        }
        
        .tree-shop-item:hover .tree-branch {
            background-color: var(--primary-light);
        }
        
        .tree-shop-content {
            flex: 1;
            display: flex;
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 3px solid var(--primary-light);
            flex-wrap: wrap; /* 允许在小屏幕上换行 */
        }
        
        .tree-shop-item:hover .tree-shop-content {
            transform: translateX(5px);
            box-shadow: var(--shadow-md);
        }
        
        .shop-name-section {
            padding: 10px 15px;
            min-width: 150px;
            max-width: 350px; /* 增加最大宽度 */
            width: 30%; /* 设置为百分比宽度，根据内容自适应 */
            border-right: 1px solid rgba(0, 0, 0, 0.06);
            display: flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(to right, rgba(175, 132, 243, 0.1), rgba(63, 81, 181, 0.05));
            flex-shrink: 0; /* 防止被压缩 */
            flex-grow: 0; /* 防止过度扩张 */
            position: relative; /* 为绝对定位的子元素提供参考 */
        }
        
        .shop-name-container {
            display: flex;
            flex-direction: column;
            width: 100%; /* 确保容器占满父元素宽度 */
            overflow: hidden; /* 防止内容溢出 */
        }
        
        .shop-name {
            font-size: 14px;
            font-weight: 600;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: var(--primary-dark);
            max-width: 100%; /* 确保不超过容器 */
            padding-right: 10px; /* 为右侧留出空间 */
        }
        
        .shop-dsr {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .shop-metrics-row {
            display: flex;
            flex: 1;
            align-items: center;
            padding: 5px 15px;
            flex-wrap: wrap;
            overflow-x: auto; /* 允许横向滚动 */
        }
        
        .shop-metric-tree {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 5px 12px;
            min-width: 80px;
            transition: transform 0.2s ease, background-color 0.2s ease;
            border-radius: 6px;
            margin: 3px;
        }
        
        .shop-metric-tree:hover {
            transform: translateY(-2px);
            background-color: rgba(63, 81, 181, 0.05);
        }
        
        .metric-icon {
            font-size: 18px;
            margin-bottom: 4px;
        }
        
        .shop-metric-tree .metric-value {
            font-size: 15px;
            font-weight: 600;
        }
        
        .shop-metric-tree .metric-label {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 2px;
        }
        
        .shop-action {
            padding: 0 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-left: 1px solid rgba(0, 0, 0, 0.06);
        }
        
        .view-details-btn {
            padding: 5px 10px;
            border-radius: 4px;
            background-color: var(--primary-color);
            color: #808080;
            font-size: 12px;
            transition: background-color 0.2s ease;
            white-space: nowrap;
        }
        
        .view-details-btn:hover {
            background-color: var(--primary-dark);
        }
        
        /* 数据颜色样式 */
        .text-positive {
            color: var(--success-color);
        }
        
        .text-negative {
            color: var(--danger-color);
        }
        
        .text-warning {
            color: var(--warning-color);
        }
        
        .text-info {
            color: var(--info-color);
        }
        
        .text-highlight {
            color: var(--info-color);
            font-weight: 700;
        }
        
        /* 新上链接数据特殊高亮效果 */
        .highlight-glow {
            position: relative;
            text-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
            animation: glow 2s infinite alternate;
        }
        
        @keyframes glow {
            from {
                text-shadow: 0 0 3px rgba(76, 175, 80, 0.3);
            }
            to {
                text-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
            }
        }
        
        /* 加载指示器 */
        .loading-indicator {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(63, 81, 181, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 15px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* 表格视图 */
        .shop-table-container {
            overflow-x: auto;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
        }
        
        .shop-table {
            width: 100%;
            border-collapse: collapse;
            text-align: left;
        }
        
        .shop-table th {
            padding: 12px 15px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            font-weight: 500;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .shop-table td {
            padding: 12px 15px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.06);
        }
        
        .shop-table tbody tr:hover {
            background-color: rgba(63, 81, 181, 0.05);
        }
        
        .shop-table .sortable {
            cursor: pointer;
            position: relative;
            padding-right: 25px;
        }
        
        .sortable:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .sortable .sort-icon::after {
            content: '⇅';
            position: absolute;
            right: 8px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
        }
        
        .sortable.asc .sort-icon::after {
            content: '↑';
            color: white;
        }
        
        .sortable.desc .sort-icon::after {
            content: '↓';
            color: white;
        }
        
        /* 无数据提示 */
        .no-shops {
            text-align: center;
            padding: 30px;
            background-color: white;
            border-radius: var(--border-radius);
            color: var(--text-secondary);
            font-size: 16px;
            box-shadow: var(--shadow-sm);
        }
        
        /* 分页 */
        #pagination-container {
            display: flex;
            justify-content: center;
            padding: 10px 0;
        }
        
        .shop-count {
            padding: 8px 16px;
            background-color: white;
            border-radius: 20px;
            font-size: 14px;
            color: var(--text-secondary);
            box-shadow: var(--shadow-sm);
        }
        
        /* 响应式调整 */
        @media (max-width: 1200px) {
            .operator-metrics {
                gap: 10px;
            }
            
            .metric-mini {
                min-width: 60px;
                padding: 4px 6px;
            }
            
            .shop-metrics-row {
                justify-content: flex-start; /* 调整对齐方式 */
                flex: 1;
                overflow-x: hidden; /* 在小屏幕上隐藏溢出内容 */
            }
            
            .shop-action {
                padding: 0 8px;
            }
        }
        
        @media (max-width: 992px) {
            .control-panel {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
            
            .date-control, .search-box {
                width: 100%;
            }
            
            .search-box input {
                width: 100%;
                flex: 1;
            }
            
            .shop-metrics-row {
                flex-wrap: wrap;
                justify-content: space-around; /* 更好的间距分布 */
            }
            
            .shop-metric-tree {
                min-width: 70px;
                padding: 5px 8px;
                flex: 0 0 calc(33.333% - 10px); /* 在中等屏幕上一行显示3个 */
                margin: 5px;
            }
        }
        
        @media (max-width: 768px) {
            .operator-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .operator-metrics {
                width: 100%;
                justify-content: space-between;
            }
            
            .tree-shop-content {
                flex-direction: column;
                width: 100%;
            }
            
            .shop-name-section {
                max-width: 100%;
                width: 100%;
                border-right: none;
                border-bottom: 1px solid rgba(0, 0, 0, 0.06);
            }
            
            .shop-metrics-row {
                justify-content: space-around;
                padding: 10px;
                width: 100%;
            }
            
            .shop-metric-tree {
                flex: 0 0 calc(50% - 10px); /* 在小屏幕上一行显示2个 */
            }
            
            .shop-action {
                width: 100%;
                padding: 10px;
                border-left: none;
                border-top: 1px solid rgba(0, 0, 0, 0.06);
                justify-content: center; /* 按钮居中 */
            }
            
            .view-details-btn {
                width: 100%;
                text-align: center;
                padding: 8px;
            }
        }
        
        /* 折叠动画 */
        .expanded .collapse-icon {
            transform: rotate(90deg);
        }
        
        /* 折叠展开动画 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes fadeOut {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(-10px); }
        }
        
        .operator-shops {
            transition: max-height 0.3s ease-out;
            overflow: hidden;
        }
        
        .expanded .collapse-icon {
            transform: rotate(90deg);
            transition: transform 0.3s ease;
        }
        
        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
            padding: 20px;
            animation: modalFadeIn 0.3s ease-out;
        }
        
        @keyframes modalFadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }
        
        .modal-content {
            background-color: white;
            border-radius: var(--border-radius);
            width: 90%;
            max-width: 1000px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: var(--shadow-lg);
            position: relative;
            animation: modalSlideIn 0.3s ease-out;
        }
        
        @keyframes modalSlideIn {
            from {
                transform: translateY(-30px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.06);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            z-index: 10;
            border-top-left-radius: var(--border-radius);
            border-top-right-radius: var(--border-radius);
        }
        
        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: white;
            margin: 0;
        }
        
        #modal-shop-name {
            font-size: 20px;
            font-weight: 600;
            color: white;
            margin: 0;
        }
        
        .close-modal {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            cursor: pointer;
            font-size: 22px;
            color: white;
            padding: 5px;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: background-color 0.2s ease;
        }
        
        .close-modal:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .tabs {
            display: flex;
            border-bottom: 1px solid rgba(0, 0, 0, 0.06);
            margin-bottom: 20px;
            background-color: #f5f7fa;
        }
        
        .tab-button {
            padding: 12px 20px;
            cursor: pointer;
            font-size: 15px;
            color: var(--text-secondary);
            background: none;
            border: none;
            border-bottom: 3px solid transparent;
            transition: all 0.2s ease;
            font-weight: 500;
        }
        
        .tab-button.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }
        
        .tab-button:hover:not(.active) {
            background-color: rgba(0, 0, 0, 0.02);
            color: var(--text-primary);
        }
        
        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease-out;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .chart-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .chart-type-selector select {
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #dbe1e6;
            background-color: white;
            font-size: 14px;
            cursor: pointer;
            transition: border-color 0.2s ease;
            min-width: 120px;
        }
        
        .chart-type-selector select:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .chart-period-selector {
            display: flex;
            gap: 5px;
        }
        
        .period-btn {
            padding: 6px 12px;
            border-radius: 6px;
            border: 1px solid #dbe1e6;
            background-color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
            color: var(--text-secondary);
        }
        
        .period-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .period-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .table-controls {
            margin-bottom: 15px;
        }
        
        .table-container {
            overflow-x: auto;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: var(--shadow-sm);
        }
        
        #detail-table {
            width: 100%;
            border-collapse: collapse;
            text-align: left;
        }
        
        #detail-table th {
            padding: 12px 15px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            font-weight: 500;
            position: sticky;
            top: 0;
            z-index: 1;
        }
        
        #detail-table td {
            padding: 12px 15px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.06);
        }
        
        #detail-table tr:nth-child(even) {
            background-color: rgba(0, 0, 0, 0.01);
        }
        
        #detail-table tr:hover td {
            background-color: rgba(63, 81, 181, 0.05);
        }
        
        .loading-data, .no-data, .error-message {
            text-align: center;
            padding: 30px;
            color: var(--text-secondary);
        }
        
        .error-message {
            color: var(--danger-color);
        }
        
        /* 响应式模态框 */
        @media (max-width: 768px) {
            .modal-content {
                width: 95%;
                max-height: 95vh;
            }
            
            .chart-controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .chart-type-selector, .chart-period-selector {
                width: 100%;
            }
            
            .chart-period-selector {
                justify-content: space-between;
            }
            
            .period-btn {
                flex: 1;
                text-align: center;
            }
            
            .tabs {
                overflow-x: auto;
                white-space: nowrap;
                padding-bottom: 5px;
            }
        }
        

        .shop-metric-tree:before {
            position: absolute;
            top: -5px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
        }
        
        .view-details-btn {
            padding: 5px 10px;
            border-radius: 4px;
            background-color: var(--primary-color);
            color: #808080;
            font-size: 12px;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s ease;
            white-space: nowrap;
            display: inline-block;
            text-align: center;
        }
        
        .view-details-btn:hover {
            background-color: var(--primary-dark);
        }
        
        .sortable-metric {
            cursor: pointer;
            position: relative;
            transition: background-color 0.2s ease;
        }
        
        .sortable-metric:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
        
        .sort-indicator {
            margin-left: 3px;
            font-weight: bold;
        }
        
        /* 运营商排序工具栏样式 */
        .operator-sort-toolbar {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            background-color: white;
            border-radius: var(--border-radius);
            margin-bottom: 15px;
            box-shadow: var(--shadow-sm);
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .sort-label {
            font-weight: 500;
            color: var(--text-secondary);
            margin-right: 10px;
            white-space: nowrap;
        }
        
        .operator-sort-btn {
            padding: 6px 12px;
            border-radius: 6px;
            border: 1px solid #dbe1e6;
            background-color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
            color: var(--text-secondary);
        }
        
        .operator-sort-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            background-color: rgba(63, 81, 181, 0.05);
        }
        
        .operator-sort-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .operator-sort-toolbar {
                flex-direction: column;
                align-items: flex-start;
                padding: 10px;
            }
            
            .operator-sort-btn {
                width: 100%;
                text-align: center;
            }
        }
        
        /* 添加新的深色文本类 */
        .text-dark {
            color: #333333;
            font-weight: 600;
        }
        
        /* 运营商排序工具栏样式修改，添加搜索框样式 */
        .operator-sort-toolbar {
            display: flex;
            align-items: center;
            padding: 15px;
            background-color: white;
            border-radius: var(--border-radius);
            margin-bottom: 15px;
            box-shadow: var(--shadow-sm);
            flex-wrap: wrap;
            gap: 15px;
            justify-content: space-between;
        }
        
        .search-section {
            flex: 1;
            min-width: 250px;
        }
        
        .sort-section {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .search-box {
            display: flex;
            align-items: center;
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid #dbe1e6;
            background: white;
            transition: box-shadow 0.2s ease;
            max-width: 350px;
        }
        
        .search-box:hover,
        .search-box:focus-within {
            box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.2);
            border-color: var(--primary-color);
        }
        
        .search-box input {
            border: none;
            padding: 8px 12px;
            font-size: 14px;
            width: 100%;
            outline: none;
        }
        
        .search-box button {
            border: none;
            background: none;
            padding: 8px 12px;
            cursor: pointer;
            color: #808080;
            font-size: 16px;
            transition: background-color 0.2s ease;
            background-color: var(--primary-color);
        }
        
        .search-box button:hover {
            background-color: var(--primary-dark);
        }
        
        /* 链接趋势图样式 */
        .global-links-trend-container,
        .operator-links-trend-container {
            margin-bottom: 20px;
        }
        
        .trend-chart-card {
            background-color: var(--card-color);
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--shadow-sm);
            border: 1px solid #e0e7ff;
        }
        
        .trend-chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .trend-chart-header h3,
        .trend-chart-header h4 {
            margin: 0;
            color: var(--text-primary);
            font-weight: 600;
        }
        
        .trend-chart-header h3 {
            font-size: 18px;
        }
        
        .trend-chart-header h4 {
            font-size: 16px;
        }
        
        .refresh-trend-btn {
            padding: 6px 12px;
            border: 1px solid #d0d7de;
            background-color: white;
            border-radius: 6px;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: all 0.2s ease;
        }
        
        .refresh-trend-btn:hover {
            background-color: #f6f8fa;
            border-color: #d0d7de;
            color: var(--text-primary);
        }
        
        .trend-chart-container {
            position: relative;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .links-trend-description {
            margin-bottom: 15px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid var(--primary-color);
        }
        
        .links-trend-description h4 {
            margin: 0 0 8px 0;
            color: var(--text-primary);
            font-size: 16px;
            font-weight: 600;
        }
        
        .links-trend-description p {
            margin: 0;
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.4;
        }
        
        .no-data,
        .error-message {
            text-align: center;
            color: var(--text-secondary);
            font-size: 14px;
            padding: 40px 20px;
        }
        
        .error-message {
            color: var(--danger-color);
        }
        
        .loading-data {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 10px;
            color: var(--text-secondary);
            font-size: 14px;
        }
        
        .loading-data .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(styleElement);
}

// 将函数暴露到全局作用域
window.toggleSort = toggleSort;
window.searchShops = searchShops;
window.switchView = switchView;
window.refreshData = refreshData;
window.exportROIData = exportROIData;
window.toggleCollapse = toggleCollapse;
window.sortOperatorShops = sortOperatorShops; // 排序店铺函数
window.sortOperators = sortOperators; // 排序运营商函数
window.refreshGlobalLinksTrend = refreshGlobalLinksTrend; // 刷新全局链接趋势
window.refreshOperatorLinksTrend = refreshOperatorLinksTrend; // 刷新运营链接趋势

// 添加全局变量
window.operatorSortField = '';
window.operatorSortOrder = 'desc';

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
    if(checkLoginStatus()) {
        updateShopList();
    }
});

// 创建店铺详情模态框
function createShopDetailModal() {
    return `
    <div id="shop-detail-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-shop-name">店铺详情</h2>
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="tabs">
                    <button class="tab-button active" data-tab="trend">数据趋势</button>
                    <button class="tab-button" data-tab="linksTrend">链接趋势</button>
                    <button class="tab-button" data-tab="table">详细数据</button>
                </div>
                
                <div class="tab-content active" id="trend-tab">
                    <div class="chart-controls">
                        <div class="chart-type-selector">
                            <select id="chart-data-type">
                                <option value="sales">销售额</option>
                                <option value="orders">订单数</option>
                                <option value="buyers">买家数</option>
                                <option value="promoAmount">推广费</option>
                                <option value="refundRate">退款率</option>
                                <option value="conversionRate">转化率</option>
                            </select>
                        </div>
                        <div class="chart-period-selector">
                            <button class="period-btn active" data-period="7">7天</button>
                            <button class="period-btn" data-period="14">14天</button>
                            <button class="period-btn" data-period="30">30天</button>
                        </div>
                    </div>
                    <div id="chart-container" style="width: 100%; height: 350px;">
                        <div class="loading-data">
                            <div class="spinner"></div>
                            <p>正在加载数据...</p>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="linksTrend-tab" style="display: none;">
                    <div class="links-trend-description">
                        <h4>近30日新上链接数量趋势</h4>
                        <p>显示该店铺近30天每日新增链接的数量变化</p>
                    </div>
                    <div id="shop-links-trend-chart" style="width: 100%; height: 350px;">
                        <div class="loading-data">
                            <div class="spinner"></div>
                            <p>正在加载链接趋势数据...</p>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="table-tab" style="display: none;">
                    <div class="table-controls">
                        <button id="export-csv-btn" class="export-btn">导出CSV</button>
                    </div>
                    <div class="table-container">
                        <table id="detail-table">
                            <thead>
                                <tr>
                                    <th>日期</th>
                                    <th>销售额</th>
                                    <th>订单数</th>
                                    <th>买家数</th>
                                    <th>推广费</th>
                                    <th>退款率</th>
                                    <th>转化率</th>
                                </tr>
                            </thead>
                            <tbody id="detail-table-body">
                                <tr>
                                    <td colspan="7" class="loading-data">加载中...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    `;
}

// 显示店铺详情
// 存储当前请求的标识符，用于防止竞态条件
let currentRequestId = null;

async function showShopDetails(shopName) {
    // 生成新的请求标识符
    const requestId = Date.now() + '_' + Math.random();
    currentRequestId = requestId;
    
    // 获取模态框
    const modal = document.getElementById('shop-detail-modal');
    
    // 清理之前的状态和数据
    clearModalData();
    
    // 设置模态框标题
    document.getElementById('modal-shop-name').innerText = shopName;
    
    // 显示模态框
    modal.style.display = 'flex';
    
    // 确保模态框动画正确显示
    setTimeout(() => {
        const modalContent = modal.querySelector('.modal-content');
        if (modalContent) {
            modalContent.style.animation = 'modalSlideIn 0.3s ease-out';
        }
    }, 10);
    
    // 重置到默认标签（趋势图）
    resetToDefaultTab();
    
    // 获取并显示默认的趋势数据
    if (currentRequestId === requestId) {
        await fetchAndDisplayTrendData(shopName, requestId);
    }
    
    // 清除之前的事件监听器，重新绑定
    bindModalEvents(shopName);
}

// 清理模态框数据
function clearModalData() {
    // 清空图表容器
    const chartContainer = document.getElementById('chart-container');
    if (chartContainer) {
        chartContainer.innerHTML = '<div class="loading-data">加载中...</div>';
    }
    
    // 清空表格数据
    const tableBody = document.getElementById('detail-table-body');
    if (tableBody) {
        tableBody.innerHTML = '<tr><td colspan="7" class="loading-data">加载中...</td></tr>';
    }
    
    // 清空链接趋势图
    const linksTrendChart = document.getElementById('shop-links-trend-chart');
    if (linksTrendChart) {
        linksTrendChart.innerHTML = '<div class="loading-data">正在加载链接趋势数据...</div>';
    }
}

// 重置到默认标签
function resetToDefaultTab() {
    // 重置所有标签按钮状态
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(btn => btn.classList.remove('active'));
    
    // 设置第一个标签为激活状态
    const firstTab = tabButtons[0];
    if (firstTab) {
        firstTab.classList.add('active');
    }
    
    // 隐藏所有标签内容
    document.querySelectorAll('.tab-content').forEach(content => {
        content.style.display = 'none';
    });
    
    // 显示默认标签内容（趋势图）
    const trendTab = document.getElementById('trend-tab');
    if (trendTab) {
        trendTab.style.display = 'block';
    }
}

// 绑定模态框事件
function bindModalEvents(shopName) {
    const modal = document.getElementById('shop-detail-modal');
    
    // 移除之前可能存在的事件监听器
    const oldCloseBtn = document.querySelector('.close-modal');
    if (oldCloseBtn) {
        oldCloseBtn.replaceWith(oldCloseBtn.cloneNode(true));
    }
    
    // 绑定关闭按钮事件
    const closeBtn = document.querySelector('.close-modal');
    if (closeBtn) {
        closeBtn.onclick = function() {
            modal.style.display = 'none';
            currentRequestId = null; // 清除请求标识符
        };
    }
    
    // 点击模态框外部关闭
    const newWindowClickHandler = function(event) {
        if (event.target == modal) {
            modal.style.display = 'none';
            currentRequestId = null; // 清除请求标识符
        }
    };
    
    // 移除旧的事件监听器，添加新的
    window.removeEventListener('click', window.modalClickHandler);
    window.addEventListener('click', newWindowClickHandler);
    window.modalClickHandler = newWindowClickHandler;
    
    // 重新绑定标签切换事件
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        // 克隆节点来移除旧的事件监听器
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
    });
    
    // 为新的标签按钮绑定事件
    const newTabButtons = document.querySelectorAll('.tab-button');
    newTabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            
            // 切换标签按钮的激活状态
            newTabButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // 切换标签内容的显示
            document.querySelectorAll('.tab-content').forEach(content => {
                content.style.display = 'none';
            });
            
            const activeTab = document.getElementById(tabName + '-tab');
            if (activeTab) {
                activeTab.style.display = 'block';
                // 添加淡入动画
                activeTab.style.animation = 'fadeIn 0.3s ease-out';
            }
            
            // 根据标签类型加载相应数据
            if (tabName === 'table') {
                fetchAndDisplayTableData(shopName);
            } else if (tabName === 'linksTrend') {
                fetchAndDisplayShopLinksTrend(shopName);
            }
        });
    });
    
    // 重新绑定数据类型选择事件
    const chartDataType = document.getElementById('chart-data-type');
    if (chartDataType) {
        // 克隆节点来移除旧的事件监听器
        const newChartDataType = chartDataType.cloneNode(true);
        chartDataType.parentNode.replaceChild(newChartDataType, chartDataType);
        
        // 绑定新的事件监听器
        document.getElementById('chart-data-type').addEventListener('change', function() {
            fetchAndDisplayTrendData(shopName, currentRequestId);
        });
    }
    
    // 重新绑定周期选择按钮事件
    const periodButtons = document.querySelectorAll('.period-btn');
    periodButtons.forEach(button => {
        // 克隆节点来移除旧的事件监听器
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
    });
    
    // 为新的周期按钮绑定事件
    const newPeriodButtons = document.querySelectorAll('.period-btn');
    newPeriodButtons.forEach(button => {
        button.addEventListener('click', function() {
            newPeriodButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            fetchAndDisplayTrendData(shopName, currentRequestId);
        });
    });
    
    // 重新绑定导出CSV按钮事件
    const exportBtn = document.getElementById('export-csv-btn');
    if (exportBtn) {
        // 克隆节点来移除旧的事件监听器
        const newExportBtn = exportBtn.cloneNode(true);
        exportBtn.parentNode.replaceChild(newExportBtn, exportBtn);
        
        // 绑定新的事件监听器
        document.getElementById('export-csv-btn').addEventListener('click', function() {
            exportTableToCSV(shopName);
        });
    }
}

// 获取并显示趋势数据
async function fetchAndDisplayTrendData(shopName, requestId = null) {
    try {
        // 检查请求是否仍然有效
        if (requestId && currentRequestId !== requestId) {
            console.log('请求已过期，忽略结果');
            return;
        }
        
        // 获取选中的数据类型和周期
        const dataType = document.getElementById('chart-data-type')?.value || 'sales';
        const activePeriodBtn = document.querySelector('.period-btn.active');
        const days = activePeriodBtn ? parseInt(activePeriodBtn.getAttribute('data-period')) : 7;
        
        // 显示加载状态
        const chartContainer = document.getElementById('chart-container');
        if (chartContainer) {
            chartContainer.innerHTML = '<div class="loading-data">加载趋势数据中...</div>';
        }
        
        // 从服务器获取趋势数据
        const response = await fetch(`/api/shop-trend?shop=${encodeURIComponent(shopName)}&days=${days}`);
        
        // 再次检查请求是否仍然有效
        if (requestId && currentRequestId !== requestId) {
            console.log('请求已过期，忽略结果');
            return;
        }
        
        const result = await response.json();
        
        // 最后检查请求是否仍然有效
        if (requestId && currentRequestId !== requestId) {
            console.log('请求已过期，忽略结果');
            return;
        }
        
        if (result.success) {
            renderChart(result.data, dataType, shopName);
        } else {
            if (chartContainer) {
                chartContainer.innerHTML = '<div class="error-message">获取趋势数据失败</div>';
            }
        }
    } catch (error) {
        console.error('获取趋势数据失败:', error);
        
        // 检查请求是否仍然有效再显示错误
        if (!requestId || currentRequestId === requestId) {
            const chartContainer = document.getElementById('chart-container');
            if (chartContainer) {
                chartContainer.innerHTML = '<div class="error-message">获取趋势数据失败</div>';
            }
        }
    }
}

// 渲染图表
function renderChart(data, dataType, shopName) {
    const chartContainer = document.getElementById('chart-container');
    
    // 验证当前显示的店铺是否匹配
    const currentShopName = document.getElementById('modal-shop-name')?.innerText;
    if (currentShopName !== shopName) {
        console.log('店铺已切换，忽略图表渲染');
        return;
    }
    
    if (!chartContainer) {
        console.error('图表容器不存在');
        return;
    }
    
    chartContainer.innerHTML = '';
    
    // 如果没有数据，显示提示信息
    if (!data || data.length === 0) {
        chartContainer.innerHTML = '<div class="no-data">暂无数据</div>';
        return;
    }
    
    // 准备图表数据
    const dates = data.map(item => formatDate(item.date));
    let chartValues;
    let chartTitle;
    
    switch(dataType) {
        case 'sales':
            chartValues = data.map(item => item.salesAmount);
            chartTitle = "销售额 (¥)";
            break;
        case 'orders':
            chartValues = data.map(item => item.ordersCount);
            chartTitle = "订单数";
            break;
        case 'buyers':
            chartValues = data.map(item => item.buyersCount);
            chartTitle = "买家数";
            break;
        case 'promoAmount':
            chartValues = data.map(item => item.promoAmount);
            chartTitle = "推广费 (¥)";
            break;
        case 'refundRate':
            chartValues = data.map(item => {
                return item.refundAmount && item.salesAmount ? 
                    (item.refundAmount / item.salesAmount * 100).toFixed(2) : 0;
            });
            chartTitle = "退款率 (%)";
            break;
        case 'conversionRate':
            chartValues = data.map(item => item.conversionRate);
            chartTitle = "转化率 (%)";
            break;
        default:
            chartValues = data.map(item => item.salesAmount);
            chartTitle = "销售额 (¥)";
    }
    
    // 创建图表的canvas元素
    const canvas = document.createElement('canvas');
    chartContainer.appendChild(canvas);
    
    // 渲染图表
    const ctx = canvas.getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [{
                label: chartTitle,
                data: chartValues,
                borderColor: '#4b6cb7',
                backgroundColor: 'rgba(75, 108, 183, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.3
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: `${shopName} - ${chartTitle}趋势`,
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            if (dataType === 'sales' || dataType === 'promoAmount') {
                                return '¥' + value.toLocaleString();
                            } else if (dataType === 'refundRate' || dataType === 'conversionRate') {
                                return value + '%';
                            }
                            return value;
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

// 获取并显示表格数据
async function fetchAndDisplayTableData(shopName) {
    const tableBody = document.getElementById('detail-table-body');
    
    // 检查当前请求是否仍然有效
    const requestStartTime = Date.now();
    
    if (tableBody) {
        tableBody.innerHTML = '<tr><td colspan="7" class="loading-data">加载中...</td></tr>';
    }
    
    try {
        // 获取最大天数的数据
        const response = await fetch(`/api/shop-trend?shop=${encodeURIComponent(shopName)}&days=30`);
        
        // 检查请求是否仍然有效（通过模态框标题验证）
        const currentShopName = document.getElementById('modal-shop-name')?.innerText;
        if (currentShopName !== shopName) {
            console.log('店铺已切换，忽略表格数据结果');
            return;
        }
        
        const result = await response.json();
        
        // 再次检查
        const currentShopNameAfter = document.getElementById('modal-shop-name')?.innerText;
        if (currentShopNameAfter !== shopName) {
            console.log('店铺已切换，忽略表格数据结果');
            return;
        }
        
        if (result.success) {
            renderTable(result.data);
        } else {
            if (tableBody) {
                tableBody.innerHTML = '<tr><td colspan="7" class="error-message">获取表格数据失败</td></tr>';
            }
        }
    } catch (error) {
        console.error('获取表格数据失败:', error);
        
        // 检查是否仍然是当前店铺
        const currentShopName = document.getElementById('modal-shop-name')?.innerText;
        if (currentShopName === shopName && tableBody) {
            tableBody.innerHTML = '<tr><td colspan="7" class="error-message">获取表格数据失败</td></tr>';
        }
    }
}

// 渲染表格
function renderTable(data) {
    const tableBody = document.getElementById('detail-table-body');
    
    if (!tableBody) {
        console.error('表格容器不存在');
        return;
    }
    
    tableBody.innerHTML = '';
    
    // 如果没有数据，显示提示信息
    if (!data || data.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="7" class="no-data">暂无数据</td></tr>';
        return;
    }
    
    // 按日期排序，最新的在前面
    data.sort((a, b) => b.date - a.date);
    
    // 填充表格
    data.forEach(item => {
        const refundRate = item.refundAmount && item.salesAmount ? 
            (item.refundAmount / item.salesAmount * 100).toFixed(2) : '0.00';
        
        tableBody.innerHTML += `
            <tr>
                <td>${formatDate(item.date)}</td>
                <td>¥${item.salesAmount.toLocaleString()}</td>
                <td>${item.ordersCount}</td>
                <td>${item.buyersCount}</td>
                <td>¥${item.promoAmount.toLocaleString()}</td>
                <td>${refundRate}%</td>
                <td>${item.conversionRate.toFixed(2)}%</td>
            </tr>
        `;
    });
}

// 导出表格数据为CSV
function exportTableToCSV(shopName) {
    const table = document.getElementById('detail-table');
    const rows = table.querySelectorAll('tr');
    
    // 如果表格中有"暂无数据"或"加载中"，则不导出
    if (table.querySelector('.no-data') || table.querySelector('.loading-data')) {
        alert('暂无可导出的数据');
        return;
    }
    
    let csv = [];
    
    // 获取表头
    const headers = [];
    const headerCells = rows[0].querySelectorAll('th');
    headerCells.forEach(cell => {
        headers.push(cell.innerText);
    });
    csv.push(headers.join(','));
    
    // 获取数据行
    for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        const cols = row.querySelectorAll('td');
        
        if (cols.length) {
            let rowData = [];
            cols.forEach(col => {
                // 移除货币符号和千分位逗号
                let cellValue = col.innerText.replace('¥', '').replace(/,/g, '');
                // 移除百分比符号
                cellValue = cellValue.replace('%', '');
                rowData.push(cellValue);
            });
            csv.push(rowData.join(','));
        }
    }
    
    // 创建CSV文件并下载
    const csvContent = 'data:text/csv;charset=utf-8,' + csv.join('\n');
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement('a');
    
    // 设置下载文件名
    const today = new Date();
    const dateStr = today.getFullYear() + 
        ('0' + (today.getMonth() + 1)).slice(-2) + 
        ('0' + today.getDate()).slice(-2);
    
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', `${shopName}数据_${dateStr}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 创建控制面板
function createControlPanel() {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);

    const formatDate = (date) => {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };
    
    const yesterdayFormatted = formatDate(yesterday);
    
    return `
    <div class="control-panel">
            <div class="date-control">
                <div id="date-range-container" class="date-range">
                    <label for="start-date-picker">开始日期:</label>
                    <input type="date" id="start-date-picker" value="${yesterdayFormatted}" max="${formatDate(today)}" onChange="refreshData()">
                    <label for="end-date-picker">结束日期:</label>
                    <input type="date" id="end-date-picker" value="${yesterdayFormatted}" max="${formatDate(today)}" onChange="refreshData()">
            </div>
            </div>
            
            <button class="export-btn" onClick="exportROIData()">
                <span>导出ROI数据</span>
            </button>
    </div>
    `;
}

// 修改导出ROI数据的函数支持日期范围
function exportROIData() {
    try {
        // 获取日期模式
        const singleModeRadio = document.querySelector('input[name="dateMode"][value="single"]');
        const isSingleMode = singleModeRadio && singleModeRadio.checked;
        
        let dateText = '';
        let formattedDate = '';
        
        if (isSingleMode) {
            // 获取单日日期
        const datePicker = document.getElementById('date-picker');
        const selectedDate = datePicker ? datePicker.value : '';
        
        if (!selectedDate) {
            alert('请先选择日期');
            return;
        }
        
            dateText = selectedDate;
            formattedDate = selectedDate.replace(/-/g, '');
        } else {
            // 获取日期范围
            const startDatePicker = document.getElementById('start-date-picker');
            const endDatePicker = document.getElementById('end-date-picker');
            const startDate = startDatePicker ? startDatePicker.value : '';
            const endDate = endDatePicker ? endDatePicker.value : '';
            
            if (!startDate || !endDate) {
                alert('请先选择开始和结束日期');
                return;
            }
            
            dateText = `${startDate}至${endDate}`;
            formattedDate = `${startDate.replace(/-/g, '')}-${endDate.replace(/-/g, '')}`;
        }
        
        // 准备CSV数据
        let csvContent = '运营,店铺,净销售额,推广费,日期\n';
        
        // 过滤和处理数据
        window.allShops.forEach(shop => {
            // 计算净销售额（销售额减去退款金额）
            const netSales = (parseFloat(shop.dailySales) || 0) - (parseFloat(shop.refundAmount) || 0);
            
            // 添加到CSV
            csvContent += `${shop.operator},${shop.name},${netSales.toFixed(2)},${parseFloat(shop.promoAmount).toFixed(2)},${dateText}\n`;
        });
        
        // 创建下载链接
        const encodedUri = encodeURI('data:text/csv;charset=utf-8,' + csvContent);
        const link = document.createElement('a');
        link.setAttribute('href', encodedUri);
        link.setAttribute('download', `ROI数据_${formattedDate}.csv`);
        document.body.appendChild(link);
        
        // 触发下载
        link.click();
        
        // 清理
        document.body.removeChild(link);
        
    } catch (error) {
        console.error('导出ROI数据失败:', error);
        alert('导出ROI数据失败，请稍后重试');
    }
}

// 更新店铺列表
async function updateShopList() {
    const container = document.querySelector('.container');
    if (!container) return;

    addStyles();
    
    container.innerHTML = '<div class="header"><h1>宜承-店铺数据管理系统</h1></div>';
    
    // 添加全局链接趋势图
    container.innerHTML += createGlobalLinksTrendChart();
    
    if (!document.getElementById('loading-indicator')) {
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'loading-indicator';
        loadingDiv.className = 'loading-indicator';
        loadingDiv.style.display = 'none'; 
        loadingDiv.innerHTML = '<div class="spinner"></div><span>加载中...</span>';
        document.body.appendChild(loadingDiv);
    }
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) loadingIndicator.style.display = 'flex';

    container.innerHTML += createControlPanel();
    container.innerHTML += '<div class="shop-list"></div>';
    container.innerHTML += '<div id="pagination-container"></div>';
    container.innerHTML += createShopDetailModal();
    
    // 加载全局链接趋势数据
    setTimeout(() => {
        refreshGlobalLinksTrend();
    }, 500);
    
    try {
        const shopData = await fetchShopData();
        window.allShops = shopData; 
        console.log('window.allShops populated:', window.allShops); // Debug log
        renderShops(); 
    } catch (error) {
        console.error('获取店铺数据失败 (updateShopList):', error); // Debug log
        document.querySelector('.shop-list').innerHTML = '<div class="no-shops">获取店铺数据失败，请稍后重试</div>';
        if (loadingIndicator) loadingIndicator.style.display = 'none';
    }
} 

// 添加排序运营人员的店铺的函数
function sortOperatorShops(operator, sortField) {
    // 阻止冒泡，避免点击时触发折叠/展开
    event.stopPropagation();
    
    // 获取该运营下的所有店铺
    let operatorShops = window.allShops.filter(shop => 
        (shop.operator || '未分配') === operator
    );
    
    // 获取当前排序状态
    const operatorCard = document.querySelector(`.operator-card[data-operator="${operator}"]`);
    const currentSortField = operatorCard.getAttribute('data-sort-field') || '';
    const currentSortOrder = operatorCard.getAttribute('data-sort-order') || 'desc';
    
    // 确定新的排序方向
    const newSortOrder = (currentSortField === sortField && currentSortOrder === 'desc') ? 'asc' : 'desc';
    
    // 保存新的排序状态
    operatorCard.setAttribute('data-sort-field', sortField);
    operatorCard.setAttribute('data-sort-order', newSortOrder);
    
    // 更新UI，添加排序指示器
    const sortIndicators = operatorCard.querySelectorAll('.sort-indicator');
    sortIndicators.forEach(indicator => {
        indicator.parentNode.removeChild(indicator);
    });
    
    // 为当前排序字段添加排序指示器
    const metricElement = operatorCard.querySelector(`.sortable-metric[onclick*="${sortField}"]`);
    if (metricElement) {
        const indicator = document.createElement('span');
        indicator.className = 'sort-indicator';
        indicator.innerHTML = newSortOrder === 'desc' ? ' ↓' : ' ↑';
        metricElement.querySelector('.metric-label').appendChild(indicator);
    }
    
    // 根据字段排序
    operatorShops.sort((a, b) => {
        let aValue, bValue;
        
        switch(sortField) {
            case 'dailySales':
                aValue = parseFloat(a.dailySales) || 0;
                bValue = parseFloat(b.dailySales) || 0;
                break;
            
            case 'dailyOrders':
                aValue = parseInt(a.dailyOrders) || 0;
                bValue = parseInt(b.dailyOrders) || 0;
                break;
            
            case 'promoAmount':
                aValue = parseFloat(a.promoAmount) || 0;
                bValue = parseFloat(b.promoAmount) || 0;
                break;
            
            case 'roi':
                const aRoi = a.promoAmount ? (a.dailySales / a.promoAmount) : 0;
                const bRoi = b.promoAmount ? (b.dailySales / b.promoAmount) : 0;
                aValue = aRoi;
                bValue = bRoi;
                break;
            
            case 'newLinks':
                aValue = parseInt(a.productCount) || 0;
                bValue = parseInt(b.productCount) || 0;
                break;
            
            default:
                return 0;
        }
        
        // 根据排序方向返回比较结果
        return newSortOrder === 'desc' ? bValue - aValue : aValue - bValue;
    });
    
    // 获取展开的运营卡片容器
    const shopsContainer = operatorCard.querySelector('.operator-shops');
    
    // 如果卡片是展开的，重新渲染排序后的店铺
    if (shopsContainer.style.display !== 'none') {
        shopsContainer.innerHTML = operatorShops.map((shop, index) => `
            <div class="shop-lazy-container" data-lazy-index="${index}" data-operator="${operator}">
                ${generateOperatorShopItem(shop)}
            </div>
        `).join('');
    }
} 

// 获取链接趋势数据
async function fetchLinksTrendData(type = 'global', identifier = '') {
    try {
        let url = '/api/links/trend';
        let params = new URLSearchParams();
        
        // 获取日期选择器的值
        const startDatePicker = document.getElementById('start-date-picker');
        const endDatePicker = document.getElementById('end-date-picker');
        
        if (startDatePicker && endDatePicker && startDatePicker.value && endDatePicker.value) {
            params.append('startDate', startDatePicker.value);
            params.append('endDate', endDatePicker.value);
        }
        
        if (type === 'operator') {
            url = '/api/links/trend/operator';
            params.append('operator', identifier);
        } else if (type === 'shop') {
            url = '/api/links/trend/shop';
            params.append('shop', identifier);
        }
        
        if (params.toString()) {
            url += '?' + params.toString();
        }
        
        const response = await fetch(url);
        const result = await response.json();
        
        if (result.success) {
            return result.data;
        } else {
            console.error('获取链接趋势数据失败:', result.message);
            return [];
        }
    } catch (error) {
        console.error('获取链接趋势数据失败:', error);
        return [];
    }
}

// 渲染链接趋势图表
function renderLinksTrendChart(containerId, data, title = '新上链接数量趋势') {
    const container = document.getElementById(containerId);
    if (!container) {
        console.error(`未找到容器: ${containerId}`);
        return;
    }
    
    // 清空容器
    container.innerHTML = '';
    
    // 如果没有数据，显示提示信息
    if (!data || data.length === 0) {
        container.innerHTML = '<div class="no-data">暂无趋势数据</div>';
        return;
    }
    
    // 准备图表数据
    const dates = data.map(item => {
        const date = new Date(item.date);
        return `${date.getMonth() + 1}/${date.getDate()}`;
    });
    const counts = data.map(item => item.count);
    
    // 创建图表的canvas元素
    const canvas = document.createElement('canvas');
    canvas.style.width = '100%';
    canvas.style.height = '300px';
    container.appendChild(canvas);
    
    // 渲染图表
    const ctx = canvas.getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [{
                label: '新上链接数',
                data: counts,
                borderColor: '#4b6cb7',
                backgroundColor: 'rgba(75, 108, 183, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.3,
                pointBackgroundColor: '#4b6cb7',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: title,
                    font: {
                        size: 16,
                        weight: 'bold'
                    },
                    color: '#2c3e50'
                },
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '数量',
                        color: '#666'
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        color: '#666'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: '日期',
                        color: '#666'
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        color: '#666',
                        maxTicksLimit: 10
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            elements: {
                point: {
                    hoverBackgroundColor: '#4b6cb7'
                }
            }
        }
    });
}

// 创建全局链接趋势图表容器
function createGlobalLinksTrendChart() {
    return `
        <div class="global-links-trend-container">
            <div class="trend-chart-card">
                <div class="trend-chart-header">
                    <h3 id="global-trend-title">新上链接数量趋势</h3>
                    <button class="refresh-trend-btn" onclick="refreshGlobalLinksTrend()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
                <div id="global-links-trend-chart" class="trend-chart-container">
                    <div class="loading-data">加载中...</div>
                </div>
            </div>
        </div>
    `;
}

// 创建运营链接趋势图表容器
function createOperatorLinksTrendChart(operator) {
    const chartId = `operator-links-trend-${operator.replace(/\s+/g, '-')}`;
    const titleId = `operator-trend-title-${operator.replace(/\s+/g, '-')}`;
    return `
        <div class="operator-links-trend-container" style="margin-top: 15px;">
            <div class="trend-chart-card">
                <div class="trend-chart-header">
                    <h4 id="${titleId}">${operator} - 新上链接数量趋势</h4>
                    <button class="refresh-trend-btn" onclick="refreshOperatorLinksTrend('${operator}')">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
                <div id="${chartId}" class="trend-chart-container">
                    <div class="loading-data">加载中...</div>
                </div>
            </div>
        </div>
    `;
}

// 刷新全局链接趋势
async function refreshGlobalLinksTrend() {
    const trendData = await fetchLinksTrendData('global');
    
    // 更新标题以显示当前日期范围
    const startDatePicker = document.getElementById('start-date-picker');
    const endDatePicker = document.getElementById('end-date-picker');
    let title = '新上链接数量趋势';
    
    if (startDatePicker && endDatePicker && startDatePicker.value && endDatePicker.value) {
        const startDate = startDatePicker.value;
        const endDate = endDatePicker.value;
        if (startDate === endDate) {
            title = `${startDate} 新上链接数量趋势`;
        } else {
            title = `${startDate} 至 ${endDate} 新上链接数量趋势`;
        }
    }
    
    // 更新页面标题
    const titleElement = document.getElementById('global-trend-title');
    if (titleElement) {
        titleElement.textContent = title;
    }
    
    renderLinksTrendChart('global-links-trend-chart', trendData, title);
}

// 刷新运营链接趋势
async function refreshOperatorLinksTrend(operator) {
    const chartId = `operator-links-trend-${operator.replace(/\s+/g, '-')}`;
    const titleId = `operator-trend-title-${operator.replace(/\s+/g, '-')}`;
    const trendData = await fetchLinksTrendData('operator', operator);
    
    // 更新标题以显示当前日期范围
    const startDatePicker = document.getElementById('start-date-picker');
    const endDatePicker = document.getElementById('end-date-picker');
    let title = `${operator} - 新上链接数量趋势`;
    
    if (startDatePicker && endDatePicker && startDatePicker.value && endDatePicker.value) {
        const startDate = startDatePicker.value;
        const endDate = endDatePicker.value;
        if (startDate === endDate) {
            title = `${operator} - ${startDate} 新上链接数量趋势`;
        } else {
            title = `${operator} - ${startDate} 至 ${endDate} 新上链接数量趋势`;
        }
    }
    
    // 更新页面标题
    const titleElement = document.getElementById(titleId);
    if (titleElement) {
        titleElement.textContent = title;
    }
    
    renderLinksTrendChart(chartId, trendData, title);
}

// 获取并显示店铺链接趋势数据
async function fetchAndDisplayShopLinksTrend(shopName) {
    const chartContainer = document.getElementById('shop-links-trend-chart');
    
    if (chartContainer) {
        chartContainer.innerHTML = '<div class="loading-data">正在加载链接趋势数据...</div>';
    }
    
    try {
        // 检查当前店铺
        const currentShopName = document.getElementById('modal-shop-name')?.innerText;
        if (currentShopName !== shopName) {
            console.log('店铺已切换，忽略链接趋势数据结果');
            return;
        }
        
        const trendData = await fetchLinksTrendData('shop', shopName);
        
        // 再次检查
        const currentShopNameAfter = document.getElementById('modal-shop-name')?.innerText;
        if (currentShopNameAfter !== shopName) {
            console.log('店铺已切换，忽略链接趋势数据结果');
            return;
        }
        
        // 更新标题以显示当前日期范围
        const startDatePicker = document.getElementById('start-date-picker');
        const endDatePicker = document.getElementById('end-date-picker');
        let title = `${shopName} - 新上链接数量趋势`;
        
        if (startDatePicker && endDatePicker && startDatePicker.value && endDatePicker.value) {
            const startDate = startDatePicker.value;
            const endDate = endDatePicker.value;
            if (startDate === endDate) {
                title = `${shopName} - ${startDate} 新上链接数量趋势`;
            } else {
                title = `${shopName} - ${startDate} 至 ${endDate} 新上链接数量趋势`;
            }
        }
        
        renderLinksTrendChart('shop-links-trend-chart', trendData, title);
    } catch (error) {
        console.error('获取店铺链接趋势数据失败:', error);
        
        // 检查是否仍然是当前店铺
        const currentShopName = document.getElementById('modal-shop-name')?.innerText;
        if (currentShopName === shopName && chartContainer) {
            chartContainer.innerHTML = '<div class="error-message">获取链接趋势数据失败</div>';
        }
    }
}