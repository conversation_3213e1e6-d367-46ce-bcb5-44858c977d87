<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 宜承数据管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            padding: 0;
            background-color: #f8f9fa;
            color: #333;
        }
        .main-container {
            display: flex;
            min-height: 100vh;
        }
        .container {
            flex: 1;
            padding: 30px;
            transition: all 0.3s ease;
        }
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
            color: white;
            padding: 20px;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            position: relative;
            min-height: 100vh;
            height: 100vh;
            overflow-y: auto;
            flex-shrink: 0;
            position: sticky;
            top: 0;
        }
        .sidebar-header {
            text-align: center;
            padding-bottom: 20px;
            margin-bottom: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .sidebar-menu {
            list-style: none;
            margin: 0;
            padding: 0;
        }
        .sidebar-menu li {
            margin-bottom: 10px;
        }
        .sidebar-menu a {
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
            position: relative;
            overflow: hidden;
        }
        .sidebar-menu a:hover {
            background-color: rgba(255,255,255,0.15);
            transform: translateX(5px);
        }
        .sidebar-menu a.active {
            background-color: rgba(255,255,255,0.2);
            transform: translateX(5px);
            border-left: 4px solid #fff;
            font-weight: 500;
        }
        .sidebar-menu i {
            margin-right: 12px;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
        }
        .user-info {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            padding: 15px 20px;
            background: rgba(0,0,0,0.1);
            border-top: 1px solid rgba(255,255,255,0.1);
            color: white;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .user-info .username {
            display: flex;
            align-items: center;
            font-size: 14px;
        }
        .user-info .username i {
            margin-right: 10px;
            font-size: 18px;
        }
        .user-profile-link {
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 5px;
            padding: 8px 10px;
            border-radius: 5px;
            background: rgba(255,255,255,0.05);
        }
        .user-profile-link:hover {
            transform: translateX(5px);
            background: rgba(255,255,255,0.1);
        }
        .user-profile-link i {
            margin-right: 10px;
            font-size: 16px;
        }
        .logout-btn {
            width: 100%;
            padding: 8px 10px;
            background: rgba(255,255,255,0.1);
            color: white;
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .logout-btn i {
            margin-right: 8px;
            font-size: 16px;
        }
        .logout-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6e8efb, #a777e3);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            margin-right: 10px;
        }
        .user-info-container {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .user-details {
            flex: 1;
        }
        .user-name {
            font-weight: bold;
            margin-bottom: 2px;
        }
        .user-role {
            font-size: 12px;
            opacity: 0.8;
        }
        
        /* 个人中心样式 */
        .profile-container {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
        }
        
        .profile-header {
            width: 100%;
            margin-bottom: 20px;
        }
        
        .profile-header h1 {
            font-size: 24px;
            color: #2c3e50;
            margin-bottom: 10px;
            position: relative;
            display: inline-block;
            padding-bottom: 10px;
        }
        
        .profile-header h1:after {
            content: '';
            position: absolute;
            width: 50%;
            height: 3px;
            background: linear-gradient(90deg, #4b6cb7, #182848);
            bottom: 0;
            left: 0;
            border-radius: 2px;
        }
        
        .profile-section {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            overflow: hidden;
            width: 100%;
            margin-bottom: 30px;
        }
        
        .profile-section-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .profile-section-header h2 {
            font-size: 18px;
            color: #2c3e50;
        }
        
        .profile-section-body {
            padding: 20px;
        }
        
        /* 个人信息样式 */
        .user-profile {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 36px;
            font-weight: bold;
        }
        
        .user-detail {
            flex: 1;
        }
        
        .user-title {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .user-tags {
            display: flex;
            gap: 10px;
        }
        
        .user-tag {
            background: #f0f2f5;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 12px;
            color: #555;
        }
        
        /* 数据卡片样式 */
        .data-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            padding: 20px;
        }
        
        .data-card {
            flex: 1;
            min-width: 200px;
            background: #f9f9f9;
            border-radius: 10px;
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .data-card-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
        
        .data-card-content {
            flex: 1;
        }
        
        .data-card-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .data-card-label {
            font-size: 14px;
            color: #666;
        }
        
        /* 统计图表样式 */
        .chart-container {
            width: 100%;
            height: 300px;
            position: relative;
        }
        
        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th, 
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .data-table th {
            background: #f5f7fa;
            color: #2c3e50;
            font-weight: 500;
        }
        
        .data-table tr:hover {
            background-color: #f8f9fa;
        }
        
        /* 商品明细 */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        
        .product-card {
            background: #f9f9f9;
            border-radius: 10px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .product-header {
            padding: 15px;
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
            color: white;
            display: flex;
            justify-content: space-between;
        }
        
        .product-title {
            font-weight: bold;
        }
        
        .product-id {
            opacity: 0.8;
            font-size: 0.9em;
        }
        
        .product-body {
            padding: 15px;
        }
        
        .product-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }
        
        .product-stat {
            text-align: center;
        }
        
        .product-stat-value {
            font-weight: bold;
            font-size: 18px;
            color: #2c3e50;
        }
        
        .product-stat-label {
            color: #666;
            font-size: 12px;
        }
        
        /* 趋势指标 */
        .trend-indicator {
            display: inline-flex;
            align-items: center;
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 4px;
            margin-left: 5px;
        }
        
        .trend-up {
            color: #f56c6c;
            background-color: rgba(245, 108, 108, 0.1);
        }
        
        .trend-down {
            color: #67c23a;
            background-color: rgba(103, 194, 58, 0.1);
        }
        
        /* 设置指标按钮 */
        .settings-btn {
            border: 1px solid #ddd;
            background: white;
            border-radius: 5px;
            padding: 5px 10px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .settings-btn:hover {
            background: #f5f7fa;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>宜承管理系统</h2>
            </div>
            <ul class="sidebar-menu">
                <li><a href="shop.html"><i>📊</i>数据概览</a></li>
                <li><a href="shop.html"><i>📈</i>销售统计</a></li>
                <li><a href="shop.html"><i>🏪</i>店铺管理</a></li>
                <li><a href="shop.html"><i>📦</i>商品管理</a></li>
                <li><a href="shop.html"><i>💰</i>财务管理</a></li>
                <li><a href="shop.html"><i>🔗</i>链接管理</a></li>
                <li><a href="shop.html"><i>👥</i>团队管理</a></li>
            </ul>
            <!-- 添加用户信息区域 -->
            <div class="user-info">
                <div class="user-info-container">
                    <div class="user-avatar" id="userAvatar"></div>
                    <div class="user-details">
                        <div class="user-name" id="currentUsername">用户</div>
                        <div class="user-role" id="userRole">操作员</div>
                    </div>
                </div>
                <a class="user-profile-link" id="profileLink">个人中心</a>
                <button class="logout-btn" id="logoutBtn">退出登录</button>
            </div>
        </div>
        
        <div class="container">
            <div class="profile-header">
                <h1>个人信息</h1>
            </div>
            
            <div class="profile-container">
                <!-- 个人信息卡片 -->
                <div class="profile-section">
                    <div class="user-profile">
                        <div class="avatar">
                            测试
                        </div>
                        <div class="user-detail">
                            <div class="user-name">测试</div>
                            <div class="user-title">一级团队：拼多多 &nbsp;&nbsp;&nbsp; 二级团队：拼多多一组</div>
                            <div class="user-tags">
                                <span class="user-tag">管理员</span>
                                <span class="user-tag">运营专员</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="data-cards">
                        <div class="data-card">
                            <div class="data-card-icon">📦</div>
                            <div class="data-card-content">
                                <div class="data-card-value">2640</div>
                                <div class="data-card-label">负责商品</div>
                            </div>
                        </div>
                        
                        <div class="data-card">
                            <div class="data-card-icon">👥</div>
                            <div class="data-card-content">
                                <div class="data-card-value">3</div>
                                <div class="data-card-label">管理人员</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 我的选项卡 -->
                <div class="profile-section">
                    <div class="profile-section-header">
                        <h2>我负责的商品</h2>
                    </div>
                    <div class="profile-section-body">
                        <div class="tabs">
                            <div class="tab active">我负责的商品</div>
                            <div class="tab">我负责的店铺</div>
                        </div>
                    </div>
                </div>
                
                <!-- 总体情况 -->
                <div class="profile-section">
                    <div class="profile-section-header">
                        <h2>总体情况</h2>
                        <button class="settings-btn">设置指标</button>
                    </div>
                    <div class="profile-section-body">
                        <div class="data-cards">
                            <div class="data-card">
                                <div class="data-card-content">
                                    <div class="data-card-label">销售额</div>
                                    <div class="data-card-value">¥1,295.43
                                        <span class="trend-indicator trend-up">↑ 21.03%</span>
                                    </div>
                                    <div class="data-card-label">上期: ¥979.69</div>
                                </div>
                            </div>
                            
                            <div class="data-card">
                                <div class="data-card-content">
                                    <div class="data-card-label">销量</div>
                                    <div class="data-card-value">11,767
                                        <span class="trend-indicator trend-down">↓ 16.34%</span>
                                    </div>
                                    <div class="data-card-label">上期: 14,065</div>
                                </div>
                            </div>
                            
                            <div class="data-card">
                                <div class="data-card-content">
                                    <div class="data-card-label">站内推广费</div>
                                    <div class="data-card-value">¥699.50
                                        <span class="trend-indicator trend-up">↑ 22.98%</span>
                                    </div>
                                    <div class="data-card-label">上期: ¥568.87</div>
                                </div>
                            </div>
                            
                            <div class="data-card">
                                <div class="data-card-content">
                                    <div class="data-card-label">平台费</div>
                                    <div class="data-card-value">¥756.50
                                        <span class="trend-indicator trend-up">↑ 44.64%</span>
                                    </div>
                                    <div class="data-card-label">上期: ¥523.02</div>
                                </div>
                            </div>
                            
                            <div class="data-card">
                                <div class="data-card-content">
                                    <div class="data-card-label">退款金额</div>
                                    <div class="data-card-value">¥1,205.38
                                        <span class="trend-indicator trend-up">↑ 9.78%</span>
                                    </div>
                                    <div class="data-card-label">上期: ¥1,098.45</div>
                                </div>
                            </div>
                            
                            <div class="data-card">
                                <div class="data-card-content">
                                    <div class="data-card-label">利润</div>
                                    <div class="data-card-value">¥-46.48
                                        <span class="trend-indicator trend-down">↓ 47.18%</span>
                                    </div>
                                    <div class="data-card-label">上期: ¥-87.99</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 销售趋势图表 -->
                        <div class="chart-container">
                            <div id="salesChart" style="width: 100%; height: 100%;">
                                <!-- 这里将用 echarts 或其他图表库来渲染图表 -->
                                <img src="chart_placeholder.png" alt="销售趋势图表" style="width: 100%; height: 100%; object-fit: cover; opacity: 0.7;">
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 商品明细 -->
                <div class="profile-section">
                    <div class="profile-section-header">
                        <h2>商品明细</h2>
                        <button class="settings-btn">设置指标</button>
                    </div>
                    <div class="profile-section-body">
                        <div class="product-grid">
                            <div class="product-card">
                                <div class="product-header">
                                    <div class="product-title">爆款热销</div>
                                    <div class="product-id">2640</div>
                                </div>
                                <div class="product-body">
                                    <div class="product-stats">
                                        <div class="product-stat">
                                            <div class="product-stat-value">¥262,141.74</div>
                                            <div class="product-stat-label">销售额</div>
                                        </div>
                                        <div class="product-stat">
                                            <div class="product-stat-value">100%</div>
                                            <div class="product-stat-label">占比</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="product-card">
                                <div class="product-header">
                                    <div class="product-title">快速增长</div>
                                    <div class="product-id">2640</div>
                                </div>
                                <div class="product-body">
                                    <div class="product-stats">
                                        <div class="product-stat">
                                            <div class="product-stat-value">¥262,141.74</div>
                                            <div class="product-stat-label">销售额</div>
                                        </div>
                                        <div class="product-stat">
                                            <div class="product-stat-value">100%</div>
                                            <div class="product-stat-label">占比</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="product-card">
                                <div class="product-header">
                                    <div class="product-title">利润王牌</div>
                                    <div class="product-id">412</div>
                                </div>
                                <div class="product-body">
                                    <div class="product-stats">
                                        <div class="product-stat">
                                            <div class="product-stat-value">¥40,544.1</div>
                                            <div class="product-stat-label">销售额</div>
                                        </div>
                                        <div class="product-stat">
                                            <div class="product-stat-value">15.47%</div>
                                            <div class="product-stat-label">占比</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="product-card">
                                <div class="product-header">
                                    <div class="product-title">号黑暗洞</div>
                                    <div class="product-id">1335</div>
                                </div>
                                <div class="product-body">
                                    <div class="product-stats">
                                        <div class="product-stat">
                                            <div class="product-stat-value">¥135,004.37</div>
                                            <div class="product-stat-label">销售额</div>
                                        </div>
                                        <div class="product-stat">
                                            <div class="product-stat-value">51.50%</div>
                                            <div class="product-stat-label">占比</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="product-card">
                                <div class="product-header">
                                    <div class="product-title">持续减收</div>
                                    <div class="product-id">655</div>
                                </div>
                                <div class="product-body">
                                    <div class="product-stats">
                                        <div class="product-stat">
                                            <div class="product-stat-value">¥63,954.7</div>
                                            <div class="product-stat-label">销售额</div>
                                        </div>
                                        <div class="product-stat">
                                            <div class="product-stat-value">24.40%</div>
                                            <div class="product-stat-label">占比</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 获取用户角色
        function getUserRole() {
            const cookies = document.cookie.split(';');
            const roleCookie = cookies.find(cookie => cookie.trim().startsWith('userRole='));
            return roleCookie ? roleCookie.split('=')[1].trim() : null;
        }
        
        // 获取登录时使用的用户名（user）
        function getUser() {
            const cookies = document.cookie.split(';');
            const userCookie = cookies.find(cookie => cookie.trim().startsWith('user='));
            return userCookie ? decodeURIComponent(userCookie.split('=')[1].trim()) : '用户';
        }
        
        // 退出登录
        function logout() {
            // 删除所有相关的cookie
            document.cookie = 'loginAuth=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            document.cookie = 'userRole=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            document.cookie = 'username=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            document.cookie = 'user=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            
            // 重定向到登录页面
            window.location.href = 'login.html';
        }
        
        // 在页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 显示当前用户名
            const usernameElement = document.getElementById('currentUsername');
            if (usernameElement) {
                usernameElement.textContent = getUser();
            }
            
            // 设置头像显示用户名首字母
            const userAvatar = document.getElementById('userAvatar');
            if (userAvatar) {
                const username = getUser();
                userAvatar.textContent = username.charAt(0).toUpperCase();
            }
            
            // 显示用户角色
            const userRoleElement = document.getElementById('userRole');
            if (userRoleElement) {
                userRoleElement.textContent = getUserRole() === 'admin' ? '管理员' : '操作员';
            }
            
            // 将"个人中心"链接样式设为活跃
            const profileLink = document.getElementById('profileLink');
            if (profileLink) {
                profileLink.classList.add('active');
            }
            
            // 添加退出登录按钮点击事件
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', logout);
            }
            
            // 设置模拟图表数据（在实际应用中，您会加载并使用真实数据）
            // 这里仅是占位符，在真实环境中您可以使用ECharts等图表库来绘制趋势图
        });
    </script>
</body>
</html>