/**
 * state.js - Centralized state management for the application
 * 
 * This module provides a simple state management solution to replace
 * the global window variables used throughout the application.
 */

// Create a state object with default values
const AppState = {
    // Shop management state
    shops: {
        allShops: [],
        currentPage: 1,
        itemsPerPage: 12,
        searchTerm: '',
        currentView: 'card',
        sortField: 'dailySales',
        sortOrder: 'desc',
        selectedDate: '',
        filteredShops: []
    },
    
    // Finance management state
    finance: {
        balanceData: {},
        filteredBalanceData: {},
        currentBalancePage: 1,
        itemsPerPage: 12,
        balanceViewType: 'grid',
        balanceSortField: 'balance',
        balanceSortOrder: 'desc',
        balanceSearchTerm: ''
    },
    
    // Message board state
    messageBoard: {
        messages: [],
        currentPage: 1,
        filters: {
            status: 'all',
            type: 'all'
        }
    },
    
    // SPU trend state
    spuTrend: {
        selectedSPUs: [],
        comparisonMode: false,
        timeGranularity: 'daily',
        metricType: 'sales',
        yearFilter: new Date().getFullYear(),
        weekFilter: 1,
        searchTerm: ''
    },
    
    // Dashboard state
    dashboard: {
        timestamp: new Date().getTime(),
        salesTrendChart: null,
        topShopsChart: null
    },
    
    // General application state
    app: {
        currentModule: null,
        isLoading: false,
        lastError: null
    },
    
    // Get state by module name
    getState(module) {
        return this[module] || {};
    },
    
    // Update state for a specific module
    updateState(module, updates) {
        if (!this[module]) {
            this[module] = {};
        }
        
        this[module] = {
            ...this[module],
            ...updates
        };
        
        // Notify subscribers if we implement that feature
        this._notifySubscribers(module);
        
        return this[module];
    },
    
    // Reset state for a specific module to defaults
    resetState(module) {
        // Store the original state when the app initializes
        if (!this._defaultState) {
            this._defaultState = JSON.parse(JSON.stringify(this));
        }
        
        if (this._defaultState[module]) {
            this[module] = JSON.parse(JSON.stringify(this._defaultState[module]));
            this._notifySubscribers(module);
        }
    },
    
    // Subscribers for state changes (can be implemented later)
    _subscribers: {},
    
    // Subscribe to state changes for a module
    subscribe(module, callback) {
        if (!this._subscribers[module]) {
            this._subscribers[module] = [];
        }
        
        this._subscribers[module].push(callback);
        
        // Return unsubscribe function
        return () => {
            this._subscribers[module] = this._subscribers[module].filter(cb => cb !== callback);
        };
    },
    
    // Notify subscribers of state changes
    _notifySubscribers(module) {
        if (this._subscribers[module]) {
            this._subscribers[module].forEach(callback => {
                callback(this[module]);
            });
        }
    }
};

export default AppState;
