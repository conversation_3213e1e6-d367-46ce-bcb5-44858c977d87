<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宜承-店铺数据管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: #121212; /* 深色背景更适合星空效果 */
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            position: relative;
            transition: background 1.5s ease;
        }
        
        /* 天气效果容器 */
        .weather-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
            overflow: hidden;
        }
        
        /* 通用天气元素样式 */
        .weather-element {
            position: absolute;
            pointer-events: none;
        }
        
        /* 太阳样式 */
        .sun {
            width: 80px;
            height: 80px;
            background: #FFD700;
            border-radius: 50%;
            box-shadow: 0 0 50px #FFD700, 0 0 100px #FF8C00;
            top: 15%;
            right: 15%;
            animation: pulse 3s infinite alternate;
        }
        
        .sun-ray {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100%;
            height: 3px;
            background: #FFD700;
            transform-origin: 0 0;
        }
        
        /* 云朵样式 */
        .cloud {
            width: 150px;
            height: 60px;
            background: #fff;
            border-radius: 50px;
            position: absolute;
            animation: float 20s linear infinite;
            box-shadow: 0 5px 10px rgba(0,0,0,0.1);
        }
        
        .cloud:before, .cloud:after {
            content: '';
            position: absolute;
            background: #fff;
            border-radius: 50%;
        }
        
        .cloud:before {
            width: 80px;
            height: 80px;
            top: -30px;
            left: 25px;
        }
        
        .cloud:after {
            width: 60px;
            height: 60px;
            top: -20px;
            right: 25px;
        }
        
        .small-cloud {
            transform: scale(0.6);
            opacity: 0.8;
        }
        
        .dark-cloud {
            background: #546e7a;
        }
        
        .dark-cloud:before, .dark-cloud:after {
            background: #546e7a;
        }
        
        /* 雨滴样式 */
        .raindrop {
            position: absolute;
            width: 2px;
            height: 15px;
            background: linear-gradient(to bottom, rgba(255,255,255,0), rgba(255,255,255,0.7));
            border-radius: 0 0 5px 5px;
            animation: rain 1s linear infinite;
        }
        
        /* 雪花样式 */
        .snowflake {
            position: absolute;
            background: white;
            border-radius: 50%;
            box-shadow: 0 0 5px rgba(255,255,255,0.8);
            animation: snow 10s linear infinite;
        }
        
        /* 星星样式 */
        .star {
            background: white;
            border-radius: 50%;
            box-shadow: 0 0 10px white, 0 0 20px white;
            animation: twinkle 2s ease-in-out infinite alternate;
        }
        
        /* 月亮样式 */
        .moon {
            width: 60px;
            height: 60px;
            background: #f9f9f9;
            border-radius: 50%;
            box-shadow: 0 0 20px #f9f9f9, 0 0 40px rgba(255,255,255,0.2);
            top: 15%;
            right: 15%;
        }
        
        /* 动画定义 */
        @keyframes float {
            0% {
                transform: translateX(-150px);
            }
            100% {
                transform: translateX(calc(100vw + 150px));
            }
        }
        
        @keyframes rain {
            0% {
                transform: translateY(-100px);
            }
            100% {
                transform: translateY(calc(100vh + 100px));
            }
        }
        
        @keyframes snow {
            0% {
                transform: translateY(-100px) translateX(0) rotate(0deg);
            }
            100% {
                transform: translateY(calc(100vh + 100px)) translateX(100px) rotate(360deg);
            }
        }
        
        @keyframes twinkle {
            0% {
                opacity: 0.2;
                transform: scale(0.8);
            }
            100% {
                opacity: 1;
                transform: scale(1.2);
            }
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            100% {
                transform: scale(1.1);
                opacity: 0.8;
            }
        }
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
            width: 100%;
            max-width: 400px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            overflow: hidden;
            z-index: 10; /* 确保登录框在星空之上 */
            backdrop-filter: blur(5px); /* 添加模糊背景效果 */
        }
        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, #4b6cb7, #182848);
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header h1 {
            color: #2c3e50;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .login-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .form-group {
            position: relative;
        }
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .form-group input:focus {
            border-color: #4b6cb7;
            outline: none;
            box-shadow: 0 0 0 3px rgba(75,108,183,0.1);
        }
        .form-group i {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }
        .login-btn {
            background: linear-gradient(90deg, #4b6cb7, #182848);
            color: white;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(75,108,183,0.3);
        }
        .error-message {
            color: #e74c3c;
            font-size: 14px;
            text-align: center;
            margin-top: 10px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .error-message.show {
            opacity: 1;
        }
        @media (max-width: 480px) {
            .login-container {
                margin: 20px;
                padding: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>宜承-店铺数据管理系统</h1>
        </div>
        <form class="login-form" id="loginForm" onsubmit="return handleLogin(event)">
            <div class="form-group">
                <input type="text" id="username" placeholder="请输入用户名" required>
            </div>
            <div class="form-group">
                <input type="password" id="password" placeholder="请输入密码" required>
            </div>
            <button type="submit" class="login-btn">登录</button>
            <div class="error-message" id="errorMessage"></div>
        </form>
    </div>

    <script src="weatherEffects.js"></script>
    <script>
        async function handleLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const errorMessage = document.getElementById('errorMessage');
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                if (!response.ok) {
                    throw new Error(`服务器错误: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.success) {
                    // 登录成功，设置cookie并跳转到主页面
                    const expirationDate = new Date();
                    expirationDate.setHours(expirationDate.getHours() + 24); // Cookie有效期24小时
                    
                    // 保存登录状态
                    document.cookie = `loginAuth=true; expires=${expirationDate.toUTCString()}; path=/; SameSite=Strict`;
                    
                    // 确保角色信息有效
                    const role = data.role || 'operator';
                    document.cookie = `userRole=${role}; expires=${expirationDate.toUTCString()}; path=/; SameSite=Strict`;
                    
                    // 保存用户名
                    const user = data.user || username;
                    document.cookie = `user=${encodeURIComponent(user)}; expires=${expirationDate.toUTCString()}; path=/; SameSite=Strict`;
                    
                    // 保存用户显示名称（真实姓名）
                    const name = data.name || data.user || username;
                    document.cookie = `name=${encodeURIComponent(name)}; expires=${expirationDate.toUTCString()}; path=/; SameSite=Strict`;
                    
                    // 也保存角色中文名称
                    let roleName = role;
                    if (role === 'admin') {
                        roleName = '管理员';
                    } else if (role === 'art') {
                        roleName = '美工';
                    } else if (role === 'yunying') {
                        roleName = '运营';
                    } else if (role === 'caigou') {
                        roleName = '采购';
                    } else if (role === 'kefu') {
                        roleName = '客服';
                    } else if (role === 'caiwu') {
                        roleName = '财务';
                    } else {
                        roleName = '操作员';
                    }
                    document.cookie = `roleName=${encodeURIComponent(roleName)}; expires=${expirationDate.toUTCString()}; path=/; SameSite=Strict`;
                    
                    // 添加调试日志
                    console.log("登录成功，设置cookie：", {
                        user: user,
                        name: name,
                        role: role,
                        roleName: roleName
                    });
                    
                    window.location.href = 'shop.html';
                } else {
                    // 登录失败，显示错误信息
                    errorMessage.textContent = data.message || '用户名或密码错误';
                    errorMessage.classList.add('show');
                    setTimeout(() => {
                        errorMessage.classList.remove('show');
                    }, 3000);
                }
            } catch (error) {
                console.error('登录验证失败:', error);
                errorMessage.textContent = '系统错误，请稍后重试';
                errorMessage.classList.add('show');
                setTimeout(() => {
                    errorMessage.classList.remove('show');
                }, 3000);
            }
            
            return false;
        }
    </script>
    
    <!-- 引入天气效果脚本 -->
    <script>
        // 页面加载完成后初始化天气效果
        document.addEventListener('DOMContentLoaded', function() {
            // 创建天气效果容器
            const weatherContainer = document.createElement('div');
            weatherContainer.className = 'weather-container';
            weatherContainer.style.position = 'fixed';
            weatherContainer.style.top = '0';
            weatherContainer.style.left = '0';
            weatherContainer.style.width = '100%';
            weatherContainer.style.height = '100%';
            weatherContainer.style.zIndex = '-1';
            document.body.appendChild(weatherContainer);
            
            // 初始化星空效果，增加星星数量和视差效果
            const weatherEffects = initWeatherEffects({
                container: weatherContainer,
                type: 'starry',
                interactive: true,
                parallax: true
            });
            
            // 设置背景颜色为深色
            document.body.style.backgroundColor = '#121212';
            
            // 让星星更明亮
            setTimeout(() => {
                const starElements = document.querySelectorAll('.weather-star');
                starElements.forEach(star => {
                    const currentShadow = window.getComputedStyle(star).boxShadow;
                    star.style.boxShadow = currentShadow.replace('rgba(255, 255, 255,', 'rgba(255, 255, 255,');
                    star.style.opacity = parseFloat(star.style.opacity) + 0.2;
                });
            }, 500);
        });
    </script>
</body>
</html> 