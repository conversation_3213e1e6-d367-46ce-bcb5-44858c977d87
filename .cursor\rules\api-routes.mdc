---
description: 
globs: 
alwaysApply: true
---
# API 路由指南

## 🔗 API 端点总览

基于 [app.py](mdc:app.py) 的路由分析，系统提供以下主要 API 端点：

## 🔐 用户认证与权限管理

### 登录认证
- **POST** `/api/login` - 用户登录验证
- **GET** `/api/users` - 获取用户列表（需要管理员权限）
- **POST** `/api/users` - 创建新用户（需要管理员权限）
- **PUT** `/api/users/<username>` - 更新用户信息（需要管理员权限）
- **DELETE** `/api/users/<username>` - 删除用户（需要管理员权限）
- **GET** `/api/users/filtered` - 获取过滤后的用户列表

### 权限控制
- 使用 `@admin_required` 装饰器保护管理员功能
- 角色类型：admin, yunying, art, caigou, caiwu, kefu
- 用户信息存储在 [宜承账号.json](mdc:宜承账号.json)

## 📊 店铺数据管理

### 店铺信息
- **GET** `/api/shops` - 获取店铺列表和销售数据
- **GET** `/api/shop-trend` - 获取店铺趋势数据
- **GET** `/api/shop-trend-data` - 获取店铺趋势详细数据

### 数据来源
- 店铺基础信息：[店铺-账号信息.csv](mdc:店铺-账号信息.csv)
- 销售数据通过 [sale_data_importer.py](mdc:sale_data_importer.py) 导入

## 💰 财务数据分析

### 财务总览
- **GET** `/api/finance/overview` - 获取财务总览数据
- **GET** `/api/finance/details` - 获取财务详细数据
- **GET** `/api/finance/account-balance` - 获取账户余额信息

### 推广数据
- **GET** `/api/promotion-data` - 获取推广数据
- **GET** `/api/promotion-shops` - 获取推广店铺列表
- **POST** `/api/promotion-data/export` - 导出推广数据
- **GET** `/api/promotion-data/trend` - 获取推广趋势
- **GET** `/api/promotion-data/top-ads` - 获取表现最佳的广告

## 📈 仪表盘与报表

### 仪表盘
- **GET** `/api/dashboard` - 获取仪表盘数据
- **GET** `/api/dashboard/trend` - 获取仪表盘趋势数据
- **GET** `/api/daily-operation-data` - 获取日常运营数据

### 数据处理
- 使用 [utils/sales_data.py](mdc:utils/sales_data.py) 中的函数处理销售数据
- 支持多时间维度数据分析（日、周、月）

## 🎯 SPU 数据管理

### SPU 分析
- **GET** `/api/spu-data` - 获取SPU数据
- **GET** `/api/spu-trend` - 获取SPU趋势
- **GET** `/api/spu-trend-data` - 获取SPU趋势详细数据
- **GET** `/api/spu-search` - 搜索SPU
- **GET** `/api/spu-statistics` - 获取SPU统计信息

### 数据导入
- 使用 [spu_data_importer.py](mdc:spu_data_importer.py) 导入SPU数据
- 详细说明参见 [SPU_DATA_IMPORT_README.md](mdc:SPU_DATA_IMPORT_README.md)

## 🔗 链接管理

### 链接数据
- **GET** `/api/links` - 获取链接列表
- **GET** `/api/links/summary` - 获取链接汇总
- **POST** `/api/links/<link_id>/toggle` - 切换链接状态
- **GET** `/api/links/trend` - 获取链接趋势
- **GET** `/api/links/trend/operator` - 获取运营商链接趋势
- **GET** `/api/links/trend/shop` - 获取店铺链接趋势

## 📋 任务管理系统

### 美工任务
- **GET** `/api/art-tasks` - 获取任务列表
- **POST** `/api/art-tasks` - 创建新任务
- **GET** `/api/art-tasks/<task_id>` - 获取任务详情
- **PUT** `/api/art-tasks/<task_id>` - 更新任务
- **DELETE** `/api/art-tasks/<task_id>` - 删除任务
- **PATCH** `/api/art-tasks/<task_id>/status` - 更新任务状态

### 任务附件与评论
- **POST** `/api/art-tasks/<task_id>/comments` - 添加任务评论
- **POST** `/api/art-tasks/<task_id>/attachments` - 上传任务附件
- **GET** `/api/art-tasks/<task_id>/attachments/<filename>` - 下载任务附件

### 任务总览
- **GET** `/api/art-tasks/overview` - 获取任务概览
- **POST** `/api/art-tasks/overview/reset` - 重置任务概览

## 🚨 违规数据管理

### 违规数据
- **GET** `/api/violation-data` - 获取违规数据
- **GET** `/api/violation-data/<violation_id>` - 获取违规详情
- **POST** `/api/violation-data/export` - 导出违规数据

## 💬 消息管理

### 消息系统
- **GET** `/api/messages` - 获取消息列表
- **POST** `/api/messages` - 添加新消息
- **PATCH** `/api/messages/<message_id>` - 更新消息（需要管理员权限）
- **DELETE** `/api/messages/<message_id>` - 删除消息（需要管理员权限）

## 📁 文件上传下载

### 文件管理
- **POST** `/api/tasks/upload` - 上传任务文件
- **GET** `/api/tasks/download` - 下载任务文件

## 👥 团队管理

### 团队数据
- **GET** `/api/team-management` - 获取团队管理数据

## 🎨 静态文件路由

### 页面路由
- **GET** `/` - 默认跳转到 shop.html
- **GET** `/<path:path>` - 提供静态文件服务
- **GET** `/user/<username>` - 用户个人资料页面
- **GET** `/product` - 产品管理页面

## 📝 API 使用注意事项

### 权限验证
- 管理员功能需要 `userRole=admin` Cookie
- 用户角色通过 `request.cookies.get('userRole')` 获取

### 错误处理
- 统一返回 JSON 格式的错误响应
- HTTP 状态码：200（成功）、403（权限不足）、404（未找到）、500（服务器错误）

### 数据格式
- 日期格式统一使用字符串格式（如：'2025-01-15'）
- 金额数据使用浮点数类型
- 中文编码统一使用 UTF-8

### 跨域支持
- 使用 Flask-CORS 支持跨域请求
- 支持 credentials 传递

