/**
 * api.js - Centralized API request handling
 */

// Add timestamp to URL to prevent caching
function addTimestamp(url) {
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}t=${Date.now()}`;
}

// Generic fetch function with error handling
async function fetchData(url, options = {}) {
    try {
        const response = await fetch(addTimestamp(url), options);
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.success === false) {
            throw new Error(data.message || data.error || 'API request failed');
        }
        
        return data;
    } catch (error) {
        console.error(`API request failed: ${url}`, error);
        throw error;
    }
}

// Shop API functions
export const shopAPI = {
    // Get all shops
    async getShops(date = '') {
        const url = date ? `/api/shops?date=${date}` : '/api/shops';
        return fetchData(url);
    },
    
    // Get shop details
    async getShopDetails(shopName, startDate = '', endDate = '') {
        let url = `/api/shop/details?shop=${encodeURIComponent(shopName)}`;
        if (startDate && endDate) {
            url += `&startDate=${startDate}&endDate=${endDate}`;
        }
        return fetchData(url);
    },
    
    // Get shop trend data
    async getShopTrend(shopName, days = 30) {
        return fetchData(`/api/shop/trend?shop=${encodeURIComponent(shopName)}&days=${days}`);
    }
};

// Dashboard API functions
export const dashboardAPI = {
    // Get dashboard data
    async getDashboardData(days = 30) {
        return fetchData(`/api/dashboard/trend?days=${days}`);
    }
};

// Finance API functions
export const financeAPI = {
    // Get finance overview
    async getFinanceOverview() {
        return fetchData('/api/finance/overview');
    },
    
    // Get finance details
    async getFinanceDetails(startDate = '', endDate = '', reportType = 'all', page = 1, pageSize = 10) {
        const params = new URLSearchParams({
            startDate,
            endDate,
            reportType,
            page,
            pageSize
        });
        return fetchData(`/api/finance/details?${params}`);
    },
    
    // Get account balance data
    async getAccountBalance(date = '') {
        const url = date ? `/api/finance/account-balance?date=${date}` : '/api/finance/account-balance';
        return fetchData(url);
    }
};

// Product API functions
export const productAPI = {
    // Get SPU list
    async getSPUList() {
        return fetchData('/api/spu/list');
    },
    
    // Get SKU list
    async getSKUList() {
        return fetchData('/api/sku/list');
    },
    
    // Get daily operation data
    async getDailyOperationData() {
        return fetchData('/api/daily-operation-data');
    },
    
    // Get SPU trend data
    async getSPUTrend(spuIds, year, week, granularity = 'daily', metricType = 'sales') {
        const params = new URLSearchParams({
            year,
            week,
            granularity,
            metricType
        });
        
        if (Array.isArray(spuIds)) {
            spuIds.forEach(id => params.append('spuIds', id));
        } else {
            params.append('spuIds', spuIds);
        }
        
        return fetchData(`/api/spu/trend?${params}`);
    }
};

// Link API functions
export const linkAPI = {
    // Get link list
    async getLinkList(startDate = '', endDate = '', search = '', page = 1, pageSize = 10) {
        const params = new URLSearchParams({
            startDate,
            endDate,
            search,
            page,
            pageSize
        });
        return fetchData(`/api/links?${params}`);
    }
};

// Message board API functions
export const messageBoardAPI = {
    // Get messages
    async getMessages(page = 1, filters = { status: 'all', type: 'all' }) {
        const params = new URLSearchParams({
            page,
            per_page: 5
        });
        
        if (filters.status !== 'all') {
            params.append('status', filters.status);
        }
        
        if (filters.type !== 'all') {
            params.append('type', filters.type);
        }
        
        return fetchData(`/api/messages?${params}`);
    },
    
    // Add message
    async addMessage(messageData) {
        return fetchData('/api/messages', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(messageData)
        });
    },
    
    // Update message
    async updateMessage(messageId, updates) {
        return fetchData(`/api/messages/${messageId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(updates)
        });
    },
    
    // Delete message
    async deleteMessage(messageId) {
        return fetchData(`/api/messages/${messageId}`, {
            method: 'DELETE'
        });
    }
};
