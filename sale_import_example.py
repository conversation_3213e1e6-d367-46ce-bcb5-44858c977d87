#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
销售数据导入使用示例
演示如何使用销售数据导入工具
"""

from sale_data_importer import SaleDataImporter

def example_import_all():
    """示例：导入所有Excel文件"""
    print("=== 导入所有Excel文件 ===")
    importer = SaleDataImporter()
    importer.import_all_files()

def example_import_specific_file():
    """示例：导入特定文件"""
    print("=== 导入特定文件 ===")
    importer = SaleDataImporter()
    # 导入2025年6月10日的文件
    importer.import_all_files("*20250610*.xlsx")

def example_import_by_date_pattern():
    """示例：按日期模式导入"""
    print("=== 按日期模式导入 ===")
    importer = SaleDataImporter()
    # 导入2025年6月的所有文件
    importer.import_all_files("*202506*.xlsx")

def example_import_with_custom_paths():
    """示例：使用自定义路径"""
    print("=== 使用自定义路径 ===")
    importer = SaleDataImporter(
        db_path='custom_db/sale_data.db',
        excel_dir='custom_excel_dir'
    )
    importer.import_all_files()

def example_check_stats():
    """示例：查看数据库统计信息"""
    print("=== 查看数据库统计信息 ===")
    importer = SaleDataImporter()
    importer.show_database_stats()

def example_query_data():
    """示例：查询数据"""
    print("=== 查询销售数据示例 ===")
    import sqlite3
    
    # 连接数据库
    conn = sqlite3.connect('static/db/sale_data.db')
    cursor = conn.cursor()
    
    try:
        # 查询前5条记录
        print("\n前5条销售记录:")
        cursor.execute("SELECT shop_name, date, sales_amount, net_sales_amount FROM sale_data LIMIT 5")
        records = cursor.fetchall()
        for record in records:
            print(f"  店铺: {record[0]}, 日期: {record[1]}, 销售额: ¥{record[2]:,.2f}, 净销售额: ¥{record[3]:,.2f}")
        
        # 查询销售额最高的店铺
        print("\n销售额最高的5个店铺:")
        cursor.execute("""
            SELECT shop_name, SUM(sales_amount) as total_sales 
            FROM sale_data 
            GROUP BY shop_code, shop_name 
            ORDER BY total_sales DESC 
            LIMIT 5
        """)
        top_shops = cursor.fetchall()
        for i, (shop_name, total_sales) in enumerate(top_shops, 1):
            print(f"  {i}. {shop_name}: ¥{total_sales:,.2f}")
        
        # 查询最近7天的销售趋势
        print("\n最近的销售记录:")
        cursor.execute("""
            SELECT date, SUM(sales_amount) as daily_sales, COUNT(*) as shop_count
            FROM sale_data 
            GROUP BY date 
            ORDER BY date DESC 
            LIMIT 7
        """)
        daily_sales = cursor.fetchall()
        for date, sales, count in daily_sales:
            print(f"  {date}: ¥{sales:,.2f} (来自{count}个店铺)")
            
    except sqlite3.Error as e:
        print(f"查询出错: {e}")
    finally:
        conn.close()

if __name__ == '__main__':
    print("销售数据导入工具使用示例")
    print("请选择要执行的示例:")
    print("1. 导入所有Excel文件")
    print("2. 导入特定文件 (2025年6月10日)")
    print("3. 按日期模式导入 (2025年6月)")
    print("4. 使用自定义路径")
    print("5. 查看数据库统计信息")
    print("6. 查询数据示例")
    
    choice = input("请输入选择 (1-6): ").strip()
    
    if choice == '1':
        example_import_all()
    elif choice == '2':
        example_import_specific_file()
    elif choice == '3':
        example_import_by_date_pattern()
    elif choice == '4':
        example_import_with_custom_paths()
    elif choice == '5':
        example_check_stats()
    elif choice == '6':
        example_query_data()
    else:
        print("无效选择") 