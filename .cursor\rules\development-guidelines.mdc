---
description: 
globs: 
alwaysApply: true
---
# 开发规范指南

## 📋 开发规范总则

基于现有代码库 [app.py](mdc:app.py) 的分析，制定以下开发规范和最佳实践：

## 🎯 代码结构规范

### 文件组织
```
项目根目录/
├── app.py                    # 主应用文件，包含所有路由
├── requirements.txt          # Python依赖管理
├── package.json             # 前端依赖管理
├── utils/                   # 工具函数库
│   ├── sales_data.py        # 销售数据处理
│   ├── data_utils.py        # 通用数据工具
│   ├── file_utils.py        # 文件处理工具
│   ├── date_utils.py        # 日期处理工具
│   └── auth.py              # 认证相关工具
├── static/                  # 静态资源和数据
│   ├── db/                  # SQLite数据库文件
│   └── temp/                # 临时文件目录
├── templates/               # HTML模板文件
└── config/                  # 配置文件目录
```

### 模块分离原则
- **单一职责**: 每个模块只负责一个功能领域
- **高内聚**: 相关功能放在同一模块中
- **低耦合**: 模块间依赖最小化
- **可测试**: 便于单元测试和集成测试

## 🔐 权限管理规范

### 装饰器使用
```python
from functools import wraps

def admin_required(f):
    """检查用户是否为管理员的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user_role = request.cookies.get('userRole')
        if user_role != 'admin':
            return jsonify({"success": False, "message": "权限不足，需要管理员权限"}), 403
        return f(*args, **kwargs)
    return decorated_function
```

### 角色定义
- **admin**: 系统管理员，拥有所有权限
- **yunying**: 运营人员，负责日常运营数据
- **art**: 美工人员，负责任务管理
- **caigou**: 采购人员，负责采购相关功能
- **caiwu**: 财务人员，负责财务数据管理
- **kefu**: 客服人员，负责客服相关功能

### 权限验证
```python
# 在需要权限控制的路由上使用装饰器
@app.route('/api/users', methods=['POST'])
@admin_required
def create_user():
    # 创建用户逻辑
    pass
```

## 🌐 API 设计规范

### 路由命名
- 所有API路由使用 `/api/` 前缀
- 使用RESTful设计原则
- 路由名称使用小写字母和连字符

```python
# 正确示例
@app.route('/api/users', methods=['GET'])           # 获取用户列表
@app.route('/api/users/<username>', methods=['PUT']) # 更新用户
@app.route('/api/shop-trend-data', methods=['GET'])  # 获取店铺趋势数据
```

### HTTP方法使用
- **GET**: 获取数据，不改变服务器状态
- **POST**: 创建新资源
- **PUT**: 更新整个资源
- **PATCH**: 部分更新资源
- **DELETE**: 删除资源

### 响应格式
```python
# 成功响应
return jsonify({
    "success": True,
    "data": data,
    "message": "操作成功"
})

# 错误响应
return jsonify({
    "success": False,
    "message": "错误描述",
    "error_code": "ERROR_CODE"
}), 400
```

## 🗄️ 数据库操作规范

### SQLite 连接管理
```python
def get_db_connection(db_path):
    """获取数据库连接"""
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row  # 启用字典访问
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None
```

### 事务管理
```python
# 使用事务确保数据一致性
conn = get_db_connection(db_path)
try:
    conn.execute("BEGIN")
    # 执行多个操作
    conn.execute("INSERT INTO ...")
    conn.execute("UPDATE ...")
    conn.commit()
except Exception as e:
    conn.rollback()
    print(f"事务回滚: {e}")
finally:
    conn.close()
```

### 数据库文件位置
- **销售数据**: `static/db/sale_data.db`
- **SPU数据**: `static/db/spu_data.db`
- **违规数据**: `static/db/violation_data.db`

## 📁 文件处理规范

### 文件上传
```python
from werkzeug.utils import secure_filename
import uuid

def secure_filename(filename):
    """安全的文件名处理"""
    # 生成唯一文件名
    file_ext = os.path.splitext(filename)[1]
    unique_filename = str(uuid.uuid4()) + file_ext
    return unique_filename
```

### 文件存储结构
```
static/
├── uploads/                 # 用户上传文件
├── temp/                    # 临时文件
├── sale_data_xlsx/          # 销售数据Excel文件
├── spu_data_xlsx/           # SPU数据Excel文件
└── task_attachments/        # 任务附件
```

## 🔧 工具函数规范

### 日期处理
参考 [utils/date_utils.py](mdc:utils/date_utils.py):
```python
def standardize_date(date_str):
    """标准化日期格式"""
    # 统一转换为 'YYYY-MM-DD' 格式
    pass

def get_yesterday_date():
    """获取昨天日期"""
    yesterday = datetime.now() - timedelta(days=1)
    return yesterday.strftime('%Y-%m-%d')
```

### 数据处理
参考 [utils/data_utils.py](mdc:utils/data_utils.py):
```python
def clean_numeric_column(series):
    """清理数值列数据"""
    # 移除非数值字符，转换为数值类型
    pass

def calculate_growth_rate(current, previous):
    """计算增长率"""
    if previous == 0:
        return 0
    return ((current - previous) / previous) * 100
```

## 🔍 错误处理规范

### 异常捕获
```python
@app.route('/api/example', methods=['GET'])
def example_api():
    try:
        # 业务逻辑
        result = process_data()
        return jsonify({"success": True, "data": result})
    except ValueError as e:
        return jsonify({"success": False, "message": f"参数错误: {e}"}), 400
    except Exception as e:
        print(f"服务器错误: {e}")
        return jsonify({"success": False, "message": "服务器内部错误"}), 500
```

### 日志记录
```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 记录关键操作
logging.info(f"用户 {username} 执行了 {operation}")
logging.error(f"操作失败: {error_message}")
```

## 📊 数据验证规范

### 输入验证
```python
def validate_date_range(start_date, end_date):
    """验证日期range"""
    if not start_date or not end_date:
        return False, "日期不能为空"
    
    try:
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')
        if start > end:
            return False, "开始日期不能晚于结束日期"
        return True, None
    except ValueError:
        return False, "日期格式错误"
```

### 数据清理
```python
def clean_shop_data(data):
    """清理店铺数据"""
    # 移除空值
    data = data.dropna()
    
    # 标准化字符串
    data['shop_name'] = data['shop_name'].str.strip()
    
    # 转换数据类型
    numeric_columns = ['sales_amount', 'sales_quantity']
    for col in numeric_columns:
        data[col] = pd.to_numeric(data[col], errors='coerce')
    
    return data
```

## 🎨 前端交互规范

### Cookie 管理
```python
# 设置Cookie
response = make_response(jsonify({"success": True}))
response.set_cookie('userRole', user_role, max_age=30*24*60*60)  # 30天

# 读取Cookie
user_role = request.cookies.get('userRole')
username = request.cookies.get('username')
```

### 跨域处理
```python
from flask_cors import CORS

# 启用跨域支持
CORS(app, supports_credentials=True)
```

## 🚀 性能优化规范

### 数据库优化
- 使用索引提高查询性能
- 分批处理大量数据（每批1000条）
- 使用连接池管理数据库连接

### 内存管理
```python
# 处理大文件时分块读取
def process_large_file(file_path, chunk_size=1000):
    for chunk in pd.read_csv(file_path, chunksize=chunk_size):
        # 处理数据块
        process_chunk(chunk)
```

### 缓存策略
```python
# 简单的内存缓存
cache = {}

def get_cached_data(key):
    if key in cache:
        return cache[key]
    
    data = fetch_data_from_db(key)
    cache[key] = data
    return data
```

## 📝 代码注释规范

### 函数注释
```python
def calculate_dashboard_data(sales_data, date_range=None):
    """
    计算仪表盘数据
    
    参数:
        sales_data (dict): 销售数据字典
        date_range (list, optional): 日期范围列表
    
    返回:
        dict: 包含仪表盘统计数据的字典
    
    异常:
        ValueError: 当输入数据格式错误时抛出
    """
    pass
```

### 行内注释
```python
# 读取账号信息，如果文件不存在则创建默认账号
accounts_data = read_accounts()

# 使用GBK编码读取CSV文件，处理中文字符
shops = read_csv_file(file_path)
```

## 🔄 版本控制规范

### 提交信息格式
```
feat: 添加新功能
fix: 修复bug
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 其他杂项
```

### 分支管理
- **main**: 主分支，稳定版本
- **develop**: 开发分支，集成新功能
- **feature/***: 功能分支
- **hotfix/***: 紧急修复分支

## 🧪 测试规范

### 单元测试
```python
import unittest

class TestDataUtils(unittest.TestCase):
    def test_calculate_growth_rate(self):
        """测试增长率计算"""
        result = calculate_growth_rate(120, 100)
        self.assertEqual(result, 20.0)
    
    def test_clean_numeric_column(self):
        """测试数值清理"""
        # 测试逻辑
        pass
```

### 集成测试
- 测试API端点的完整流程
- 测试数据导入的完整流程
- 测试用户权限控制

## 📚 文档规范

### README文档
- 每个重要模块都应有README文档
- 参考现有的 [SALE_DATA_IMPORT_README.md](mdc:SALE_DATA_IMPORT_README.md)
- 包含安装、使用、示例、FAQ等内容

### API文档
- 详细描述每个API的用途、参数、返回值
- 提供调用示例
- 说明错误码和处理方式

