import os
import pandas as pd
from config.settings import ENCODINGS

def get_operator_shop_mapping():
    """读取店铺-账号信息，建立运营人员 -> 店铺列表的映射
    
    Returns:
        tuple: (
            mapping: dict - 运营人员到店铺列表的映射,
            operators: list - 运营人员列表,
            shop_to_brand: dict - 店铺到品牌的映射,
            brand_to_shops: dict - 品牌到店铺列表的映射,
            brands: list - 品牌列表
        )
    """
    mapping = {}
    operators = set()
    brands = set()  # 存储所有品牌
    shop_to_brand = {}  # 店铺到品牌的映射
    brand_to_shops = {}  # 品牌到店铺的映射
    
    file_path = '店铺-账号信息.csv'
    print(f"尝试读取运营人员映射文件: {os.path.abspath(file_path)}")
    if not os.path.exists(file_path):
        print(f"错误: {file_path} 文件不存在，无法进行运营人员筛选。")
        return {}, [], {}, {}, []

    try:
        df = None
        for encoding in ENCODINGS:
            try:
                # 重要：指定 dtype=str 防止数字被错误解析, keep_default_na=False 防止空字符串被视为NaN
                df = pd.read_csv(file_path, encoding=encoding, dtype=str, keep_default_na=False)
                print(f"成功使用 {encoding} 编码读取CSV文件: {file_path}")
                break
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"使用 {encoding} 编码读取CSV文件时发生错误: {str(e)}")
                continue
        
        if df is None:
             print(f"错误: 尝试了所有编码但无法读取文件: {file_path}")
             return {}, [], {}, {}, []

        # 尝试通过列名查找，如果失败则回退到索引
        shop_col_name = None
        operator_col_name = None
        brand_col_name = None  # 品牌列名
        possible_shop_names = ['店铺名称', '店铺名']
        possible_operator_names = ['运营', '运营人员']
        possible_brand_names = ['品牌', '品牌名称']

        for name in possible_shop_names:
            if name in df.columns:
                shop_col_name = name
                print(f"找到店铺列名: {shop_col_name}")
                break

        for name in possible_operator_names:
            if name in df.columns:
                operator_col_name = name
                print(f"找到运营列名: {operator_col_name}")
                break
                
        for name in possible_brand_names:
            if name in df.columns:
                brand_col_name = name
                print(f"找到品牌列名: {brand_col_name}")
                break
                
        # 如果没有找到店铺列名，尝试使用索引0（A列）
        if not shop_col_name and df.shape[1] > 0:
            shop_col_name = df.columns[0] # 通常第一列是店铺名称
            print(f"未找到店铺列名，使用第一列: {shop_col_name}")
            
        # 如果没有找到运营列名，尝试使用索引3（D列）    
        if not operator_col_name and df.shape[1] > 3:
            operator_col_name = df.columns[3] # 通常第四列是运营
            print(f"未找到运营列名，使用第四列: {operator_col_name}")
            
        # 如果没有找到品牌列名，尝试使用H列（索引7）
        if not brand_col_name and df.shape[1] > 7:
            brand_col_name = df.columns[7]  # H列，索引7
            print(f"未找到品牌列名，使用H列: {brand_col_name}")
            
        if not shop_col_name:
            print("错误: 无法确定店铺名称列，无法进行运营人员映射。")
            return {}, [], {}, {}, []

        processed_rows = 0
        
        for index, row in df.iterrows():
            try:
                shop_name = row[shop_col_name].strip() if shop_col_name and pd.notna(row[shop_col_name]) else ""
                
                # 获取运营人员，如果列不存在或值为空，则设为"未分配"
                if operator_col_name and pd.notna(row[operator_col_name]):
                    operator = row[operator_col_name].strip()
                    if not operator:
                         operator = "未分配"
                else:
                    operator = "未分配"
                    
                # 获取品牌，如果列不存在或值为空，则设为"未分类"
                if brand_col_name and pd.notna(row[brand_col_name]):
                    brand = row[brand_col_name].strip()
                    if not brand:
                        brand = "未分类"
                else:
                    brand = "未分类"
                
                if not shop_name: # 跳过店铺名为空的行
                    continue

                operators.add(operator)
                brands.add(brand)  # 添加到品牌集合
                
                # 建立店铺-品牌映射关系
                shop_to_brand[shop_name] = brand
                
                # 建立品牌-店铺映射关系
                if brand not in brand_to_shops:
                    brand_to_shops[brand] = []
                if shop_name not in brand_to_shops[brand]:
                    brand_to_shops[brand].append(shop_name)
                
                if operator not in mapping:
                    mapping[operator] = []
                if shop_name not in mapping[operator]:
                    mapping[operator].append(shop_name)
                processed_rows += 1
            except KeyError as ke:
                print(f"处理行 {index+2} 时发生 KeyError: {ke} - 请检查列名或索引是否正确。")
                continue # 跳过此行
            except Exception as row_e:
                 print(f"处理行 {index+2} 时发生错误: {row_e}")
                 continue # 跳过此行
                 
        print(f"处理完成，共处理 {processed_rows} 行有效数据。")
        print(f"运营人员映射创建成功，共 {len(operators)} 个运营， {len(mapping)} 个有效映射。")
        print(f"品牌映射创建成功，共 {len(brands)} 个品牌， {len(brand_to_shops)} 个有效映射。")
        
        return mapping, sorted(list(operators)), shop_to_brand, brand_to_shops, sorted(list(brands))

    except Exception as e:
        print(f"读取或处理 {file_path} 文件时发生严重错误: {e}")
        import traceback
        traceback.print_exc()
        return {}, [], {}, {}, []

def calculate_shop_comparison(shops_list, yesterday_data, day_before_yesterday_data, week_ago_yesterday_data):
    """计算各店铺的同比环比数据
    
    Args:
        shops_list: 店铺列表
        yesterday_data: 昨天的数据
        day_before_yesterday_data: 前天的数据
        week_ago_yesterday_data: 上周昨天的数据
        
    Returns:
        dict: 店铺的同比环比数据
    """
    comparison_data = {}
    
    for shop in shops_list:
        # 初始化数据结构
        comparison_data[shop] = {
            "sales": {"yoy": 0, "mom": 0, "current": 0, "yoyRef": 0, "momRef": 0},
            "promotion": {"yoy": 0, "mom": 0, "current": 0, "yoyRef": 0, "momRef": 0},
            "roi": {"yoy": 0, "mom": 0, "current": 0, "yoyRef": 0, "momRef": 0}
        }
        
        # 获取昨天的数据（当前值）
        yesterday_sales = yesterday_data['sales'].get(shop, 0)
        yesterday_refunds = yesterday_data['refunds'].get(shop, 0)
        yesterday_promo = yesterday_data['promo'].get(shop, 0)
        yesterday_net_sales = max(0, yesterday_sales - yesterday_refunds)  # 净销售额
        
        # 获取前天的数据（同比参考值）
        dby_sales = day_before_yesterday_data['sales'].get(shop, 0)
        dby_refunds = day_before_yesterday_data['refunds'].get(shop, 0)
        dby_promo = day_before_yesterday_data['promo'].get(shop, 0)
        dby_net_sales = max(0, dby_sales - dby_refunds)  # 净销售额
        
        # 获取上周昨天的数据（环比参考值）
        way_sales = week_ago_yesterday_data['sales'].get(shop, 0)
        way_refunds = week_ago_yesterday_data['refunds'].get(shop, 0)
        way_promo = week_ago_yesterday_data['promo'].get(shop, 0)
        way_net_sales = max(0, way_sales - way_refunds)  # 净销售额
        
        # 保存当前值
        comparison_data[shop]['sales']['current'] = yesterday_net_sales
        comparison_data[shop]['promotion']['current'] = yesterday_promo
        comparison_data[shop]['roi']['current'] = yesterday_net_sales / yesterday_promo if yesterday_promo > 0 else 0
        
        # 保存参考值
        comparison_data[shop]['sales']['yoyRef'] = dby_net_sales
        comparison_data[shop]['promotion']['yoyRef'] = dby_promo
        comparison_data[shop]['roi']['yoyRef'] = dby_net_sales / dby_promo if dby_promo > 0 else 0
        
        comparison_data[shop]['sales']['momRef'] = way_net_sales
        comparison_data[shop]['promotion']['momRef'] = way_promo
        comparison_data[shop]['roi']['momRef'] = way_net_sales / way_promo if way_promo > 0 else 0
        
        # 计算同比变化率 (昨天 vs 前天)
        if dby_net_sales > 0:
            comparison_data[shop]['sales']['yoy'] = ((yesterday_net_sales - dby_net_sales) / dby_net_sales) * 100
        else:
            comparison_data[shop]['sales']['yoy'] = 100 if yesterday_net_sales > 0 else 0
        
        if dby_promo > 0:
            comparison_data[shop]['promotion']['yoy'] = ((yesterday_promo - dby_promo) / dby_promo) * 100
        else:
            comparison_data[shop]['promotion']['yoy'] = 100 if yesterday_promo > 0 else 0
        
        current_roi = comparison_data[shop]['roi']['current']
        yoy_ref_roi = comparison_data[shop]['roi']['yoyRef']
        
        if yoy_ref_roi > 0:
            comparison_data[shop]['roi']['yoy'] = ((current_roi - yoy_ref_roi) / yoy_ref_roi) * 100
        else:
            comparison_data[shop]['roi']['yoy'] = 100 if current_roi > 0 else 0
        
        # 计算环比变化率 (昨天 vs 上周昨天)
        if way_net_sales > 0:
            comparison_data[shop]['sales']['mom'] = ((yesterday_net_sales - way_net_sales) / way_net_sales) * 100
        else:
            comparison_data[shop]['sales']['mom'] = 100 if yesterday_net_sales > 0 else 0
        
        if way_promo > 0:
            comparison_data[shop]['promotion']['mom'] = ((yesterday_promo - way_promo) / way_promo) * 100
        else:
            comparison_data[shop]['promotion']['mom'] = 100 if yesterday_promo > 0 else 0
        
        mom_ref_roi = comparison_data[shop]['roi']['momRef']
        
        if mom_ref_roi > 0:
            comparison_data[shop]['roi']['mom'] = ((current_roi - mom_ref_roi) / mom_ref_roi) * 100
        else:
            comparison_data[shop]['roi']['mom'] = 100 if current_roi > 0 else 0
    
    return comparison_data 