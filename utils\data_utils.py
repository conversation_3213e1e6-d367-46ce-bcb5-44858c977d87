def safe_float(value, default=0.0):
    """安全地将值转换为浮点数
    
    Args:
        value: 要转换的值
        default: 转换失败时的默认值
        
    Returns:
        float: 转换后的浮点数
    """
    try:
        if isinstance(value, str):
            value = value.strip()
            if value == '':
                return default
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_int(value, default=0):
    """安全地将值转换为整数
    
    Args:
        value: 要转换的值
        default: 转换失败时的默认值
        
    Returns:
        int: 转换后的整数
    """
    try:
        if isinstance(value, str):
            value = value.strip()
            if value == '':
                return default
        return int(float(value))
    except (ValueError, TypeError):
        return default

def validate_required_fields(data, required_fields):
    """验证必填字段
    
    Args:
        data: 要验证的数据字典
        required_fields: 必填字段列表
        
    Returns:
        tuple: (是否验证通过, 缺失字段列表)
    """
    missing_fields = []
    for field in required_fields:
        if field not in data or data[field] is None or data[field] == '':
            missing_fields.append(field)
    return len(missing_fields) == 0, missing_fields

def format_percentage(value, decimal_places=2):
    """格式化百分比
    
    Args:
        value: 要格式化的值
        decimal_places: 小数位数
        
    Returns:
        str: 格式化后的百分比字符串
    """
    try:
        return f"{round(float(value), decimal_places)}%"
    except (ValueError, TypeError):
        return "0%"

def format_currency(value, decimal_places=2):
    """格式化货币
    
    Args:
        value: 要格式化的值
        decimal_places: 小数位数
        
    Returns:
        str: 格式化后的货币字符串
    """
    try:
        return f"¥{round(float(value), decimal_places):,.2f}"
    except (ValueError, TypeError):
        return "¥0.00"

def calculate_growth_rate(current_value, previous_value):
    """计算增长率
    
    Args:
        current_value: 当前值
        previous_value: 前一个值
        
    Returns:
        float: 增长率（百分比）
    """
    try:
        current = float(current_value)
        previous = float(previous_value)
        if previous == 0:
            return 100 if current > 0 else 0
        return ((current - previous) / previous) * 100
    except (ValueError, TypeError):
        return 0 