{"messages": [{"id": 10, "type": "feature", "title": "采购部", "details": " SPU上架店铺运营->可查询", "priority": "low", "status": "completed", "date": "2025-03-30", "user": "admin", "admin_reply": null}, {"id": 9, "type": "feature", "title": "采购部", "details": "新上链接SPU明细查询功能", "priority": "medium", "status": "completed", "date": "2025-03-30", "user": "admin", "admin_reply": null}, {"id": 8, "type": "feature", "title": "财务部", "details": "OA审批自动采集录入", "priority": "medium", "status": "pending", "date": "2025-03-30", "user": "admin", "admin_reply": null}, {"id": 7, "type": "data", "title": "采购部", "details": "SPU上架链接数量数据", "priority": "medium", "status": "completed", "date": "2025-03-28", "user": "admin", "admin_reply": null}, {"id": 1, "type": "feature", "title": "机器人新增", "details": "自动处理客服留言：订单挽回登记机器人开发。", "priority": "medium", "status": "completed", "date": "2025-03-25", "user": "俞莉垚", "admin_reply": null}, {"id": 2, "type": "data", "title": "SPU往年今年数据对比", "details": "SPU往年今年数据对比", "priority": "medium", "status": "pending", "date": "2025-03-26", "user": "老板", "admin_reply": null}, {"id": 4, "type": "feature", "title": "店铺数据批量比较功能", "details": "希望能够同时选择多个店铺，进行数据对比分析，方便发现业绩差异。", "priority": "medium", "status": "pending", "date": "2025-03-27", "user": "测试", "admin_reply": null}, {"id": 5, "type": "other", "title": "系统使用手册更新", "details": "希望能更新系统使用手册，增加一些新功能的操作指南，尤其是数据分析部分。", "priority": "low", "status": "pending", "date": "2025-03-27", "user": "测试", "admin_reply": "已更新使用手册，新版本可在个人中心下载。"}], "next_id": 12}