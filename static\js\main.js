/**
 * main.js - 应用程序主入口
 * 负责初始化应用状态、检查用户登录状态、加载模块等
 */

import { checkLoginStatus, getUserRole, getUsername } from './auth.js';
import { showNotification, loadScript } from './utils.js';
import AppState from './state.js';
import { generateDashboard } from '../generateDashboard.js';

// 检查登录状态
if (!checkLoginStatus()) {
    // 如果登录检查函数返回false，已经重定向到登录页面
    throw new Error('未登录');
}

// 初始化应用程序状态
initializeApp();

/**
 * 初始化应用程序
 */
function initializeApp() {
    try {
        // 设置当前用户信息
        const username = getUsername();
        const userRole = getUserRole();
        
        // 更新用户信息显示
        updateUserInfoDisplay(username, userRole);
        
        // 设置侧边栏事件
        setupSidebarEvents();
        
        // 加载默认模块 (数据概览)
        loadModule('dashboard');
        
        // 标记应用初始化完成
        AppState.updateState('app', { initialized: true });
        
    } catch (error) {
        console.error('应用初始化失败:', error);
        showNotification('应用初始化失败，请刷新页面重试', 'error');
    }
}

/**
 * 更新用户信息显示
 */
function updateUserInfoDisplay(username, userRole) {
    const userNameElement = document.querySelector('.user-name');
    const userRoleElement = document.querySelector('.user-role');
    
    if (userNameElement) {
        userNameElement.textContent = username;
    }
    
    if (userRoleElement) {
        userRoleElement.textContent = userRole === 'admin' ? '管理员' : '运营';
    }
}

/**
 * 设置侧边栏事件
 */
function setupSidebarEvents() {
    // 为侧边栏菜单项添加点击事件
    document.querySelectorAll('.sidebar-menu-item').forEach(item => {
        item.addEventListener('click', function() {
            const moduleId = this.getAttribute('data-module');
            if (moduleId) {
                // 移除所有激活状态
                document.querySelectorAll('.sidebar-menu-item').forEach(menuItem => {
                    menuItem.classList.remove('active');
                });
                
                // 添加当前项的激活状态
                this.classList.add('active');
                
                // 加载对应模块
                loadModule(moduleId);
            }
        });
    });
    
    // 为退出按钮添加点击事件
    const logoutButton = document.querySelector('.logout-button');
    if (logoutButton) {
        logoutButton.addEventListener('click', function() {
            // 删除相关cookie并重定向到登录页面
            document.cookie = 'loginAuth=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            document.cookie = 'userRole=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            document.cookie = 'username=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            window.location.href = '/login.html';
        });
    }
    
    // 侧边栏折叠按钮事件
    const toggleButton = document.querySelector('.sidebar-toggle');
    if (toggleButton) {
        toggleButton.addEventListener('click', function() {
            document.querySelector('.sidebar').classList.toggle('collapsed');
            document.querySelector('.container').classList.toggle('expanded');
        });
    }
}

/**
 * 加载指定模块
 */
async function loadModule(moduleId) {
    // 更新当前模块状态
    AppState.updateState('app', { 
        currentModule: moduleId,
        isLoading: true 
    });
    
    try {
        // 首先清空内容区域
        const container = document.querySelector('.container');
        if (!container) throw new Error('未找到内容容器');
        
        // 添加加载指示器
        container.innerHTML = `
            <div class="loading-container">
                <div class="spinner"></div>
                <p>正在加载${getModuleName(moduleId)}...</p>
            </div>
        `;
        
        // 根据模块ID加载对应的内容
        switch (moduleId) {
            case 'dashboard':
                await generateDashboard();
                break;
                
            case 'shop_management':
                // 动态导入店铺管理模块
                const { generateShopCards } = await import('../generateShopCards.js');
                await generateShopCards();
                break;
                
            case 'product_management':
                // 动态导入产品管理模块
                const { generateProductManagement } = await import('../generateProductManagement.js');
                await generateProductManagement();
                break;
                
            case 'sales_stats':
                // 动态导入销售统计模块
                const { generateSalesStats } = await import('../generateSalesStats.js');
                await generateSalesStats();
                break;
                
            case 'finance_management':
                // 动态导入财务管理模块
                const { generateFinanceManagement } = await import('../generateFinanceManagement.js');
                await generateFinanceManagement();
                break;
                
            case 'link_management':
                // 动态导入链接管理模块
                const { generateLinkManagement } = await import('../generateLinkManagement.js');
                await generateLinkManagement();
                break;
                
            case 'team_management':
                // 动态导入团队管理模块
                const { generateTeamManagement } = await import('../generateTeamManagement.js');
                await generateTeamManagement();
                break;
                
            case 'spu_trend':
                // 动态导入SPU趋势模块
                const { generateSpuTrend } = await import('../generateSpuTrend.js');
                await generateSpuTrend();
                break;
                
            case 'message_board':
                // 动态导入留言板模块
                const { generateMessageBoard } = await import('../generateMessageBoard.js');
                await generateMessageBoard();
                break;
                
            default:
                container.innerHTML = `
                    <div class="error-message">
                        <h2>模块未找到</h2>
                        <p>无法加载指定的模块: ${moduleId}</p>
                    </div>
                `;
        }
        
        // 更新完成状态
        AppState.updateState('app', { isLoading: false });
        
    } catch (error) {
        console.error(`加载模块 ${moduleId} 失败:`, error);
        
        const container = document.querySelector('.container');
        if (container) {
            container.innerHTML = `
                <div class="error-message">
                    <h2>加载失败</h2>
                    <p>${error.message || '模块加载时发生错误'}</p>
                    <button class="retry-button">重试</button>
                </div>
            `;
            
            // 添加重试按钮事件
            const retryButton = container.querySelector('.retry-button');
            if (retryButton) {
                retryButton.addEventListener('click', () => loadModule(moduleId));
            }
        }
        
        // 更新错误状态
        AppState.updateState('app', { 
            isLoading: false,
            lastError: error.message 
        });
        
        // 显示通知
        showNotification(`加载${getModuleName(moduleId)}失败: ${error.message}`, 'error');
    }
}

/**
 * 获取模块名称
 */
function getModuleName(moduleId) {
    const moduleNames = {
        dashboard: '数据概览',
        shop_management: '店铺管理',
        product_management: '商品管理',
        sales_stats: '销售统计',
        finance_management: '财务管理',
        link_management: '链接管理',
        team_management: '团队管理',
        spu_trend: 'SPU趋势',
        message_board: '留言板'
    };
    
    return moduleNames[moduleId] || moduleId;
}

// 添加样式
(function addStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 300px;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: #3b82f6;
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .error-message {
            background-color: #fee2e2;
            border-left: 4px solid #ef4444;
            padding: 20px;
            border-radius: 4px;
            margin: 20px 0;
        }
        
        .error-message h2 {
            color: #b91c1c;
            margin-top: 0;
        }
        
        .retry-button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
            margin-top: 10px;
        }
        
        .retry-button:hover {
            background-color: #2563eb;
        }
    `;
    document.head.appendChild(style);
})(); 