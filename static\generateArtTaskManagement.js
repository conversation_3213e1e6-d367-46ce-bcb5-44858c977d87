/**
 * 美工任务管理系统 - 工作流程系统
 * 实现美工与运营的协作流程
 */

/* Art Task Management 功能 */

// 初始化全局变量，避免重复声明
if (typeof artTasks === 'undefined') {
    var artTasks = [];
}
if (typeof artDesigners === 'undefined') {
    var artDesigners = [];
}
if (typeof taskOverview === 'undefined') {
    var taskOverview = null;
}

// 任务状态定义
const TASK_STATUS = {
    PENDING: 1, // 待接收
    IN_PROGRESS: 2, // 进行中
    PENDING_APPROVAL: 3, // 待确认
    COMPLETED: 4, // 已完成
    CANCELLED: 5 // 已取消
};

let taskCategories = [
    { id: 1, name: '主图设计', color: '#4b6cb7' },
    { id: 2, name: '详情页设计', color: '#1e88e5' },
    { id: 3, name: '店招设计', color: '#43a047' },
    { id: 4, name: 'LOGO设计', color: '#fb8c00' },
    { id: 5, name: '品牌物料', color: '#8e24aa' }
];

// 任务描述模板，根据任务类别提供对应的模板
let taskDescriptionTemplates = {
    1: `【主图设计】需求说明：
1. 商品名称：[填写商品名称]
2. 主要卖点：[填写商品主要卖点，最多3点]
3. 目标人群：[填写目标客户群体]
4. 设计风格：[简约/复古/奢华/卡通等]
5. 色调要求：[填写颜色要求]
6. 尺寸规格：800x800px，72dpi

其他说明：`,
    2: `【详情页设计】需求说明：
1. 商品名称：[填写商品名称]
2. 商品类型：[填写商品类型]
3. 需要突出的卖点：
   - [填写卖点1]
   - [填写卖点2]
   - [填写卖点3]
4. 需要包含的内容：
   - 商品展示图
   - 尺寸参数表
   - 使用场景
   - 购买须知
5. 参考风格：[填写参考风格]

其他说明：`,
    3: `【店招设计】需求说明：
1. 店铺名称：[填写店铺名称]
2. 店铺主营：[填写店铺主营商品]
3. 店铺定位：[填写店铺定位，如高端/平价/小众等]
4. 设计风格：[简约/复古/奢华/卡通等]
5. 尺寸规格：950x150px，72dpi
6. 需要包含元素：[Logo/促销信息/联系方式等]

其他说明：`,
    4: `【LOGO设计】需求说明：
1. 品牌名称：[填写品牌名称]
2. 所属行业：[填写所属行业]
3. 品牌理念：[填写品牌核心理念]
4. 目标受众：[填写目标客户群体]
5. 色彩偏好：[填写色彩偏好]
6. 参考案例：[填写参考案例，如有]
7. 需要避免的元素：[填写需要避免的元素]

其他说明：`,
    5: `【品牌物料】需求说明：
1. 物料类型：[名片/宣传册/海报/展架等]
2. 品牌名称：[填写品牌名称]
3. 使用场景：[填写使用场景]
4. 核心信息：[填写必须包含的核心信息]
5. 尺寸规格：[填写尺寸要求]
6. 材质要求：[填写材质要求，如有]
7. 数量：[填写需要的数量]

其他说明：`
};

let taskPriorities = [
    { id: 1, name: '低', color: '#78909c' },
    { id: 2, name: '中', color: '#4b6cb7' },
    { id: 3, name: '高', color: '#f44336' }
];

let taskStatuses = [
    { id: TASK_STATUS.PENDING, name: '待接收', color: '#78909c' },
    { id: TASK_STATUS.IN_PROGRESS, name: '进行中', color: '#4b6cb7' },
    { id: TASK_STATUS.PENDING_APPROVAL, name: '待确认', color: '#fb8c00' },
    { id: TASK_STATUS.COMPLETED, name: '已完成', color: '#43a047' },
    { id: TASK_STATUS.CANCELLED, name: '已取消', color: '#f44336' }
];

// 负责任务的用户列表（美工角色）
// artDesigners 已经在文件开头声明

// 获取用户信息
function getCurrentUserInfo() {
    // 更健壮的Cookie解析
    
    const cookieObj = document.cookie.split(';').reduce((res, cookie) => {
        const [key, value] = cookie.trim().split('=');
        res[key] = value ? decodeURIComponent(value) : '';
        return res;
    }, {});

    const username = cookieObj.user || cookieObj.name || '用户';
    const role = cookieObj.userRole || 'operator';
    let roleName = cookieObj.roleName || (
        role === 'admin' ? '管理员' : 
        role === 'art' ? '美工' : 
        role === 'yunying' ? '运营' : '操作员'
    );


    // 调试日志
    console.log("解析后的Cookie对象:", cookieObj);
    console.log("当前用户信息:", { username, role, roleName });

    return { username, role, roleName };
}

// 生成唯一ID
function generateUniqueId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
}

// 格式化日期
function formatDate(date) {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// 计算剩余时间
function getRemainingTime(deadline, statusId) {
    // 如果任务已完成，不显示剩余时间
    if (statusId === 4) {  // 4代表已完成状态
        return '';
    }
    
    const now = new Date();
    const deadlineDate = new Date(deadline);
    const diffTime = deadlineDate - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
        return `<span class="overdue">已逾期 ${Math.abs(diffDays)} 天</span>`;
    } else if (diffDays === 0) {
        return '<span class="due-today">今日到期</span>';
    } else {
        return `<span class="remaining">剩余 ${diffDays} 天</span>`;
    }
}

// 保存任务到本地存储
function saveTasksToLocalStorage() {
    localStorage.setItem('artTasks', JSON.stringify(artTasks));
}

// 从本地存储加载任务
function loadTasksFromLocalStorage() {
    const savedTasks = localStorage.getItem('artTasks');
    if (savedTasks) {
        artTasks = JSON.parse(savedTasks);
    } else {
        // 初始化空任务数组，不再使用示例任务
        artTasks = [];
        saveTasksToLocalStorage();
    }
}

// 从API加载任务数据
function loadTasksFromAPI() {
    fetch('/api/art-tasks')
        .then(response => response.json())
        .then(data => {
            artTasks = data;
            renderKanbanView();
            renderListView();
        })
        .catch(error => {
            console.error('加载任务失败:', error);
            // 如果API请求失败，回退到localStorage
            loadTasksFromLocalStorage();
        });
}

// 保存任务到API
function saveTaskToAPI(task, isNew = false) {
    const method = isNew ? 'POST' : 'PUT';
    const url = isNew ? '/api/art-tasks' : `/api/art-tasks/${task.id}`;
    
    return fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(task)
    })
    .then(response => response.json())
    .catch(error => {
        console.error('保存任务失败:', error);
        // 如果API请求失败，回退到localStorage
        if (isNew) {
            artTasks.push(task);
        } else {
            const taskIndex = artTasks.findIndex(t => t.id === task.id);
            if (taskIndex !== -1) {
                artTasks[taskIndex] = task;
            }
        }
        saveTasksToLocalStorage();
        return task;
    });
}

// 删除API中的任务
function deleteTaskFromAPI(taskId) {
    return fetch(`/api/art-tasks/${taskId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .catch(error => {
        console.error('删除任务失败:', error);
        // 如果API请求失败，回退到localStorage
        const taskIndex = artTasks.findIndex(task => task.id === taskId);
        if (taskIndex !== -1) {
            artTasks.splice(taskIndex, 1);
            saveTasksToLocalStorage();
        }
    });
}

// 更新API中的任务状态
function updateTaskStatusToAPI(taskId, newStatusId) {
    return fetch(`/api/art-tasks/${taskId}/status`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ statusId: newStatusId })
    })
    .then(response => response.json())
    .catch(error => {
        console.error('更新任务状态失败:', error);
        // 如果API请求失败，回退到localStorage
        const taskIndex = artTasks.findIndex(task => task.id === taskId);
        if (taskIndex !== -1) {
            artTasks[taskIndex].statusId = parseInt(newStatusId);
            saveTasksToLocalStorage();
        }
    });
}

// 添加评论到API
function addCommentToAPI(taskId, user, text) {
    const comment = {
        user,
        text,
        time: new Date().toISOString()
    };
    
    return fetch(`/api/art-tasks/${taskId}/comments`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(comment)
    })
    .then(response => response.json())
    .catch(error => {
        console.error('添加评论失败:', error);
        // 如果API请求失败，回退到localStorage
        const taskIndex = artTasks.findIndex(task => task.id === taskId);
        if (taskIndex !== -1) {
            const now = new Date();
            const timeString = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
            
            artTasks[taskIndex].comments.push({
                user,
                text,
                time: timeString
            });
            
            saveTasksToLocalStorage();
        }
    });
}

// 上传文件到API
function uploadFilesToAPI(taskId, files) {
    return new Promise((resolve, reject) => {
        // 使用FormData对象上传文件
        const formData = new FormData();
        formData.append('taskId', taskId);
        
        // 添加每个文件到formData
        for (let i = 0; i < files.length; i++) {
            // 使用原始文件名而不是只用扩展名
            formData.append('files', files[i]);
        }
        
        // 发送请求到服务器
        fetch('/api/tasks/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('文件上传成功');
                resolve(data);
            } else {
                console.error('文件上传失败:', data.message);
                reject(data.message);
            }
        })
        .catch(error => {
            console.error('文件上传错误:', error);
            reject(error);
        });
    });
}

// 下载文件
function downloadFile(taskId, filename) {
    // 构建下载URL
    const downloadUrl = `/api/tasks/download?taskId=${taskId}&filename=${encodeURIComponent(filename)}`;
    
    // 创建一个隐藏的a标签
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = downloadUrl;
    
    // 设置下载的文件名
    a.download = filename;
    
    // 添加到DOM，触发点击，然后移除
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
}

// 加载美工用户列表
function loadArtDesigners() {
    fetch('/api/users/filtered?role=art')
        .then(response => response.json())
        .then(data => {
            if (data && data.length > 0) {
                artDesigners = data.map(user => ({
                    username: user.user,
                    displayName: user.user,
                    role: user.role || 'art'
                }));
                
                // 更新任务分配下拉列表
                updateAssigneeSelect();
            } else {
                // 如果API返回为空，尝试直接从宜承账号.json加载
                return fetch('/宜承账号.json')
                    .then(response => response.json())
                    .then(accounts => {
                        const designers = accounts.zhanghu.filter(user => user.role === 'art');
                        artDesigners = designers.map(user => ({
                            username: user.user,
                            displayName: user.user, 
                            role: user.role
                        }));
                        
                        // 更新任务分配下拉列表
                        updateAssigneeSelect();
                    });
            }
        })
        .catch(error => {
            console.error('加载美工用户列表失败:', error);
            // 尝试直接从宜承账号.json加载
            fetch('/宜承账号.json')
                .then(response => response.json())
                .then(accounts => {
                    const designers = accounts.zhanghu.filter(user => user.role === 'art');
                    artDesigners = designers.map(user => ({
                        username: user.user,
                        displayName: user.user,
                        role: user.role
                    }));
                    
                    // 更新任务分配下拉列表
                    updateAssigneeSelect();
                })
                .catch(err => {
                    console.error('无法加载宜承账号.json:', err);
                });
        });
}

// 更新任务分配下拉列表
function updateAssigneeSelect() {
    const assigneeSelect = document.getElementById('taskAssignee');
    if (assigneeSelect) {
        // 保留第一个选项（空选项）
        const emptyOption = assigneeSelect.options[0];
        assigneeSelect.innerHTML = '';
        assigneeSelect.appendChild(emptyOption);
        
        // 添加美工用户选项
        artDesigners.forEach(designer => {
            const option = document.createElement('option');
            option.value = designer.username;
            option.textContent = `${designer.displayName} (${designer.role})`;
            assigneeSelect.appendChild(option);
        });
    }
}

// 替换原有的创建新任务函数
function createNewTask(taskData) {
    const newTask = {
        id: generateUniqueId(),
        title: taskData.title,
        description: taskData.description,
        categoryId: parseInt(taskData.categoryId),
        priorityId: parseInt(taskData.priorityId),
        statusId: 1, // 初始状态：待接收
        createdBy: taskData.createdBy,
        assignedTo: taskData.assignedTo || '',
        createdAt: formatDate(new Date()),
        deadline: taskData.deadline,
        comments: [],
        attachments: [], // 初始化为空数组，不再从表单直接获取
        references: taskData.references || []
    };  
    const userInfo = getCurrentUserInfo();
    if (userInfo.role == 'art') {
        showToast('权限不足', '美工没有权限创建任务', 'error');
        return Promise.reject('权限不足');
    }
    // 保存到API
    return saveTaskToAPI(newTask, true).then(() => {
        // 如果有成果附件，上传文件
        const uploadPromises = [];
        if (taskData.files && taskData.files.length > 0) {
            uploadPromises.push(uploadFilesToAPI(newTask.id, taskData.files));
        }
        // 新增：如果有关联参考附件，上传文件
        if (taskData.referenceFiles && taskData.referenceFiles.length > 0) {
            // 假设 uploadFilesToAPI 可以处理，或者需要调整后端逻辑
            uploadPromises.push(uploadFilesToAPI(newTask.id, taskData.referenceFiles)); 
        }
        // 等待所有上传完成（如果需要确保上传成功再返回）
        return Promise.all(uploadPromises).then(() => newTask);
    });
}

// 替换原有的更新任务函数
function updateTask(taskId, updatedData) {
    const taskIndex = artTasks.findIndex(task => task.id === taskId);
    if (taskIndex !== -1) {
        // 创建更新后的任务对象，但不包含文件对象和附件数组（如果附件由服务器管理）
        const { files, referenceFiles, attachments, ...dataToUpdate } = updatedData;
        
        // 确保categoryId和priorityId是整数类型
        if (dataToUpdate.categoryId !== undefined) {
            dataToUpdate.categoryId = parseInt(dataToUpdate.categoryId);
        } else {
            // 保留原有的分类ID
            dataToUpdate.categoryId = artTasks[taskIndex].categoryId;
        }
        
        if (dataToUpdate.priorityId !== undefined) {
            dataToUpdate.priorityId = parseInt(dataToUpdate.priorityId);
        } else {
            // 保留原有的优先级ID
            dataToUpdate.priorityId = artTasks[taskIndex].priorityId;
        }
        
        const updatedTask = {
            ...artTasks[taskIndex],
            ...dataToUpdate
        };
        
        // 保存到API
        return saveTaskToAPI(updatedTask, false).then(() => {
            // 如果有成果附件，上传文件
            const uploadPromises = [];
            if (files && files.length > 0) {
                uploadPromises.push(uploadFilesToAPI(taskId, files));
            }
             // 新增：如果有关联参考附件，上传文件
            if (referenceFiles && referenceFiles.length > 0) {
                uploadPromises.push(uploadFilesToAPI(taskId, referenceFiles));
            }
            // 等待所有上传完成
            return Promise.all(uploadPromises).then(() => updatedTask);
        });
    }
    
    return Promise.reject('任务不存在');
}

// 替换原有的更新任务状态函数
function updateTaskStatus(taskId, newStatusId) {
    return updateTaskStatusToAPI(taskId, newStatusId).then(() => {
        const taskIndex = artTasks.findIndex(task => task.id === taskId);
        if (taskIndex !== -1) {
            artTasks[taskIndex].statusId = parseInt(newStatusId);
            return true;
        }
        return false;
    });
}

// 替换原有的删除任务函数
function deleteTask(taskId) {
    const task = artTasks.find(t => t.id === taskId);
    const userInfo = getCurrentUserInfo();
    
    // 添加权限验证
    if (userInfo.role !== 'admin' && task.createdBy !== userInfo.username) {
        showToast('权限不足', '您没有权限删除此任务', 'error');
        return Promise.reject('权限不足');
    }
    
    return deleteTaskFromAPI(taskId).then(() => {
        const taskIndex = artTasks.findIndex(task => task.id === taskId);
        if (taskIndex !== -1) {
            artTasks.splice(taskIndex, 1);
            return true;
        }
        return false;
    });
}

// 替换原有的添加评论函数
function addComment(taskId, user, text) {
    return addCommentToAPI(taskId, user, text).then(() => {
        const taskIndex = artTasks.findIndex(task => task.id === taskId);
        if (taskIndex !== -1) {
            const now = new Date();
            const timeString = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
            
            artTasks[taskIndex].comments.push({
                user,
                text,
                time: timeString
            });
            
            return true;
        }
        return false;
    });
}

// 获取任务类别名称
function getCategoryName(categoryId) {
    const category = taskCategories.find(cat => cat.id === categoryId);
    return category ? category.name : '未分类';
}

// 获取任务优先级名称
function getPriorityName(priorityId) {
    const priority = taskPriorities.find(p => p.id === priorityId);
    return priority ? priority.name : '未设置';
}

// 获取任务状态名称
function getStatusName(statusId) {
    const status = taskStatuses.find(s => s.id === statusId);
    return status ? status.name : '未知状态';
}

// 获取任务类别颜色
function getCategoryColor(categoryId) {
    const category = taskCategories.find(cat => cat.id === categoryId);
    return category ? category.color : '#ccc';
}

// 获取任务优先级颜色
function getPriorityColor(priorityId) {
    const priority = taskPriorities.find(p => p.id === priorityId);
    return priority ? priority.color : '#ccc';
}

// 获取任务状态颜色
function getStatusColor(statusId) {
    const status = taskStatuses.find(s => s.id === statusId);
    return status ? status.color : '#ccc';
}

// 过滤任务
function filterTasks(filters) {
    let filteredTasks = [...artTasks];
    
    if (filters.categoryId) {
        filteredTasks = filteredTasks.filter(task => task.categoryId === parseInt(filters.categoryId));
    }
    
    if (filters.priorityId) {
        filteredTasks = filteredTasks.filter(task => task.priorityId === parseInt(filters.priorityId));
    }
    
    if (filters.statusId) {
        filteredTasks = filteredTasks.filter(task => task.statusId === parseInt(filters.statusId));
    }
    
    if (filters.assignedTo) {
        filteredTasks = filteredTasks.filter(task => task.assignedTo === filters.assignedTo);
    }
    
    if (filters.createdBy) {
        filteredTasks = filteredTasks.filter(task => task.createdBy === filters.createdBy);
    }
    
    if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        filteredTasks = filteredTasks.filter(task => 
            task.title.toLowerCase().includes(searchTerm) || 
            task.description.toLowerCase().includes(searchTerm)
        );
    }
    
    return filteredTasks;
}

// 显示任务详情
function showTaskDetails(taskId) {
    const task = artTasks.find(t => t.id === taskId);
    if (!task) return;
    
    const modalBody = document.getElementById('taskModalBody');
    const userInfo = getCurrentUserInfo();
    const isArtDesigner = userInfo.role === 'art';
    
    // 根据当前用户和任务状态，显示不同的操作按钮
    let actionButtons = '';

    console.log(task.createdBy)
    console.log(userInfo.username)
    console.log(userInfo.role)
    
    if (task.assignedTo === userInfo.username || (isArtDesigner && task.assignedTo === '')) {
        // 如果是分配给当前用户的任务，或者当前用户是美工且任务未分配
        if (task.statusId === 1) { // 待接收
            actionButtons = `
                <button class="btn btn-success task-action-btn" data-action="accept" data-task-id="${task.id}">
                    <i class="fas fa-check-circle"></i> 接受任务
                </button>
                <button class="btn btn-danger task-action-btn" data-action="reject" data-task-id="${task.id}">
                    <i class="fas fa-times-circle"></i> 拒绝任务
                </button>
            `;
        } else if (task.statusId === 2) { // 进行中
            actionButtons = `
                <button class="btn btn-primary task-action-btn" data-action="complete" data-task-id="${task.id}" data-bs-toggle="modal" data-bs-target="#submitTaskModal">
                    <i class="fas fa-check-double"></i> 提交完成
                </button>
            `;
        }
    } else if (task.createdBy === userInfo.username || userInfo.role === 'admin') {
        // 如果是当前用户创建的任务或者是管理员
        if (task.statusId === 3) { // 待确认
            actionButtons = `
                <button class="btn btn-success task-action-btn" data-action="approve" data-task-id="${task.id}">
                    <i class="fas fa-thumbs-up"></i> 确认完成
                </button>
                <button class="btn btn-warning task-action-btn" data-action="revise" data-task-id="${task.id}">
                    <i class="fas fa-sync-alt"></i> 需要修改
                </button>
            `;
        }
    }
    
    // 所有用户都可以编辑自己创建的未完成任务，管理员可以编辑所有未完成任务
    if ((task.createdBy === userInfo.username || userInfo.role === 'admin') && task.statusId !== 4 && task.statusId !== 5) {
        actionButtons += `
            <button class="btn btn-info task-action-btn" data-action="edit" data-task-id="${task.id}">
                <i class="fas fa-edit"></i> 编辑任务
            </button>
            <button class="btn btn-danger task-action-btn" data-action="delete" data-task-id="${task.id}">
                <i class="fas fa-trash-alt"></i> 删除任务
            </button>
        `;
    }
    
    // 解析参考链接
    let referencesHtml = '';
    if (task.references && task.references.length > 0) {
        referencesHtml = `
            <div class="task-references">
                <h5>参考链接</h5>
                <ul>
                    ${task.references.map(ref => `<li><a href="${ref}" target="_blank">${ref}</a></li>`).join('')}
                </ul>
            </div>
        `;
    }
    
    // 解析附件，添加下载按钮
    let attachmentsHtml = '';
    if (task.attachments && task.attachments.length > 0) {
        attachmentsHtml = `
            <div class="task-attachments">
                <h5>附件</h5>
                <ul class="attachments-list">
                    ${task.attachments.map(attach => {
                        const isImage = /\.(jpe?g|png|gif|bmp|webp)$/i.test(attach);
                        const previewHtml = isImage 
                            ? `<img src="/api/tasks/download?taskId=${task.id}&filename=${encodeURIComponent(attach)}" 
                                   alt="${attach}" 
                                   class="attachment-preview" 
                                   data-lightbox-src="/api/tasks/download?taskId=${task.id}&filename=${encodeURIComponent(attach)}">` 
                            : '';
                        
                        return `
                            <li>
                                <div>
                                    <span class="attachment-name">${attach}</span>
                                    ${previewHtml}
                                </div>
                                <button class="btn btn-sm btn-outline-primary download-attachment" 
                                    data-task-id="${task.id}" 
                                    data-filename="${attach}">
                                    <i class="fas fa-download"></i> 下载
                                </button>
                            </li>
                        `;
                    }).join('')}
                </ul>
            </div>
        `;
    }
    
    // 渲染评论
    let commentsHtml = '';
    if (task.comments && task.comments.length > 0) {
        commentsHtml = `
            <div class="task-comments">
                <h5>评论记录</h5>
                <div class="comments-list">
                    ${task.comments.map(comment => `
                        <div class="comment-item">
                            <div class="comment-header">
                                <span class="comment-user">${comment.user}</span>
                                <span class="comment-time">${comment.time}</span>
                            </div>
                            <div class="comment-text">${comment.text}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }
    
    // 生成任务详情内容
    modalBody.innerHTML = `
        <div class="task-detail-container">
            <div class="task-header">
                <div class="task-badges">
                    <span class="badge" style="background-color: ${getCategoryColor(task.categoryId)}">
                        ${getCategoryName(task.categoryId)}
                    </span>
                    <span class="badge" style="background-color: ${getPriorityColor(task.priorityId)}">
                        ${getPriorityName(task.priorityId)}
                    </span>
                    <span class="badge" style="background-color: ${getStatusColor(task.statusId)}">
                        ${getStatusName(task.statusId)}
                    </span>
                </div>
                <h3 class="task-title">${task.title}</h3>
                <div class="task-meta">
                    <div>创建者: ${task.createdBy}</div>
                    <div>创建时间: ${task.createdAt}</div>
                    <div>截止日期: ${task.deadline} ${getRemainingTime(task.deadline, task.statusId)}</div>
                    <div>负责人: ${task.assignedTo || '未分配'}</div>
                </div>
            </div>
            
            <div class="task-description">
                <h5>任务描述</h5>
                <p>${task.description}</p>
            </div>
            
            ${referencesHtml}
            ${attachmentsHtml}
            ${commentsHtml}
            
            <div class="task-actions">
                ${actionButtons}
            </div>
            
            <div class="add-comment">
                <h5>添加评论</h5>
                <textarea id="commentText" class="form-control" rows="3" placeholder="输入评论内容..."></textarea>
                <button id="addCommentBtn" class="btn btn-primary mt-2" data-task-id="${task.id}">
                    <i class="fas fa-comment"></i> 发表评论
                </button>
            </div>
        </div>
    `;
    
    // 显示模态框
    const taskModal = new bootstrap.Modal(document.getElementById('taskModal'));
    taskModal.show();
    openModalById('taskModal');
    
    // 绑定下载附件按钮事件
    document.querySelectorAll('.download-attachment').forEach(button => {
        button.addEventListener('click', function() {
            const taskId = this.dataset.taskId;
            const filename = this.dataset.filename;
            downloadFile(taskId, filename);
        });
    });
    
    // 新增：绑定图片预览点击事件 (Lightbox)
    document.querySelectorAll('.attachment-preview').forEach(img => {
        img.addEventListener('click', function() {
            const largeSrc = this.dataset.lightboxSrc;
            showImageLightbox(largeSrc);
        });
    });
    
    // 如果点击了提交完成按钮，准备提交任务模态框
    if (task.statusId === 2 && (task.assignedTo === userInfo.username || (isArtDesigner && task.assignedTo === ''))) {
        document.getElementById('submitTaskId').value = task.id;
        document.getElementById('submitTaskTitle').textContent = task.title;
    }
}

// 新增：显示图片预览的 Lightbox 函数
function showImageLightbox(src) {
    // 移除已存在的 lightbox
    const existingLightbox = document.querySelector('.lightbox-overlay');
    if (existingLightbox) {
        existingLightbox.remove();
    }

    // 创建 lightbox 元素
    const lightbox = document.createElement('div');
    lightbox.className = 'lightbox-overlay';
    lightbox.innerHTML = `<img src="${src}" class="lightbox-image">`;

    // 点击 lightbox 关闭
    lightbox.addEventListener('click', () => {
        lightbox.remove();
    });

    document.body.appendChild(lightbox);
}

// 添加任务提交完成的处理函数
function submitTaskComplete(taskId, completionNotes, files) {
    // 显示加载状态
    const submitBtn = document.getElementById('confirmSubmitTaskBtn');
    const originalText = submitBtn.textContent;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';
    submitBtn.disabled = true;
    
    // 先添加完成说明评论
    addComment(taskId, getCurrentUserInfo().username, `任务完成说明: ${completionNotes}`)
        .then(() => {
            // 如果有文件，上传成果文件
            if (files && files.length > 0) {
                return uploadFilesToAPI(taskId, files);
            }
            return Promise.resolve();
        })
        .then(() => {
            // 更新任务状态为待确认
            return updateTaskStatus(taskId, 3);
        })
        .then(() => {
            // 添加系统评论，记录提交操作
            return addComment(taskId, getCurrentUserInfo().username, '已提交任务成果，等待确认');
        })
        .then(() => {
            // 关闭提交模态框
            bootstrap.Modal.getInstance(document.getElementById('submitTaskModal')).hide();
            // 关闭任务详情模态框
            //bootstrap.Modal.getInstance(document.getElementById('taskModal')).hide();
            // 显示成功提示
            showToast('任务提交成功', '您的任务已成功提交，等待确认。', 'success');
            // 重新加载任务列表
            loadTasksFromAPI(); 
        })
        .catch(error => {
            console.error('提交任务失败:', error);
            showToast('提交失败', '任务提交过程中出现错误，请重试。', 'error');
        })
        .finally(() => {
            // 恢复按钮状态
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
}

// 显示提示通知
function showToast(title, message, type = 'info') {
    // 检查是否已有toast容器，没有则创建
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    // 创建toast元素
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header ${type === 'success' ? 'bg-success text-white' : type === 'error' ? 'bg-danger text-white' : 'bg-info text-white'}">
                <strong class="me-auto">${title}</strong>
                <button type="button" class="btn-close ${type === 'success' || type === 'error' ? 'btn-close-white' : ''}" data-bs-dismiss="toast" aria-label="关闭"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;
    
    // 添加到容器
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    // 显示toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
    toast.show();
    
    // 自动删除已关闭的toast
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

// 绑定事件处理函数
function bindEventHandlers() {
    const userInfo = getCurrentUserInfo();
    
    // 视图切换按钮
    document.getElementById('kanbanViewBtn').addEventListener('click', function() {
        document.getElementById('kanbanViewBtn').classList.add('active');
        document.getElementById('listViewBtn').classList.remove('active');
        document.getElementById('kanbanView').classList.add('active');
        document.getElementById('listView').classList.remove('active');
    });
    
    document.getElementById('listViewBtn').addEventListener('click', function() {
        document.getElementById('listViewBtn').classList.add('active');
        document.getElementById('kanbanViewBtn').classList.remove('active');
        document.getElementById('listView').classList.add('active');
        document.getElementById('kanbanView').classList.remove('active');
    });
    
    // 添加任务按钮
    
    document.getElementById('addTaskBtn').addEventListener('click', function() {
        document.getElementById('taskForm').reset();
        document.getElementById('taskId').value = '';
        document.getElementById('editTaskModalTitle').textContent = '新建任务';
        document.getElementById('attachmentsList').innerHTML = '';
        
        // 设置默认截止日期为一周后
        const defaultDeadline = new Date();
        defaultDeadline.setDate(defaultDeadline.getDate() + 7);
        document.getElementById('taskDeadline').value = formatDate(defaultDeadline);
        
        // 设置默认任务类别为第一个类别，并加载对应的描述模板
        document.getElementById('taskCategory').value = taskCategories[0].id;
        // 新任务时填充默认模板
        const defaultCategoryId = document.getElementById('taskCategory').value;
        document.getElementById('taskDescription').value = taskDescriptionTemplates[defaultCategoryId] || '';
        
        const editTaskModal = new bootstrap.Modal(document.getElementById('editTaskModal'));
        editTaskModal.show();
        openModalById('editTaskModal');
    });
    
    // 任务类别选择改变时自动切换任务描述模板
    document.getElementById('taskCategory').addEventListener('change', function() {
        const categoryId = this.value;
        const taskDescriptionElement = document.getElementById('taskDescription');
        
        // 如果描述为空或使用的是模板，则直接应用新模板
        // 如果用户已经修改了描述，则提示确认是否覆盖
        const currentDescription = taskDescriptionElement.value.trim();
        const isUsingTemplate = Object.values(taskDescriptionTemplates).some(template => 
            currentDescription === template.trim() || currentDescription === ''
        );
        
        if (isUsingTemplate || currentDescription === '') {
            // 直接应用新模板
            taskDescriptionElement.value = taskDescriptionTemplates[categoryId] || '';
        } else {
            // 询问用户是否要覆盖
            if (confirm('是否使用该类别的任务描述模板？这将覆盖您当前已输入的描述内容。')) {
                taskDescriptionElement.value = taskDescriptionTemplates[categoryId] || '';
            }
        }
    });
    
    // 保存任务按钮
    document.getElementById('saveTaskBtn').addEventListener('click', function() {
        const form = document.getElementById('taskForm');
        
        // 简单的表单验证
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }
        
        const taskId = document.getElementById('taskId').value;
        const title = document.getElementById('taskTitle').value;
        const description = document.getElementById('taskDescription').value;
        const categoryId = document.getElementById('taskCategory').value;
        const priorityId = document.getElementById('taskPriority').value;
        const assignedTo = document.getElementById('taskAssignee').value;
        const deadline = document.getElementById('taskDeadline').value;
        
        // 处理参考链接
        const references = document.getElementById('taskReferences').value
            .split('\n')
            .map(line => line.trim())
            .filter(line => line !== '');
        
        // 处理附件
        const attachmentsInput = document.getElementById('taskAttachments');
        let attachments = [];
        let files = []; // 用于上传的成果附件
        
        if (attachmentsInput.files.length > 0) {
            files = attachmentsInput.files;
            for (let i = 0; i < attachmentsInput.files.length; i++) {
                attachments.push(attachmentsInput.files[i].name);
            }
        }
        
        // 新增：处理参考图片附件
        const referenceAttachmentsInput = document.getElementById('taskReferenceAttachments');
        let referenceFiles = []; // 用于上传的参考附件
        if (referenceAttachmentsInput.files.length > 0) {
            referenceFiles = referenceAttachmentsInput.files;
            // 注意：我们不在 taskData.attachments 中包含参考图片名称，因为它们会通过单独上传处理
            // 如果后端需要区分，可能需要在上传API调用时传递额外参数或使用不同端点
        }
        
        // 任务数据
        const taskData = {
            title,
            description,
            categoryId,
            priorityId,
            assignedTo,
            deadline,
            references,
            attachments, // 这里仍然是用户显式看到的附件列表（如果需要分开的话）
            files, // 成果附件文件对象
            referenceFiles, // 参考附件文件对象
            createdBy: userInfo.username
        };
        
        // 显示加载状态
        const saveBtn = document.getElementById('saveTaskBtn');
        const originalText = saveBtn.textContent;
        saveBtn.textContent = '保存中...';
        saveBtn.disabled = true;
        
        // 创建或更新任务
        const savePromise = taskId ? updateTask(taskId, taskData) : createNewTask(taskData);
        
        savePromise.then(() => {
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('editTaskModal')).hide();
            
            // 重新渲染视图（在上传完成后或并行执行？）
            // 为了用户体验，可以先刷新列表，上传在后台进行
            loadTasksFromAPI(); 
        })
        .catch(error => {
            console.error('保存任务失败:', error);
            alert('保存任务失败，请重试。');
        })
        .finally(() => {
            // 恢复按钮状态
            saveBtn.textContent = originalText;
            saveBtn.disabled = false;
        });
    });
    
    // 搜索按钮
    document.getElementById('searchBtn').addEventListener('click', function() {
        const searchTerm = document.getElementById('taskSearch').value;
        
        // 应用筛选
        const filters = {
            search: searchTerm,
            categoryId: document.getElementById('categoryFilter').value,
            priorityId: document.getElementById('priorityFilter').value,
            statusId: document.getElementById('statusFilter').value
        };
        
        const viewFilter = document.getElementById('viewFilter').value;
        if (viewFilter === 'my-tasks') {
            filters.assignedTo = userInfo.username;
        } else if (viewFilter === 'my-created') {
            filters.createdBy = userInfo.username;
        }
        
        // 过滤任务
        artTasks = filterTasks(filters);
        
        // 重新渲染视图
        renderKanbanView();
        renderListView();
    });
    
    // 绑定任务卡片查看详情按钮
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('view-task-btn')) {
            const taskId = e.target.dataset.taskId;
            showTaskDetails(taskId);
        }
    });
    
    // 绑定任务操作按钮
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('task-action-btn') || e.target.closest('.task-action-btn')) {
            const actionBtn = e.target.classList.contains('task-action-btn') ? e.target : e.target.closest('.task-action-btn');
            const action = actionBtn.dataset.action;
            const taskId = actionBtn.dataset.taskId;
            const task = artTasks.find(t => t.id === taskId);
            
            if (!task) return;
            
            // 禁用按钮，防止重复点击
            actionBtn.disabled = true;
            
            // 如果是提交完成按钮，不在这里处理，而是通过模态框提交
            if (action === 'complete') {
                actionBtn.disabled = false;
                return;
            }
            
            switch (action) {
                case 'accept':
                    // 显示处理中状态
                    const originalAcceptText = actionBtn.innerHTML;
                    actionBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
                    
                    // 任务接收处理
                    let updatePromise;
                    
                    // 如果任务未分配，但美工用户点击了接收，则自动分配给当前用户
                    if (task.assignedTo === '' && userInfo.role === 'art') {
                        // 先更新任务分配
                        updatePromise = updateTask(taskId, { assignedTo: userInfo.username })
                            .then(() => updateTaskStatus(taskId, 2)) // 然后更新状态为进行中
                            .then(() => addComment(taskId, userInfo.username, `已接受任务并将任务分配给自己`));
                    } else {
                        // 常规接收流程
                        updatePromise = updateTaskStatus(taskId, 2) // 进行中
                            .then(() => addComment(taskId, userInfo.username, '已接受任务'));
                    }
                    
                    updatePromise.then(() => {
                        bootstrap.Modal.getInstance(document.getElementById('taskModal')).hide();
                        showToast('任务已接受', '任务已成功分配给您，现在可以开始工作了。', 'success');
                        loadTasksFromAPI();
                    })
                    .catch(error => {
                        console.error('接受任务失败:', error);
                        showToast('操作失败', '接受任务时出现错误，请重试。', 'error');
                        actionBtn.innerHTML = originalAcceptText;
                        actionBtn.disabled = false;
                    });
                    break;
                case 'reject':
                    // 显示处理中状态
                    const originalRejectText = actionBtn.innerHTML;
                    actionBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
                    
                    updateTaskStatus(taskId, 5) // 已取消
                        .then(() => addComment(taskId, userInfo.username, '已拒绝任务'))
                        .then(() => {
                            bootstrap.Modal.getInstance(document.getElementById('taskModal')).hide();
                            showToast('任务已拒绝', '您已拒绝了这个任务。', 'info');
                            loadTasksFromAPI();
                        })
                        .catch(error => {
                            console.error('拒绝任务失败:', error);
                            showToast('操作失败', '拒绝任务时出现错误，请重试。', 'error');
                            actionBtn.innerHTML = originalRejectText;
                            actionBtn.disabled = false;
                        });
                    break;
                case 'approve':
                    // 显示处理中状态
                    const originalApproveText = actionBtn.innerHTML;
                    actionBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
                    
                    updateTaskStatus(taskId, 4) // 已完成
                        .then(() => addComment(taskId, userInfo.username, '任务已验收完成'))
                        .then(() => {
                            bootstrap.Modal.getInstance(document.getElementById('taskModal')).hide();
                            showToast('任务已确认完成', '任务已成功完成并确认。', 'success');
                            loadTasksFromAPI();
                        })
                        .catch(error => {
                            console.error('确认任务失败:', error);
                            showToast('操作失败', '确认任务时出现错误，请重试。', 'error');
                            actionBtn.innerHTML = originalApproveText;
                            actionBtn.disabled = false;
                        });
                    break;
                case 'revise':
                    // 显示处理中状态
                    const originalReviseText = actionBtn.innerHTML;
                    actionBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
                    
                    updateTaskStatus(taskId, 2) // 回到进行中
                        .then(() => addComment(taskId, userInfo.username, '需要修改: 请查看评论了解详情'))
                        .then(() => {
                            bootstrap.Modal.getInstance(document.getElementById('taskModal')).hide();
                            showToast('已要求修改', '任务已退回给美工进行修改。', 'info');
                            loadTasksFromAPI();
                        })
                        .catch(error => {
                            console.error('退回修改失败:', error);
                            showToast('操作失败', '退回修改时出现错误，请重试。', 'error');
                            actionBtn.innerHTML = originalReviseText;
                            actionBtn.disabled = false;
                        });
                    break;
                case 'edit':
                    // 填充表单数据
                    document.getElementById('taskId').value = task.id;
                    document.getElementById('taskTitle').value = task.title;
                    document.getElementById('taskDescription').value = task.description;
                    document.getElementById('taskCategory').value = task.categoryId;
                    document.getElementById('taskPriority').value = task.priorityId;
                    document.getElementById('taskAssignee').value = task.assignedTo || '';
                    document.getElementById('taskDeadline').value = task.deadline;
                    
                    // 填充参考链接
                    if (task.references && task.references.length > 0) {
                        document.getElementById('taskReferences').value = task.references.join('\n');
                    } else {
                        document.getElementById('taskReferences').value = '';
                    }
                    
                    // 清空附件列表和预览区
                    document.getElementById('attachmentsList').innerHTML = '';
                    document.getElementById('referenceAttachmentsPreview').innerHTML = '';
                    
                    // 显示已有附件（如果有）
                    if (task.attachments && task.attachments.length > 0) {
                        const attachmentsList = document.getElementById('attachmentsList');
                        attachmentsList.innerHTML = '<p>已上传的附件:</p>';
                        task.attachments.forEach(attach => {
                            const isImage = /\.(jpe?g|png|gif|bmp|webp)$/i.test(attach);
                            const attachItem = document.createElement('div');
                            attachItem.className = 'attachment-item';
                            attachItem.innerHTML = `
                                <span>${attach}</span>
                                ${isImage ? 
                                    `<img src="/api/tasks/download?taskId=${task.id}&filename=${encodeURIComponent(attach)}" 
                                         alt="${attach}" class="small-preview">` : 
                                    `<i class="fas fa-file"></i>`
                                }
                            `;
                            attachmentsList.appendChild(attachItem);
                        });
                    }
                    
                    document.getElementById('editTaskModalTitle').textContent = '编辑任务';
                    
                    // 显示模态框
                    const editTaskModal = new bootstrap.Modal(document.getElementById('editTaskModal'));
                    editTaskModal.show();
                    
                    break;
                case 'delete':
                    if (confirm('确定要删除这个任务吗？此操作不可恢复。')) {
                        deleteTask(taskId).then(() => {
                            bootstrap.Modal.getInstance(document.getElementById('taskModal')).hide();
                            loadTasksFromAPI();
                        });
                    } else {
                        e.target.disabled = false;
                    }
                    return; // 避免重复处理
            }
        }
    });
    
    // 添加评论按钮
    document.addEventListener('click', function(e) {
        if (e.target.id === 'addCommentBtn' || e.target.closest('#addCommentBtn')) {
            const commentBtn = e.target.id === 'addCommentBtn' ? e.target : e.target.closest('#addCommentBtn');
            const taskId = commentBtn.dataset.taskId;
            const commentText = document.getElementById('commentText').value.trim();
            
            if (commentText) {
                // 禁用按钮，显示加载状态
                commentBtn.disabled = true;
                const originalCommentBtnText = commentBtn.innerHTML;
                commentBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';
                
                addComment(taskId, userInfo.username, commentText)
                    .then(() => {
                        document.getElementById('commentText').value = '';
                        
                        // 重新加载任务详情
                        showTaskDetails(taskId);
                        showToast('评论已添加', '您的评论已成功添加。', 'success');
                    })
                    .catch(error => {
                        console.error('添加评论失败:', error);
                        showToast('评论失败', '添加评论时出现错误，请重试。', 'error');
                    })
                    .finally(() => {
                        // 恢复按钮状态
                        commentBtn.disabled = false;
                        commentBtn.innerHTML = originalCommentBtnText;
                    });
            }
        }
    });
    
    // 确认提交任务按钮
    document.getElementById('confirmSubmitTaskBtn').addEventListener('click', function() {
        const taskId = document.getElementById('submitTaskId').value;
        const completionNotes = document.getElementById('completionNotes').value.trim();
        const files = document.getElementById('completionFiles').files;
        
        if (taskId) {
            submitTaskComplete(taskId, completionNotes, files);
        }
    });
    
    // 筛选器变化事件
    document.getElementById('viewFilter').addEventListener('change', function() {
        const viewFilter = this.value;
        const filters = {
            categoryId: document.getElementById('categoryFilter').value,
            priorityId: document.getElementById('priorityFilter').value,
            statusId: document.getElementById('statusFilter').value
        };
        
        if (viewFilter === 'my-tasks') {
            filters.assignedTo = userInfo.username;
        } else if (viewFilter === 'my-created') {
            filters.createdBy = userInfo.username;
        }
        
        // 过滤任务
        artTasks = filterTasks(filters);
        
        // 重新渲染视图
        renderKanbanView();
        renderListView();
    });
    
    // 状态筛选器变化事件
    document.getElementById('statusFilter').addEventListener('change', function() {
        const statusId = this.value;
        const filters = {
            categoryId: document.getElementById('categoryFilter').value,
            priorityId: document.getElementById('priorityFilter').value
        };
        
        if (statusId) {
            filters.statusId = statusId;
        }
        
        const viewFilter = document.getElementById('viewFilter').value;
        if (viewFilter === 'my-tasks') {
            filters.assignedTo = userInfo.username;
        } else if (viewFilter === 'my-created') {
            filters.createdBy = userInfo.username;
        }
        
        // 过滤任务
        artTasks = filterTasks(filters);
        
        // 重新渲染视图
        renderKanbanView();
        renderListView();
    });
    
    // 类别筛选器变化事件
    document.getElementById('categoryFilter').addEventListener('change', function() {
        const categoryId = this.value;
        const filters = {
            priorityId: document.getElementById('priorityFilter').value,
            statusId: document.getElementById('statusFilter').value
        };
        
        if (categoryId) {
            filters.categoryId = categoryId;
        }
        
        const viewFilter = document.getElementById('viewFilter').value;
        if (viewFilter === 'my-tasks') {
            filters.assignedTo = userInfo.username;
        } else if (viewFilter === 'my-created') {
            filters.createdBy = userInfo.username;
        }
        
        // 过滤任务
        artTasks = filterTasks(filters);
        
        // 重新渲染视图
        renderKanbanView();
        renderListView();
    });
    
    // 优先级筛选器变化事件
    document.getElementById('priorityFilter').addEventListener('change', function() {
        const priorityId = this.value;
        const filters = {
            categoryId: document.getElementById('categoryFilter').value,
            statusId: document.getElementById('statusFilter').value
        };
        
        if (priorityId) {
            filters.priorityId = priorityId;
        }
        
        const viewFilter = document.getElementById('viewFilter').value;
        if (viewFilter === 'my-tasks') {
            filters.assignedTo = userInfo.username;
        } else if (viewFilter === 'my-created') {
            filters.createdBy = userInfo.username;
        }
        
        // 过滤任务
        artTasks = filterTasks(filters);
        
        // 重新渲染视图
        renderKanbanView();
        renderListView();
    });
}

// 添加美工任务管理样式
function addArtTaskManagementStyles() {
    // 检查是否已经添加了样式
    if (document.getElementById('art-task-management-styles')) return;

    // 创建样式元素
    const style = document.createElement('style');
    style.id = 'art-task-management-styles';
    style.textContent = `
        /* 美工任务管理系统样式 */
        .art-task-management {
            padding: 20px;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        .art-task-management .header {
            margin-bottom: 20px;
        }
        
        .art-task-management .header h1 {
            margin-bottom: 5px;
            color: #333;
            font-size: 24px;
        }
        
        .art-task-management .header p {
            color: #666;
            margin: 0;
        }
        
        /* 任务概览样式 */
        .task-overview {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .overview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .overview-header h3 {
            margin: 0;
            font-size: 18px;
            color: #333;
        }
        
        .overview-actions {
            display: flex;
            gap: 10px;
        }
        
        .overview-last-update {
            color: #888;
            margin-bottom: 15px;
        }
        
        .overview-summary {
            margin-top: 15px;
        }
        
        .overview-card {
            background-color: #f9f9f9;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        
        .overview-card h4 {
            font-size: 16px;
            margin-top: 0;
            margin-bottom: 15px;
            color: #333;
        }
        
        .overview-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: this5px;
        }
        
        .stat-item {
            text-align: center;
            flex: 1;
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: 600;
            color: #4b6cb7;
            display: block;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .chart-container {
            height: 200px;
            margin-top: 15px;
        }
        
        .overview-designers h4, .overview-designers h5 {
            font-size: 16px;
            margin-top: 0;
            margin-bottom: 15px;
            color: #333;
        }
        
        .overview-designers h5 {
            font-size: 14px;
        }
        
        .designer-stats {
            background-color: #f9f9f9;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .designer-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .designer-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            position: relative;
            padding: 5px 0;
        }
        
        .designer-rank {
            width: 22px;
            height: 22px;
            background-color: #4b6cb7;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin-right: 10px;
        }
        
        .designer-name {
            flex: 1;
            font-size: 14px;
        }
        
        .designer-count {
            font-size: 12px;
            color: #666;
            margin-right: 10px;
        }
        
        .designer-bar {
            position: absolute;
            left: 0;
            bottom: 0;
            height: 3px;
            background-color: #4b6cb7;
            border-radius: 3px;
            z-index: 1;
        }
        
        .no-data {
            text-align: center;
            color: #999;
            padding: 20px;
            font-style: italic;
        }
        
        /* 任务工具栏样式 */
        .task-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .view-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .view-controls .form-select {
            width: auto;
            min-width: 120px;
        }
        
        .search-box {
            display: flex;
            gap: 5px;
            width: 300px;
        }
        
        .search-box .form-control {
            width: 100%;
        }
        
        /* 视图切换器 */
        .view-switcher {
            display: flex;
            margin-bottom: 20px;
        }
        
        .view-btn {
            padding: 8px 15px;
            border: 1px solid #ddd;
            background: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .view-btn:first-child {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
        }
        
        .view-btn:last-child {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
        }
        
        .view-btn.active {
            background: linear-gradient(90deg, #4b6cb7, #182848);
            color: white;
            border-color: transparent;
        }
        
        /* 任务视图容器 */
        #taskViews {
            position: relative;
        }
        
        .task-view {
            display: none;
        }
        
        .task-view.active {
            display: block;
        }
        
        /* 看板视图样式 */
        .kanban-container {
            display: flex;
            overflow-x: auto;
            padding-bottom: 20px;
            gap: 15px;
        }
        
        .kanban-column {
            flex: 0 0 300px;
            background: #f9f9f9;
            border-radius: 6px;
            padding: 15px;
            max-height: calc(100vh - 300px);
            overflow-y: auto;
        }
        
        .column-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .column-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
        }
        
        .task-count {
            background: #e0e0e0;
            color: #333;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
        }
        
        .task-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        /* 任务卡片样式 */
        .task-card {
            background: white;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            padding: 15px;
            transition: all 0.3s ease;
        }
        
        .task-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .task-card.priority-urgent {
            border-left: 4px solid #f44336;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: normal;
            color: white;
        }
        
        .card-title {
            font-weight: 500;
            margin-bottom: 8px;
            color: #333;
        }
        
        .card-description {
            font-size: 13px;
            color: #666;
            margin-bottom: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .card-meta {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #888;
            margin-bottom: 10px;
        }
        
        .overdue {
            color: #f44336;
            font-weight: 500;
        }
        
        .due-today {
            color: #ff9800;
            font-weight: 500;
        }
        
        .remaining {
            color: #4caf50;
        }
        
        .card-footer {
            display: flex;
            justify-content: flex-end;
        }
        
        /* 列表视图样式 */
        .task-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .task-table th, 
        .task-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .task-table th {
            background: linear-gradient(90deg, #4b6cb7, #182848);
            color: white;
            font-weight: 500;
        }
        
        .task-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .task-table tr.priority-urgent {
            border-left: 4px solid #f44336;
        }
        
        .task-table .badge {
            padding: 3px 6px;
            border-radius: 3px;
            font-size: 11px;
        }
        
        /* 表格响应式 */
        .table-responsive {
            overflow-x: auto;
        }
        
        /* 任务详情模态框样式 */
        #taskModal .modal-dialog {
            max-width: 800px; /* 适当增大宽度 */
        }

        /* 参考图片预览样式 */
        .reference-preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            margin-right: 10px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }

        .reference-preview-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .reference-preview-name {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0,0,0,0.6);
            color: white;
            font-size: 0.7rem;
            padding: 3px 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .task-detail-container {
            padding: 15px;
        }

        .task-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .task-badges {
            margin-bottom: 10px;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .task-title {
            font-size: 1.5rem; /* 增大标题字号 */
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }

        .task-meta {
            font-size: 0.9rem;
            color: #666;
            display: grid; /* 使用 grid 布局 */
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); /* 响应式列 */
            gap: 8px;
            margin-top: 10px;
        }

        .task-meta > div {
            background-color: #f8f9fa;
            padding: 5px 8px;
            border-radius: 4px;
        }


        .task-description,
        .task-references,
        .task-attachments,
        .task-comments,
        .add-comment {
            margin-bottom: 25px; /* 增加区域间距 */
            background-color: #fdfdfd; /* 轻微背景色区分 */
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #f0f0f0;
        }

        .task-description h5,
        .task-references h5,
        .task-attachments h5,
        .task-comments h5,
        .add-comment h5 {
            font-size: 1.1rem;
            font-weight: 500;
            margin-bottom: 15px; /* 增加标题下间距 */
            color: #444;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
        }

        .task-description p {
            line-height: 1.6;
            color: #555;
        }

        .task-references ul,
        .attachments-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .task-references li,
        .attachments-list li {
            margin-bottom: 8px;
            padding: 8px 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            display: flex; /* 使用 flex 布局 */
            justify-content: space-between; /* 两端对齐 */
            align-items: center; /* 垂直居中 */
        }

        .task-references a {
            color: #0d6efd;
            text-decoration: none;
            word-break: break-all; /* 防止长链接溢出 */
        }
        .task-references a:hover {
            text-decoration: underline;
        }

        .attachment-name {
            flex-grow: 1; /* 文件名占据剩余空间 */
            margin-right: 10px; /* 与按钮保持距离 */
            word-break: break-all;
        }

        .attachment-preview {
            max-width: 100px; /* 预览图最大宽度 */
            max-height: 60px; /* 预览图最大高度 */
            margin-top: 5px;
            border-radius: 4px;
            display: block;
            cursor: pointer; /* 添加点击放大提示 */
        }

        .download-attachment {
            flex-shrink: 0; /* 防止按钮被压缩 */
        }


        .comments-list {
            max-height: 300px; /* 限制评论区高度 */
            overflow-y: auto; /* 超出滚动 */
            padding-right: 10px; /* 滚动条空间 */
        }

        .comment-item {
            background-color: #f8f9fa;
            padding: 10px 12px;
            border-radius: 5px;
            margin-bottom: 10px;
            border-left: 3px solid #17a2b8; /* 左侧边框突出 */
        }

        .comment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
            font-size: 0.85rem;
        }

        .comment-user {
            font-weight: 600;
            color: #343a40;
        }

        .comment-time {
            color: #6c757d;
        }

        .comment-text {
            font-size: 0.95rem;
            color: #495057;
            line-height: 1.5;
            word-wrap: break-word; /* 自动换行 */
        }

        /* Lightbox for image preview */
        .lightbox-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1070; /* Higher than modal */
            cursor: pointer;
        }

        .lightbox-image {
            max-width: 90%;
            max-height: 90%;
            display: block;
            border: 3px solid white;
            border-radius: 5px;
        }

        .task-actions {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            display: flex;
            flex-wrap: wrap; /* 按钮多时换行 */
            gap: 10px; /* 按钮间距 */
        }
        .task-action-btn i {
            margin-right: 5px;
        }

        .add-comment textarea {
            resize: vertical; /* 允许垂直调整大小 */
        }

        .add-comment button i {
             margin-right: 5px;
        }

        /* 状态进度条样式 */
        .status-progress {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            position: relative;
        }
        
        .status-progress::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 0;
            right: 0;
            height: 2px;
            background: #ddd;
            z-index: 1;
        }
        
        .status-step {
            position: relative;
            z-index: 2;
            text-align: center;
        }
        
        .step-circle {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #ddd;
            margin: 0 auto 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            transition: all 0.3s ease;
        }
        
        .status-step.active .step-circle {
            background: #4caf50;
        }
        
        .status-step.completed .step-circle {
            background: #4b6cb7;
        }
        
        .step-label {
            font-size: 12px;
            color: #666;
        }
        
        /* 响应式样式 */
        @media (max-width: 768px) {
            .task-toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .view-controls {
                flex-direction: column;
            }
            
            .search-box {
                width: 100%;
            }
            
            .kanban-column {
                flex: 0 0 250px;
            }
        }
        
        /* 无任务样式 */
        .no-tasks {
            padding: 30px;
            text-align: center;
            color: #999;
            background: #f9f9f9;
            border-radius: 8px;
        }
        
        /* Toast 样式 */
        .toast-container {
            z-index: 1060;
        }
        
        .toast {         
            background: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .toast-header {
            border-bottom: none;
        }
    `;
    
    // 添加到文档头部
    document.head.appendChild(style);
}

// 生成美工任务管理UI
function generateArtTaskManagement() {
    // 从API加载任务数据
    loadTasksFromAPI();
    
    // 从API加载任务概览数据
    loadTaskOverviewFromAPI(); // 新增加载概览数据
    
    // 加载美工用户列表
    loadArtDesigners();
    
    const userInfo = getCurrentUserInfo();
    const isAdmin = userInfo.role === 'admin';
    const container = document.querySelector('.container');
    
    // 初始化UI
    container.innerHTML = `
        <div class="art-task-management">
            <div class="header">
                <h1>美工任务管理</h1>
                <p>美工与运营的工作流程管理系统</p>
            </div>
            
            <!-- 新增: 任务概览部分 -->
            <div class="task-overview" id="taskOverviewSection">
                <div class="text-center p-4">
                    <div class="spinner-border text-primary" role="status"></div>
                    <p class="mt-2">正在加载任务概览...</p>
                </div>
            </div>
            
            <div class="task-toolbar">
                <div class="view-controls">
                    <select id="viewFilter" class="form-select">
                        <option value="all">所有任务</option>
                        <option value="my-tasks">我负责的任务</option>
                        <option value="my-created">我创建的任务</option>
                    </select>
                    
                    <select id="statusFilter" class="form-select">
                        <option value="">所有状态</option>
                        ${taskStatuses.map(status => `<option value="${status.id}">${status.name}</option>`).join('')}
                    </select>
                    
                    <select id="categoryFilter" class="form-select">
                        <option value="">所有类别</option>
                        ${taskCategories.map(category => `<option value="${category.id}">${category.name}</option>`).join('')}
                    </select>
                    
                    <select id="priorityFilter" class="form-select">
                        <option value="">所有优先级</option>
                        ${taskPriorities.map(priority => `<option value="${priority.id}">${priority.name}</option>`).join('')}
                    </select>
                </div>
                
                <div class="search-box">
                    <input type="text" id="taskSearch" class="form-control" placeholder="搜索任务...">
                    <button id="searchBtn" class="btn btn-primary">搜索</button>
                </div>
                
                <button id="addTaskBtn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 新建任务
                </button>
            </div>
            
            <div class="view-switcher">
                <button id="kanbanViewBtn" class="view-btn active">看板视图</button>
                <button id="listViewBtn" class="view-btn">列表视图</button>
            </div>
            
            <div id="taskViews">
                <div id="kanbanView" class="task-view active"></div>
                <div id="listView" class="task-view"></div>
            </div>
            
            <!-- 任务详情模态框 -->
            <div class="modal fade" id="taskModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="taskModalTitle">任务详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body" id="taskModalBody">
                            <!-- 任务详情将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 新建/编辑任务模态框 -->
            <div class="modal fade" id="editTaskModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="editTaskModalTitle">新建任务</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="taskForm">
                                <input type="hidden" id="taskId">
                                <div class="mb-3">
                                    <label for="taskTitle" class="form-label">任务标题</label>
                                    <input type="text" class="form-control" id="taskTitle" required>
                                </div>
                                <div class="mb-3">
                                    <label for="taskDescription" class="form-label">任务描述</label>
                                    <textarea class="form-control" id="taskDescription" rows="3" required></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="taskCategory" class="form-label">任务类别</label>
                                        <select class="form-select" id="taskCategory" required>
                                            ${taskCategories.map(category => `<option value="${category.id}">${category.name}</option>`).join('')}
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="taskPriority" class="form-label">优先级</label>
                                        <select class="form-select" id="taskPriority" required>
                                            ${taskPriorities.map(priority => `<option value="${priority.id}">${priority.name}</option>`).join('')}
                                        </select>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="taskAssignee" class="form-label">分配给<small class="text-muted">（只能选择美工）</small></label>
                                        <select class="form-select" id="taskAssignee">
                                            <option value="">选择负责人</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="taskDeadline" class="form-label">截止日期</label>
                                        <input type="date" class="form-control" id="taskDeadline" required>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="taskReferences" class="form-label">参考链接</label>
                                    <textarea class="form-control" id="taskReferences" rows="2" placeholder="每行一个链接"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="taskAttachments" class="form-label">附件上传</label>
                                    <input type="file" class="form-control" id="taskAttachments" multiple>
                                    <div id="attachmentsList" class="mt-2"></div>
                                </div>
                                <div class="mb-3">
                                    <label for="taskReferenceAttachments" class="form-label">参考图片上传 <small class="text-muted">(可选)</small></label>
                                    <input type="file" class="form-control" id="taskReferenceAttachments" multiple accept="image/*">
                                    <div id="referenceAttachmentsPreview" class="mt-2 d-flex flex-wrap gap-2"></div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="saveTaskBtn">保存</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 任务提交完成模态框 -->
            <div class="modal fade" id="submitTaskModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">提交任务完成</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                        </div>
                        <div class="modal-body">
                            <input type="hidden" id="submitTaskId">
                            <div class="mb-3">
                                <label class="form-label">任务</label>
                                <div class="fw-bold" id="submitTaskTitle"></div>
                            </div>
                            <div class="mb-3">
                                <label for="completionNotes" class="form-label">完成说明</label>
                                <textarea class="form-control" id="completionNotes" rows="3" placeholder="请简要描述您的完成情况、注意事项或其他需要说明的内容..."></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="completionFiles" class="form-label">上传成果文件</label>
                                <input type="file" class="form-control" id="completionFiles" multiple>
                                <div class="form-text">可以上传多个文件，如设计源文件、导出文件等</div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="confirmSubmitTaskBtn">确认提交</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Toast容器 -->
            <div class="toast-container position-fixed bottom-0 end-0 p-3"></div>
        </div>
    `;
    
    // 加载Chart.js库（如果还没有加载）
    if (!window.Chart) {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
        script.onload = function() {
            // Chart.js加载完成后渲染图表
            if (taskOverview) {
                renderCategoryCharts();
                renderMonthlyCharts();
            }
        };
        document.body.appendChild(script);
    }
    
    // 添加样式
    addArtTaskManagementStyles();
    
    // 绑定事件处理程序
    bindEventHandlers();
}

// 渲染看板视图
function renderKanbanView() {
    const kanbanView = document.getElementById('kanbanView');
    kanbanView.innerHTML = '';
    
    // 创建看板列
    const kanbanContainer = document.createElement('div');
    kanbanContainer.className = 'kanban-container';
    
    // 为每个状态创建一列
    taskStatuses.forEach(status => {
        // 根据状态筛选任务
        const statusTasks = artTasks.filter(task => task.statusId === status.id);
        
        // 创建看板列
        const column = document.createElement('div');
        column.className = 'kanban-column';
        column.dataset.statusId = status.id;
        column.style.borderTop = `4px solid ${status.color}`;
        
        // 创建列标题
        const columnHeader = document.createElement('div');
        columnHeader.className = 'column-header';
        columnHeader.innerHTML = `
            <h3>${status.name}</h3>
            <span class="task-count">${statusTasks.length}</span>
        `;
        
        // 创建任务卡片容器
        const taskList = document.createElement('div');
        taskList.className = 'task-list';
        
        // 添加任务卡片
        statusTasks.forEach(task => {
            const taskCard = createTaskCard(task);
            taskList.appendChild(taskCard);
        });
        
        // 组装列
        column.appendChild(columnHeader);
        column.appendChild(taskList);
        kanbanContainer.appendChild(column);
    });
    
    kanbanView.appendChild(kanbanContainer);
}

// 渲染列表视图
function renderListView() {
    const listView = document.getElementById('listView');
    listView.innerHTML = '';
    
    // 如果没有任务，显示提示信息
    if (artTasks.length === 0) {
        listView.innerHTML = '<div class="no-tasks">暂无任务</div>';
        return;
    }
    
    // 创建任务表格容器
    const tableContainer = document.createElement('div');
    tableContainer.className = 'table-responsive';
    
    // 创建任务表格
    const table = document.createElement('table');
    table.className = 'task-table';
    
    // 创建表头
    const thead = document.createElement('thead');
    thead.innerHTML = `
        <tr>
            <th width="25%">任务标题</th>
            <th width="12%">类别</th>
            <th width="10%">优先级</th>
            <th width="10%">状态</th>
            <th width="12%">负责人</th>
            <th width="18%">截止日期</th>
            <th width="13%">操作</th>
        </tr>
    `;
    
    // 创建表格内容
    const tbody = document.createElement('tbody');
    
    // 添加任务行
    artTasks.forEach(task => {
        const tr = document.createElement('tr');
        tr.dataset.taskId = task.id;
        
        // 根据优先级添加相应的类
        if (task.priorityId === 1) {
            tr.classList.add('priority-urgent');
        }
        
        tr.innerHTML = `
            <td>
                <div style="font-weight: 500; color: #2c3e50;">${task.title}</div>
                <small style="color: #7f8c8d; display: block; margin-top: 3px;">${task.description.substring(0, 50)}${task.description.length > 50 ? '...' : ''}</small>
            </td>
            <td>
                <span class="badge" style="background-color: ${getCategoryColor(task.categoryId)}">
                    ${getCategoryName(task.categoryId)}
                </span>
            </td>
            <td>
                <span class="badge" style="background-color: ${getPriorityColor(task.priorityId)}">
                    ${getPriorityName(task.priorityId)}
                </span>
            </td>
            <td>
                <span class="badge" style="background-color: ${getStatusColor(task.statusId)}">
                    ${getStatusName(task.statusId)}
                </span>
            </td>
            <td>${task.assignedTo || '<span style="color: #95a5a6; font-style: italic;">未分配</span>'}</td>
            <td>
                <div>${task.deadline}</div>
                <div>${getRemainingTime(task.deadline, task.statusId)}</div>
            </td>
            <td>
                <button class="btn btn-primary view-task-btn" data-task-id="${task.id}">
                    查看详情
                </button>
            </td>
        `;
        
        tbody.appendChild(tr);
    });
    
    // 组装表格
    table.appendChild(thead);
    table.appendChild(tbody);
    tableContainer.appendChild(table);
    
    // 添加到视图
    listView.appendChild(tableContainer);
}

// 创建任务卡片
function createTaskCard(task) {
    const card = document.createElement('div');
    card.className = 'task-card';
    card.dataset.taskId = task.id;
    
    // 根据优先级添加相应的类
    if (task.priorityId === 1) {
        card.classList.add('priority-urgent');
    }
    
    card.innerHTML = `
        <div class="card-header">
            <span class="badge" style="background-color: ${getCategoryColor(task.categoryId)}">
                ${getCategoryName(task.categoryId)}
            </span>
            <span class="badge" style="background-color: ${getPriorityColor(task.priorityId)}">
                ${getPriorityName(task.priorityId)}
            </span>
        </div>
        <div class="card-title">${task.title}</div>
        <div class="card-description">${task.description}</div>
        <div class="card-meta">
            <div class="assignee">
                ${task.assignedTo ? `负责人: ${task.assignedTo}` : '未分配'}
            </div>
            <div class="deadline">
                ${task.deadline} ${getRemainingTime(task.deadline, task.statusId)}
            </div>
        </div>
        <div class="card-footer">
            <button class="btn btn-primary view-task-btn" data-task-id="${task.id}">
                查看详情
            </button>
        </div>
    `;
    
    return card;
} 

function openModalById(id) {
    const modalEl = document.getElementById(id);
  
    // ① 先移除可能遗留的 backdrop 与 body 状态
    document.querySelectorAll('.modal-backdrop').forEach(el => el.remove());
    document.body.classList.remove('modal-open');
    document.body.style.paddingRight = '';
  
    // ② 只有第一次才会创建实例，之后都会复用
    const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
    modal.show();
  
    // ③ 隐藏后再做一次彻底清理，并销毁实例
    modalEl.addEventListener(
      'hidden.bs.modal',
      () => {
        modal.dispose();                                 // 释放实例
        document.querySelectorAll('.modal-backdrop').forEach(el => el.remove());
        document.body.classList.remove('modal-open');
        document.body.style.paddingRight = '';
      },
      { once: true }                                     // 只监听一次
    );
}
  
// 从API加载任务概览数据
function loadTaskOverviewFromAPI() {
    fetch('/api/art-tasks/overview')
        .then(response => response.json())
        .then(data => {
            taskOverview = data;
            renderTaskOverview();
        })
        .catch(error => {
            console.error('加载任务概览失败:', error);
            showToast('错误', '加载任务概览失败，请刷新页面重试', 'danger');
        });
}

// 手动重置任务概览数据
function resetTaskOverview() {
    // 显示加载指示器
    const overviewSection = document.getElementById('taskOverviewSection');
    if (overviewSection) {
        overviewSection.innerHTML = '<div class="text-center p-4"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">正在重置数据...</p></div>';
    }
    
    fetch('/api/art-tasks/overview/reset', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            taskOverview = result.data;
            renderTaskOverview();
            showToast('成功', '任务概览数据已重置', 'success');
        } else {
            throw new Error(result.message || '重置失败');
        }
    })
    .catch(error => {
        console.error('重置任务概览失败:', error);
        showToast('错误', '重置任务概览失败，请稍后重试', 'danger');
        // 重新加载数据
        loadTaskOverviewFromAPI();
    });
}

// 渲染任务概览UI
function renderTaskOverview() {
    const overviewSection = document.getElementById('taskOverviewSection');
    if (!overviewSection || !taskOverview) return;
    
    // 获取当前用户信息，用于权限判断
    const userInfo = getCurrentUserInfo();
    
    // 获取当前月份和年份
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM
    const currentYear = new Date().getFullYear().toString();
    
    // 获取当月任务数据
    const monthlyStats = taskOverview.monthly_stats[currentMonth] || {
        total: 0,
        completed: 0,
        by_category: {},
        by_designer: {},
        by_status: {}
    };
    
    // 获取当年任务数据
    const yearlyStats = taskOverview.yearly_stats[currentYear] || {
        total: 0,
        completed: 0,
        by_month: {},
        by_category: {},
        by_designer: {}
    };
    
    // 计算当月完成率
    const monthlyCompletionRate = monthlyStats.total > 0 
        ? ((monthlyStats.completed / monthlyStats.total) * 100).toFixed(1) 
        : 0;
    
    // 计算当年完成率
    const yearlyCompletionRate = yearlyStats.total > 0 
        ? ((yearlyStats.completed / yearlyStats.total) * 100).toFixed(1) 
        : 0;
    
    // 格式化最后更新时间
    const lastResetDate = taskOverview.last_reset 
        ? new Date(taskOverview.last_reset).toLocaleDateString('zh-CN')
        : '未知';
    
    // 渲染概览内容
    overviewSection.innerHTML = `
        <div class="overview-header">
            <h3>任务概览</h3>
            <div class="overview-actions">
                <button id="refreshOverviewBtn" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
                ${userInfo && userInfo.role === 'admin' ? `
                 <button id="resetOverviewBtn" class="btn btn-sm btn-outline-danger"> <!-- 改为danger强调 -->
                     <i class="fas fa-redo-alt"></i> 重置
                 </button>
                ` : ''} 
            </div>
        </div>
        <div class="overview-last-update">
            <small>最后更新: ${lastResetDate}</small>
        </div>
        
        <div class="overview-summary">
            <div class="row">
                <div class="col-md-6">
                    <div class="overview-card">
                        <h4>本月任务</h4>
                        <div class="overview-stats">
                            <div class="stat-item">
                                <span class="stat-value">${monthlyStats.total}</span>
                                <span class="stat-label">总任务数</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">${monthlyStats.completed}</span>
                                <span class="stat-label">已完成</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">${monthlyCompletionRate}%</span>
                                <span class="stat-label">完成率</span>
                            </div>
                        </div>
                        ${renderCategoryChart(monthlyStats.by_category, 'monthly-category-chart')}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="overview-card">
                        <h4>本年任务</h4>
                        <div class="overview-stats">
                            <div class="stat-item">
                                <span class="stat-value">${yearlyStats.total}</span>
                                <span class="stat-label">总任务数</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">${yearlyStats.completed}</span>
                                <span class="stat-label">已完成</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">${yearlyCompletionRate}%</span>
                                <span class="stat-label">完成率</span>
                            </div>
                        </div>
                        ${renderMonthlyProgressChart(yearlyStats.by_month, 'yearly-monthly-chart')}
                    </div>
                </div>
            </div>
            
            <div class="overview-designers mt-4">
                <h4>设计师任务分配</h4>
                <div class="row">
                    <div class="col-md-6">
                        ${renderDesignerStats(monthlyStats.by_designer, '本月')}
                    </div>
                    <div class="col-md-6">
                        ${renderDesignerStats(yearlyStats.by_designer, '本年')}
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 添加事件监听
    document.getElementById('refreshOverviewBtn')?.addEventListener('click', loadTaskOverviewFromAPI);
    // 条件绑定重置按钮事件
    if (userInfo && userInfo.role === 'admin') {
        document.getElementById('resetOverviewBtn')?.addEventListener('click', resetTaskOverview);
    }
    
    // 初始化图表
    renderCategoryCharts();
    renderMonthlyCharts();
}

// 渲染分类统计图表HTML
function renderCategoryChart(categoryData, chartId) {
    return `
        <div class="chart-container">
            <canvas id="${chartId}" width="100%" height="180"></canvas>
        </div>
    `;
}

// 渲染月度进度图表HTML
function renderMonthlyProgressChart(monthlyData, chartId) {
    return `
        <div class="chart-container">
            <canvas id="${chartId}" width="100%" height="180"></canvas>
        </div>
    `;
}

// 渲染设计师统计
function renderDesignerStats(designerData, period) {
    if (!designerData || Object.keys(designerData).length === 0) {
        return `<div class="no-data">${period}暂无设计师任务数据</div>`;
    }
    
    // 排序，按任务数量降序
    const sortedDesigners = Object.entries(designerData)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5); // 只显示前5名
    
    return `
        <div class="designer-stats">
            <h5>${period}设计师任务数量</h5>
            <ul class="designer-list">
                ${sortedDesigners.map(([designer, count], index) => `
                    <li class="designer-item">
                        <span class="designer-rank">${index + 1}</span>
                        <span class="designer-name">${designer}</span>
                        <span class="designer-count">${count}个任务</span>
                        <div class="designer-bar" style="width: ${Math.min(count * 8, 100)}%; background-color: ${getColorByIndex(index)}"></div>
                    </li>
                `).join('')}
            </ul>
        </div>
    `;
}

// 获取颜色（根据索引）
function getColorByIndex(index) {
    const colors = ['#4b6cb7', '#1e88e5', '#43a047', '#fb8c00', '#8e24aa', '#607d8b'];
    return colors[index % colors.length];
}

// 渲染分类图表
function renderCategoryCharts() {
    if (!window.Chart) {
        console.error('Chart.js 未加载，无法渲染图表');
        return;
    }
    
    const currentMonth = new Date().toISOString().slice(0, 7);
    const monthlyChartEl = document.getElementById('monthly-category-chart');
    
    if (monthlyChartEl && taskOverview) {
        const categoryData = taskOverview.monthly_stats[currentMonth]?.by_category || {};
        
        const categoryLabels = [];
        const categoryValues = [];
        const categoryColors = [];
        
        // 准备图表数据
        Object.entries(categoryData).forEach(([categoryId, count]) => {
            const category = taskCategories.find(c => c.id.toString() === categoryId);
            categoryLabels.push(category ? category.name : `类别${categoryId}`);
            categoryValues.push(count);
            categoryColors.push(category ? category.color : getColorByIndex(parseInt(categoryId)));
        });
        
        // 如果没有数据，显示默认数据
        if (categoryValues.length === 0) {
            categoryLabels.push('暂无数据');
            categoryValues.push(1);
            categoryColors.push('#cccccc');
        }
        
        new Chart(monthlyChartEl, {
            type: 'doughnut',
            data: {
                labels: categoryLabels,
                datasets: [{
                    data: categoryValues,
                    backgroundColor: categoryColors,
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            boxWidth: 12,
                            font: {
                                size: 11
                            }
                        }
                    },
                    title: {
                        display: true,
                        text: '本月任务类型分布',
                        font: {
                            size: 14
                        }
                    }
                }
            }
        });
    }
}

// 渲染月度图表
function renderMonthlyCharts() {
    if (!window.Chart) {
        console.error('Chart.js 未加载，无法渲染图表');
        return;
    }
    
    const currentYear = new Date().getFullYear().toString();
    const yearlyChartEl = document.getElementById('yearly-monthly-chart');
    
    if (yearlyChartEl && taskOverview) {
        const monthlyData = taskOverview.yearly_stats[currentYear]?.by_month || {};
        
        // 准备12个月的数据
        const months = Array.from({length: 12}, (_, i) => {
            const month = i + 1;
            const monthStr = month < 10 ? `0${month}` : `${month}`;
            return `${currentYear}-${monthStr}`;
        });
        
        const monthLabels = months.map(m => m.split('-')[1]+'月');
        const monthValues = months.map(month => monthlyData[month] || 0);
        
        new Chart(yearlyChartEl, {
            type: 'bar',
            data: {
                labels: monthLabels,
                datasets: [{
                    label: '任务数量',
                    data: monthValues,
                    backgroundColor: '#4b6cb7',
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: '本年任务月度分布',
                        font: {
                            size: 14
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
    }
}
  
    // 监听文件上传变化
    document.getElementById('taskAttachments')?.addEventListener('change', function(e) {
        const fileList = document.getElementById('attachmentsList');
        fileList.innerHTML = '';
        
        for (let i = 0; i < this.files.length; i++) {
            const fileName = this.files[i].name;
            fileList.innerHTML += `<div class="attachment-item"><i class="fas fa-file"></i> ${fileName}</div>`;
        }
    });
    
    // 监听参考图片上传变化，添加预览功能
    document.getElementById('taskReferenceAttachments')?.addEventListener('change', function(e) {
        const previewContainer = document.getElementById('referenceAttachmentsPreview');
        previewContainer.innerHTML = '';
        
        for (let i = 0; i < this.files.length; i++) {
            const file = this.files[i];
            if (!file.type.startsWith('image/')) continue;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewDiv = document.createElement('div');
                previewDiv.className = 'reference-preview-item';
                previewDiv.innerHTML = `
                    <img src="${e.target.result}" alt="${file.name}" class="reference-preview-img">
                    <div class="reference-preview-name">${file.name.length > 15 ? file.name.substring(0, 12) + '...' : file.name}</div>
                `;
                previewContainer.appendChild(previewDiv);
            };
            reader.readAsDataURL(file);
        }
    });
    
    // Lightbox for attachment preview
  
// 若是模块系统，需要导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { generateArtTaskManagement };
} else {
    // 确保函数在全局作用域可见
    window.generateArtTaskManagement = generateArtTaskManagement;
}
  