---
description: 
globs: 
alwaysApply: true
---
# 电商数据分析管理系统 - 项目总览

## 🎯 项目简介
这是一个基于 Flask + SQLite 的电商数据分析管理系统，主要用于管理和分析电商平台的销售数据、SPU数据、推广数据等。

## 🏗️ 项目架构

### 核心文件
- **[app.py](mdc:app.py)** - 主应用程序，包含所有 API 路由和业务逻辑（5683行）
- **[requirements.txt](mdc:requirements.txt)** - Python 依赖包管理
- **[package.json](mdc:package.json)** - 前端依赖管理

### 数据导入模块
- **[sale_data_importer.py](mdc:sale_data_importer.py)** - 销售数据导入工具
- **[spu_data_importer.py](mdc:spu_data_importer.py)** - SPU数据导入工具
- **[SALE_DATA_IMPORT_README.md](mdc:SALE_DATA_IMPORT_README.md)** - 销售数据导入说明文档
- **[SPU_DATA_IMPORT_README.md](mdc:SPU_DATA_IMPORT_README.md)** - SPU数据导入说明文档

### 目录结构
- **`utils/`** - 工具函数库
- **`routes/`** - 路由模块（目前为空）
- **`models/`** - 数据模型（目前为空）
- **`templates/`** - HTML模板文件
- **`static/`** - 静态资源（CSS、JS、数据文件）
- **`config/`** - 配置文件
- **`data/`** - 数据存储目录

## 🚀 主要功能模块

### 1. 用户权限管理
- 基于角色的权限控制（admin, yunying, art, caigou, caiwu, kefu）
- 用户账号信息存储在 [宜承账号.json](mdc:宜承账号.json)
- 登录验证和Cookie管理

### 2. 销售数据分析
- Excel文件批量导入到SQLite数据库
- 多维度销售数据分析和报表
- 店铺对比和趋势分析
- 日报表生成

### 3. SPU数据管理
- SPU（Standard Product Unit）数据导入和管理
- 多时间维度数据分析
- 同比环比数据对比

### 4. 推广数据分析
- 推广费用数据管理
- 广告效果分析
- 推广趋势报表

### 5. 违规数据管理
- 违规记录导入和查询
- 违规数据统计分析
- 违规趋势监控

### 6. 任务管理系统
- 美工任务创建和跟踪
- 任务状态管理
- 文件附件上传下载
- 任务评论系统

## 🛢️ 数据存储

### SQLite 数据库
- **sale_data.db** - 销售数据
- **spu_data.db** - SPU数据  
- **violation_data.db** - 违规数据

### JSON 配置文件
- **[宜承账号.json](mdc:宜承账号.json)** - 用户账号信息
- **[店铺-cookie.json](mdc:mcd:店铺-cookie.json)** - 店铺认证信息

### CSV 数据文件
- **[店铺-账号信息.csv](mdc:店铺-账号信息.csv)** - 店铺基础信息

## 🔧 技术栈
- **后端**: Flask + SQLite
- **前端**: HTML + JavaScript + CSS
- **数据处理**: Pandas + OpenPyXL
- **其他**: Flask-CORS（跨域支持）

## 📝 开发规范
- 所有 API 路由统一使用 `/api/` 前缀
- 管理员功能使用 `@admin_required` 装饰器保护
- 错误处理统一返回 JSON 格式响应
- 中文编码统一使用 UTF-8

